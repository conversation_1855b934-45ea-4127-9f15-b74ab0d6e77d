# 🏗️ Animalia System Architecture Guide

## 📋 Overview

This document provides comprehensive technical documentation for three core systems in the Animalia Pet Grooming application:

1. **🔄 Subscription Management System** - RevenueCat-powered subscription handling with trials
2. **📱 Messages Management System** - SMS automation with quota management  
3. **🎁 Referral System** - SMS credit rewards for user referrals

---

## 🔄 Subscription Management System

### 🎯 Purpose
Manages subscription tiers, trials, and feature access across the application with RevenueCat integration for cross-platform payments.

### 🏗️ Architecture Overview

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[Subscription UI]
        Provider[SubscriptionProvider]
        Guard[SubscriptionGuard]
    end
    
    subgraph "Service Layer"
        RCS[RevenueCatSubscriptionService]
        SLS[SubscriptionLimitService]
        SQS[SmsQuotaService]
    end
    
    subgraph "External Services"
        RC[RevenueCat SDK]
        AS[App Store]
        GP[Google Play]
        WEB[Web Payments]
    end
    
    subgraph "Backend"
        API[Spring Boot API]
        DB[(PostgreSQL)]
    end
    
    UI --> Provider
    Provider --> RCS
    Guard --> SLS
    RCS --> RC
    RC --> AS
    RC --> GP
    RC --> WEB
    RCS --> API
    API --> DB
    SLS --> SQS
```

### 📊 Subscription Tiers

| Tier | Price (Monthly) | Price (Annual) | Staff Limit | SMS Quota | Key Features |
|------|----------------|----------------|-------------|-----------|--------------|
| **Free** | €0 | €0 | 1 | 0 | Basic scheduling, drag & drop |
| **Freelancer** | €99 | €990 | 2 | 100 | SMS automation, team management |
| **Team** | €199 | €1990 | 10 | 200 | Advanced reports, recurring appointments |
| **Enterprise** | €499 | €4990 | Unlimited | 500 | Multi-location, custom branding |

### 🔄 Trial System Flow

```mermaid
sequenceDiagram
    participant U as User
    participant P as SubscriptionProvider
    participant R as RevenueCat
    participant B as Backend
    
    U->>P: Create Salon
    P->>R: Initialize Customer
    R->>B: Create Subscription Record
    B->>B: Set Trial (14 days)
    B-->>P: Trial Active
    P-->>U: Full Access Granted
    
    Note over U,B: Trial Period (14 days)
    
    B->>B: Trial Expires
    B->>P: Subscription Status Change
    P->>U: Show Upgrade Prompt
    
    U->>P: Purchase Subscription
    P->>R: Process Payment
    R->>B: Update Subscription
    B-->>P: Paid Subscription Active
```

### 🛡️ Feature Access Control

```dart
// Example: Checking if user can access advanced reports
bool canAccessAdvancedReports = await subscriptionProvider
    .canAccessFeature(salonId, 'advanced_reports');

if (!canAccessAdvancedReports) {
    // Show upgrade prompt
    SubscriptionPromptService.showUpgradePrompt(
        context: context,
        feature: 'advanced_reports',
        requiredTier: SubscriptionTier.team
    );
}
```

### 🔧 Key Components

#### SubscriptionProvider
- **Purpose**: Central state management for subscription data
- **Key Methods**:
  - `purchaseSubscription()` - Handle subscription purchases
  - `canAccessFeature()` - Check feature permissions
  - `getSubscriptionLimits()` - Get current tier limits

#### SubscriptionGuard
- **Purpose**: Protect features behind subscription walls
- **Usage**: Wrap widgets to enforce subscription requirements

```dart
SubscriptionGuard(
    feature: 'advanced_reports',
    requiredTier: SubscriptionTier.team,
    child: AdvancedReportsWidget(),
    fallback: UpgradePromptCard(),
)
```

### 💳 Payment Flow

```mermaid
graph LR
    subgraph "Mobile Platforms"
        M1[iOS App Store] 
        M2[Google Play Store]
    end
    
    subgraph "Web Platform"
        W1[Stripe Checkout]
        W2[PayPal]
    end
    
    subgraph "RevenueCat"
        RC[RevenueCat SDK]
        RCW[RevenueCat Web]
    end
    
    subgraph "Backend"
        WH[Webhooks]
        DB[(Database)]
    end
    
    M1 --> RC
    M2 --> RC
    W1 --> RCW
    W2 --> RCW
    RC --> WH
    RCW --> WH
    WH --> DB
```

---

## 📱 Messages Management System

### 🎯 Purpose
Automated SMS communication system with template management, quota tracking, and delivery optimization.

### 🏗️ Architecture Overview

```mermaid
graph TB
    subgraph "Frontend"
        SMS_UI[SMS Management UI]
        Templates[Template Editor]
        Logs[SMS Logs]
    end
    
    subgraph "Services"
        SMS_Service[SmsService]
        Template_Service[SmsTemplateService]
        Quota_Service[SmsQuotaService]
        Timing_Service[SmsReminderTimingService]
    end
    
    subgraph "Backend API"
        SMS_API[SMS Controller]
        Template_API[Template Controller]
        Queue[Message Queue]
    end
    
    subgraph "External"
        SMS_Provider[SMS Gateway]
        Scheduler[Cron Scheduler]
    end
    
    SMS_UI --> SMS_Service
    Templates --> Template_Service
    SMS_Service --> SMS_API
    Template_Service --> Template_API
    SMS_API --> Queue
    Queue --> SMS_Provider
    Scheduler --> Queue
    Quota_Service --> SMS_Service
```

### 📋 SMS Template System

#### Template Types & Default Content

```typescript
enum SmsTemplateType {
    APPOINTMENT_CONFIRMATION = "Confirmare programare",
    APPOINTMENT_CANCELLATION = "Anulare programare", 
    APPOINTMENT_RESCHEDULE = "Reprogramare",
    REMINDER = "Reminder cu câteva ore înainte",
    APPOINTMENT_COMPLETION = "Finalizare programare",
    FOLLOW_UP = "Mesaj de follow up"
}
```

#### Template Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `{SALON_NAME}` | Numele salonului | "Animalia Salon" |
| `{OWNER_NAME}` | Numele proprietarului | "Maria Popescu" |
| `{PET_NAME}` | Numele animalului | "Rex" |
| `{APPOINTMENT_DATE}` | Data programării | "25.12.2024" |
| `{APPOINTMENT_TIME}` | Ora programării | "14:30" |
| `{SALON_ADDRESS}` | Adresa salonului | "Str. Florilor nr. 15" |
| `{SALON_PHONE}` | Telefonul salonului | "0721 123 456" |
| `{SERVICE_NAME}` | Numele serviciului | "Tuns și spălat" |

### 📊 SMS Quota Management

```mermaid
graph LR
    subgraph "Quota Sources"
        S1[Subscription SMS]
        S2[Referral Bonus SMS]
        S3[Promotional SMS]
    end
    
    subgraph "Quota Tracking"
        QS[SmsQuotaService]
        DB[(Quota Database)]
    end
    
    subgraph "Usage"
        U1[Confirmation SMS]
        U2[Reminder SMS]
        U3[Follow-up SMS]
    end
    
    S1 --> QS
    S2 --> QS
    S3 --> QS
    QS --> DB
    QS --> U1
    QS --> U2
    QS --> U3
```

#### Quota Calculation Logic

```dart
class SmsQuotaService {
    static Future<int> getRemainingSms() async {
        final subscription = await getCurrentSubscription();
        final baseQuota = subscription.tier.smsQuota; // Monthly quota
        
        final referralBonus = await getReferralBonusSms();
        final promotionalBonus = await getPromotionalBonusSms();
        
        final totalQuota = baseQuota + referralBonus + promotionalBonus;
        final usedThisMonth = await getUsedSmsThisMonth();
        
        return (totalQuota - usedThisMonth).clamp(0, double.infinity).toInt();
    }
}
```

### ⏰ SMS Timing System

```mermaid
gantt
    title SMS Automation Timeline
    dateFormat X
    axisFormat %H:%M
    
    section Appointment Flow
    Booking Confirmation    :0, 1
    24h Reminder           :23, 24
    2h Reminder            :46, 47
    Appointment Time       :48, 49
    Completion SMS         :49, 50
    Follow-up (24h later)  :72, 73
```

#### Configurable Timing Options

| Timing Type | Default | Configurable Range | Purpose |
|-------------|---------|-------------------|---------|
| **Confirmation** | Immediate | N/A | Booking confirmation |
| **Reminder 1** | 24 hours before | 1-72 hours | Primary reminder |
| **Reminder 2** | 2 hours before | 30min-12 hours | Final reminder |
| **Follow-up** | 24 hours after | 1-168 hours | Customer satisfaction |

### 📈 SMS Analytics & Reporting

```mermaid
graph TB
    subgraph "SMS Metrics"
        M1[Delivery Rate]
        M2[Open Rate Estimation]
        M3[Response Rate]
        M4[Cost per SMS]
    end
    
    subgraph "Business Metrics"
        B1[No-show Reduction]
        B2[Customer Satisfaction]
        B3[Repeat Bookings]
        B4[ROI on SMS]
    end
    
    subgraph "Reports"
        R1[Daily SMS Report]
        R2[Monthly Usage Report]
        R3[Cost Analysis]
        R4[Performance Dashboard]
    end
    
    M1 --> R1
    M2 --> R2
    M3 --> R3
    M4 --> R4
    B1 --> R4
    B2 --> R4
    B3 --> R4
    B4 --> R4
```

---

## 🎁 Referral System

### 🎯 Purpose
Incentivize user growth through SMS credit rewards for successful referrals who purchase subscriptions.

### 🏗️ Architecture Overview

```mermaid
graph TB
    subgraph "Referral Flow"
        R1[Referrer Gets Code]
        R2[Shares Code]
        R3[Referee Claims Code]
        R4[Referee Purchases]
        R5[Both Get Rewards]
    end
    
    subgraph "Backend Logic"
        RG[Code Generation]
        RV[Code Validation]
        RT[Reward Tracking]
        RC[Reward Calculation]
    end
    
    subgraph "Database"
        RT_DB[(Referral Tracking)]
        RC_DB[(Reward Credits)]
        US_DB[(User Stats)]
    end
    
    R1 --> RG
    R3 --> RV
    R4 --> RT
    R5 --> RC
    RG --> RT_DB
    RV --> RT_DB
    RT --> RC_DB
    RC --> US_DB
```

### 🔢 Referral Code System

#### Code Generation Algorithm
```dart
class ReferralCodeGenerator {
    static String generateCode() {
        // 8-character alphanumeric code
        // Format: XXXX-XXXX (displayed with dash for readability)
        // Excludes confusing characters: 0, O, I, 1
        const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
        final random = Random.secure();
        
        return List.generate(8, (index) => 
            chars[random.nextInt(chars.length)]
        ).join();
    }
}
```

#### Code Validation Rules
- ✅ Exactly 8 characters
- ✅ Alphanumeric only (A-Z, 2-9)
- ✅ Case insensitive
- ✅ Must exist in database
- ✅ Must be from different salon
- ❌ Cannot use own referral code
- ❌ Can only claim one code per salon

### 💰 Reward Structure

```mermaid
graph LR
    subgraph "Referral Events"
        E1[Code Claimed]
        E2[First Purchase]
        E3[Subscription Renewal]
    end
    
    subgraph "Rewards"
        R1[100 SMS Credits]
        R2[200 SMS Credits]
        R3[50 SMS Credits]
    end
    
    subgraph "Conditions"
        C1[Immediate]
        C2[Within 30 days]
        C3[Each renewal]
    end
    
    E1 --> R1
    E2 --> R2
    E3 --> R3
    R1 --> C1
    R2 --> C2
    R3 --> C3
```

#### Reward Calculation Logic

```dart
class ReferralRewardCalculator {
    static Future<int> calculateReward(ReferralEvent event) async {
        switch (event.type) {
            case ReferralEventType.CODE_CLAIMED:
                return 100; // Immediate SMS credits
                
            case ReferralEventType.FIRST_PURCHASE:
                final tier = event.subscriptionTier;
                return switch (tier) {
                    SubscriptionTier.freelancer => 200,
                    SubscriptionTier.team => 300,
                    SubscriptionTier.enterprise => 500,
                    _ => 100
                };
                
            case ReferralEventType.SUBSCRIPTION_RENEWAL:
                return 50; // Recurring reward
        }
    }
}
```

### 📊 Referral Tracking & Analytics

```mermaid
graph TB
    subgraph "Tracking Metrics"
        T1[Referral Codes Generated]
        T2[Codes Claimed]
        T3[Successful Conversions]
        T4[Total Rewards Distributed]
    end
    
    subgraph "Performance KPIs"
        K1[Conversion Rate]
        K2[Average Time to Purchase]
        K3[Referrer Retention]
        K4[Cost per Acquisition]
    end
    
    subgraph "Reports"
        RP1[Referral Dashboard]
        RP2[Individual Progress]
        RP3[System Performance]
        RP4[ROI Analysis]
    end
    
    T1 --> K1
    T2 --> K1
    T3 --> K2
    T4 --> K4
    K1 --> RP1
    K2 --> RP2
    K3 --> RP3
    K4 --> RP4
```

### 🔄 Referral Flow Sequence

```mermaid
sequenceDiagram
    participant R as Referrer
    participant S as System
    participant N as New User
    participant P as Payment
    
    R->>S: Request referral code
    S->>S: Generate unique code
    S-->>R: Return code (ABC12345)
    
    R->>N: Share referral code
    N->>S: Claim referral code
    S->>S: Validate code
    S->>S: Award 100 SMS credits to referrer
    S-->>N: Code claimed successfully
    
    N->>S: Create salon & start trial
    S->>S: Track referral relationship
    
    Note over N,P: Trial period (14 days)
    
    N->>P: Purchase subscription
    P->>S: Payment confirmed
    S->>S: Award bonus SMS credits
    S-->>R: Referral conversion bonus
    S-->>N: Welcome to paid plan
```

### 🛡️ Anti-Fraud Measures

#### Validation Rules
```dart
class ReferralValidator {
    static Future<ValidationResult> validateClaim(
        String code, 
        String claimingSalonId
    ) async {
        // 1. Code format validation
        if (!isValidFormat(code)) {
            return ValidationResult.invalid('Invalid code format');
        }
        
        // 2. Code exists and is active
        final referralCode = await findReferralCode(code);
        if (referralCode == null) {
            return ValidationResult.invalid('Code not found');
        }
        
        // 3. Cannot claim own code
        if (referralCode.salonId == claimingSalonId) {
            return ValidationResult.invalid('Cannot use own referral code');
        }
        
        // 4. One claim per salon
        final hasClaimedBefore = await hasClaimedAnyCode(claimingSalonId);
        if (hasClaimedBefore) {
            return ValidationResult.invalid('Already claimed a referral code');
        }
        
        // 5. Rate limiting
        if (await isRateLimited(claimingSalonId)) {
            return ValidationResult.invalid('Too many attempts');
        }
        
        return ValidationResult.valid();
    }
}
```

#### Security Features
- 🔒 **Rate Limiting**: Max 5 attempts per hour per IP
- 🔒 **One-time Claims**: Each salon can only claim one referral code
- 🔒 **Self-referral Prevention**: Cannot use own referral code
- 🔒 **Audit Trail**: All referral activities logged
- 🔒 **Fraud Detection**: Suspicious pattern monitoring

---

## 🔗 System Integration

### 📊 Cross-System Dependencies

```mermaid
graph TB
    subgraph "Subscription System"
        SS[Subscription Status]
        ST[Subscription Tier]
        SL[Subscription Limits]
    end
    
    subgraph "SMS System"
        SQ[SMS Quota]
        ST_SMS[SMS Templates]
        SH[SMS History]
    end
    
    subgraph "Referral System"
        RC[Referral Credits]
        RR[Referral Rewards]
        RT[Referral Tracking]
    end
    
    SS --> SQ
    ST --> SQ
    RR --> SQ
    RC --> SQ
    SL --> ST_SMS
    RT --> RR
```

### 🔄 Event-Driven Architecture

```mermaid
sequenceDiagram
    participant U as User Action
    participant E as Event Bus
    participant S1 as Subscription Service
    participant S2 as SMS Service
    participant S3 as Referral Service
    
    U->>E: Subscription Purchased
    E->>S1: Update subscription status
    E->>S2: Refresh SMS quota
    E->>S3: Process referral rewards
    
    U->>E: Referral Code Claimed
    E->>S3: Award referral credits
    E->>S2: Update SMS balance
    
    U->>E: SMS Sent
    E->>S2: Decrement quota
    E->>S1: Check limits
```

### 📈 Performance Optimizations

#### Caching Strategy
```dart
class SystemCache {
    // Subscription data - 5 minute cache
    static final subscriptionCache = LRUCache<String, SalonSubscription>(
        maxSize: 1000,
        expiry: Duration(minutes: 5)
    );
    
    // SMS quota - 1 minute cache (frequent updates)
    static final smsQuotaCache = LRUCache<String, int>(
        maxSize: 5000,
        expiry: Duration(minutes: 1)
    );
    
    // Referral codes - 1 hour cache (rarely change)
    static final referralCodeCache = LRUCache<String, ReferralCode>(
        maxSize: 10000,
        expiry: Duration(hours: 1)
    );
}
```

#### Database Optimization
- **Indexes**: Optimized queries for subscription lookups
- **Partitioning**: SMS logs partitioned by month
- **Connection Pooling**: Efficient database connections
- **Read Replicas**: Separate read/write operations

---

## 🧪 Testing Strategy

### 🔬 Unit Testing

```dart
// Example: Subscription tier validation
testWidgets('should show upgrade prompt for premium features', (tester) async {
    // Arrange
    final mockProvider = MockSubscriptionProvider();
    when(mockProvider.currentTier).thenReturn(SubscriptionTier.free);
    
    // Act
    await tester.pumpWidget(
        ProviderScope(
            overrides: [subscriptionProvider.overrideWith((_) => mockProvider)],
            child: AdvancedReportsScreen()
        )
    );
    
    // Assert
    expect(find.byType(UpgradePromptCard), findsOneWidget);
    expect(find.text('Upgrade to Team'), findsOneWidget);
});
```

### 🔄 Integration Testing

```dart
// Example: End-to-end referral flow
testWidgets('complete referral flow integration', (tester) async {
    // 1. Generate referral code
    final referralCode = await ReferralService.generateCode(salonId: 'salon1');
    expect(referralCode, hasLength(8));
    
    // 2. Claim referral code
    final claimResult = await ReferralService.claimCode(
        code: referralCode,
        claimingSalonId: 'salon2'
    );
    expect(claimResult.success, isTrue);
    
    // 3. Verify SMS credits awarded
    final smsBalance = await SmsQuotaService.getRemainingSms();
    expect(smsBalance, greaterThanOrEqualTo(100));
});
```

### 📊 Performance Testing

```dart
// Example: SMS quota performance under load
test('SMS quota service handles concurrent requests', () async {
    final futures = List.generate(100, (index) => 
        SmsQuotaService.decrementSmsCount()
    );
    
    await Future.wait(futures);
    
    final finalBalance = await SmsQuotaService.getRemainingSms();
    expect(finalBalance, equals(initialBalance - 100));
});
```

---

## 🚀 Deployment & Monitoring

### 📊 Key Metrics to Monitor

#### Subscription Metrics
- **Conversion Rate**: Trial → Paid subscription
- **Churn Rate**: Monthly subscription cancellations  
- **ARPU**: Average Revenue Per User
- **LTV**: Customer Lifetime Value

#### SMS Metrics
- **Delivery Rate**: Successfully delivered SMS
- **Quota Utilization**: SMS usage vs. allocation
- **Cost per SMS**: Average cost across providers
- **Response Rate**: Customer engagement with SMS

#### Referral Metrics
- **Referral Conversion**: Code claims → Purchases
- **Viral Coefficient**: New users per existing user
- **Reward Cost**: SMS credits distributed
- **Fraud Rate**: Invalid referral attempts

### 🔍 Monitoring Setup

```yaml
# Example: Prometheus metrics
subscription_conversions_total:
  type: counter
  help: "Total subscription conversions"
  labels: [tier, source]

sms_quota_remaining:
  type: gauge  
  help: "Remaining SMS quota per salon"
  labels: [salon_id, tier]

referral_rewards_distributed:
  type: counter
  help: "Total referral rewards distributed"
  labels: [reward_type, amount]
```

### 🚨 Alerting Rules

```yaml
# Critical alerts
- alert: SubscriptionServiceDown
  expr: up{job="subscription-service"} == 0
  for: 1m
  
- alert: SMSQuotaExhausted  
  expr: sms_quota_remaining < 10
  for: 5m
  
- alert: ReferralFraudSpike
  expr: rate(referral_fraud_attempts[5m]) > 0.1
  for: 2m
```

---

## 🔧 Troubleshooting Guide

### Common Issues & Solutions

#### Subscription Issues
**Problem**: Trial not activating after salon creation
```dart
// Solution: Check RevenueCat customer initialization
await RevenueCatSubscriptionService.setUserId(userId);
await RevenueCatSubscriptionService.syncSubscriptionStatus();
```

**Problem**: Feature access denied despite valid subscription
```dart
// Solution: Clear subscription cache and refresh
SubscriptionLimitService.clearCache(salonId);
await subscriptionProvider.refreshForSalon(salonId);
```

#### SMS Issues
**Problem**: SMS not sending despite available quota
```dart
// Solution: Check SMS service configuration
final config = await SmsService.validateConfiguration();
if (!config.isValid) {
    await SmsService.reconfigureProvider();
}
```

**Problem**: Quota not updating after SMS sent
```dart
// Solution: Force quota refresh
await SmsQuotaService.refreshAfterSubscriptionChange();
await SmsQuotaService.syncWithBackend();
```

#### Referral Issues
**Problem**: Referral code not generating
```dart
// Solution: Check salon eligibility and retry
final canGenerate = await ReferralService.canGenerateCode(salonId);
if (canGenerate) {
    final code = await ReferralService.regenerateCode(salonId);
}
```

**Problem**: Rewards not being distributed
```dart
// Solution: Manually trigger reward calculation
await ReferralService.recalculateRewards(referralId);
await SmsQuotaService.refreshAfterReferralReward();
```

---

## 📚 API Reference

### Subscription Endpoints

```typescript
// Get current subscription
GET /api/subscription/{salonId}/current
Response: SalonSubscription

// Purchase subscription  
POST /api/subscription/{salonId}/purchase
Body: { packageId: string, platform: string }
Response: { success: boolean, subscription: SalonSubscription }

// Check feature access
GET /api/subscription/{salonId}/features/{feature}
Response: { hasAccess: boolean, requiredTier: string }
```

### SMS Endpoints

```typescript
// Send SMS
POST /api/sms/send
Body: { phoneNumber: string, type: string, appointmentId: string }
Response: { success: boolean, messageId: string, cost: number }

// Get SMS quota
GET /api/sms/quota/{salonId}
Response: { remaining: number, total: number, resetDate: string }

// Update SMS template
PUT /api/sms/templates/{templateId}
Body: { content: string, isActive: boolean }
Response: { success: boolean, template: SmsTemplate }
```

### Referral Endpoints

```typescript
// Get referral code
GET /api/referral/{salonId}/code
Response: { code: string, generatedAt: string }

// Claim referral code
POST /api/referral/{salonId}/claim
Body: { code: string }
Response: { success: boolean, smsCreditsAwarded: number }

// Get referral stats
GET /api/referral/{salonId}/stats
Response: { totalReferred: number, totalRewards: number, conversionRate: number }
```

---

## 🎯 Best Practices

### 🔒 Security
- Always validate subscription status server-side
- Implement rate limiting for referral claims
- Encrypt sensitive SMS content in transit
- Use secure random generation for referral codes

### ⚡ Performance  
- Cache subscription data with appropriate TTL
- Batch SMS operations when possible
- Use database indexes for referral lookups
- Implement circuit breakers for external services

### 🧪 Testing
- Mock external services (RevenueCat, SMS providers)
- Test subscription edge cases (expired trials, failed payments)
- Validate SMS template rendering with various data
- Test referral fraud prevention mechanisms

### 📊 Monitoring
- Track business metrics alongside technical metrics
- Set up alerts for critical subscription events
- Monitor SMS delivery rates and costs
- Track referral conversion funnels

---

This comprehensive guide provides the technical foundation for understanding and maintaining Animalia's core systems. Each system is designed to work independently while integrating seamlessly to provide a cohesive user experience.