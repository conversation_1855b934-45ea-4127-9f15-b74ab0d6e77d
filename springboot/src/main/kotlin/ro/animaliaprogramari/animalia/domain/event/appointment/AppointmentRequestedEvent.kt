package ro.animaliaprogramari.animalia.domain.event.appointment

import ro.animaliaprogramari.animalia.domain.event.DomainEvent
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

/**
 * Event fired when an appointment is requested (online booking with manual approval)
 * This triggers notifications to salon staff to approve or reject the appointment
 */
data class AppointmentRequestedEvent(
    override val eventId: String,
    override val occurredAt: LocalDateTime,
    override val aggregateId: String,
    val appointmentId: AppointmentId,
    val clientId: ClientId,
    val petId: PetId,
    val staffId: StaffId,
    val salonId: SalonId,
    val appointmentDate: LocalDate,
    val startTime: LocalTime,
    val endTime: LocalTime,
    val serviceIds: List<ServiceId>,
    val clientPhone: String,
    val clientName: String?,
    val petName: String,
) : DomainEvent

