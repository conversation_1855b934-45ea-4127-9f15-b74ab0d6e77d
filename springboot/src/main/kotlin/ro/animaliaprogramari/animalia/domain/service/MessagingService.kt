package ro.animaliaprogramari.animalia.domain.service

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsMessageType
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsStatus
import ro.animaliaprogramari.animalia.application.port.outbound.*
import ro.animaliaprogramari.animalia.application.service.SmsLogService
import ro.animaliaprogramari.animalia.application.service.StripeSmsBillingService
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.port.outbound.SmsTemplateRepository
import java.time.LocalDate
import java.time.LocalTime
import java.time.format.DateTimeFormatter

/**
 * Service for creating and sending personalized SMS notifications for appointments.
 * Handles data retrieval, message templating, and SMS delivery with proper error handling.
 */
@Service
class MessagingService(
    private val clientRepository: ClientRepository,
    private val salonRepository: SalonRepository,
    private val petRepository: PetRepository,
    private val appointmentRepository: AppointmentRepository,
    private val messagingStrategyService: MessagingStrategyService,
    private val smsQuotaService: SmsQuotaService,
    private val smsTemplateRepository: SmsTemplateRepository,
    private val smsLogService: SmsLogService,
    private val smsLengthCalculator: SmsLengthCalculator,
    private val stripeSmsBillingService: StripeSmsBillingService,
    private val smsReminderSettingsRepository: SmsReminderSettingsRepository,
) {
    private val logger = LoggerFactory.getLogger(MessagingService::class.java)

    // Romanian date and time formatters
    private val dateFormatter = DateTimeFormatter.ofPattern("dd.MM.yyyy")
    private val timeFormatter = DateTimeFormatter.ofPattern("HH:mm")

    /**
     * Send appointment scheduled notification
     */
    fun sendAppointmentScheduledNotification(
        clientId: ClientId,
        petId: PetId?,
        appointmentId: AppointmentId?,
        appointmentDate: LocalDate,
        startTime: LocalTime,
    ) {
        logger.info("📧 Sending appointment scheduled notification - Client: ${clientId.value}, Appointment: ${appointmentId?.value}")

        val messageContext = buildMessageContext(clientId, petId, appointmentId)
        val salonId = messageContext.salonId

        logger.info("🏢 Salon ID for template lookup: ${salonId?.value ?: "null"}")

        val message = buildAppointmentScheduledMessage(messageContext, appointmentDate, startTime)

        logger.info("📱 Final SMS message: $message")

        sendMessageToClient(
            clientId,
            message,
            salonId,
            SmsMessageType.APPOINTMENT_CONFIRMATION,
            appointmentId,
            messageContext
        )
    }



    /**
     * Send appointment cancelled notification
     */
    fun sendAppointmentCancelledNotification(
        clientId: ClientId,
        petId: PetId?,
        appointmentId: AppointmentId?,
        appointmentDate: LocalDate,
    ) {
        val messageContext = buildMessageContext(clientId, petId, appointmentId)
        val message = buildAppointmentCancelledMessage(messageContext, appointmentDate)
        val salonId = getSalonIdFromContext(appointmentId)
        sendMessageToClient(
            clientId = clientId,
            message = message,
            salonId = salonId,
            messageType = SmsMessageType.APPOINTMENT_CANCELLATION,
            appointmentId = appointmentId,
            messageContext = messageContext,
        )
    }

    /**
     * Send appointment rescheduled notification
     */
    fun sendAppointmentRescheduledNotification(
        clientId: ClientId,
        petId: PetId?,
        appointmentId: AppointmentId?,
        oldDate: LocalDate,
        newDate: LocalDate,
        newStartTime: LocalTime,
    ) {
        val messageContext = buildMessageContext(clientId, petId, appointmentId)
        val message = buildAppointmentRescheduledMessage(messageContext, oldDate, newDate, newStartTime)
        val salonId = getSalonIdFromContext(appointmentId)
        sendMessageToClient(
            clientId = clientId,
            message = message,
            salonId = salonId,
            messageType = SmsMessageType.APPOINTMENT_RESCHEDULE,
            appointmentId = appointmentId,
            messageContext = messageContext,
        )
    }

    /**
     * Send appointment deleted notification
     */
    fun sendAppointmentDeletedNotification(
        clientId: ClientId,
        petId: PetId?,
        appointmentId: AppointmentId?,
        appointmentDate: LocalDate,
    ) {
        val messageContext = buildMessageContext(clientId, petId, appointmentId)
        val message = buildAppointmentDeletedMessage(messageContext, appointmentDate)
        val salonId = getSalonIdFromContext(appointmentId)
        sendMessageToClient(
            clientId = clientId,
            message = message,
            salonId = salonId,
            messageType = SmsMessageType.APPOINTMENT_CANCELLATION,
            appointmentId = appointmentId,
            messageContext = messageContext,
        )
    }

    /**
     * Send appointment reminder notification
     */
    fun sendAppointmentReminderNotification(
        clientId: ClientId,
        petId: PetId?,
        appointmentId: AppointmentId?,
        appointmentDate: LocalDate,
        startTime: LocalTime?,
    ) {
        val messageContext = buildMessageContext(clientId, petId, appointmentId)
        val message = buildAppointmentReminderMessage(messageContext, appointmentDate, startTime)
        val salonId = getSalonIdFromContext(appointmentId)
        val messageType = SmsMessageType.REMINDER
        sendMessageToClient(clientId, message, salonId, messageType, appointmentId, messageContext)
    }

    /**
     * Sanitize client name - treat phone number format names as empty
     */
    private fun sanitizeClientName(clientName: String?): String? {
        if (clientName == null) return null

        // If client name is in format "client-+40..." treat it as empty
        if (clientName.startsWith("client-+")) {
            logger.info("🧹 Sanitizing client name: '$clientName' -> null (phone number format)")
            return null
        }

        return clientName
    }

    /**
     * Build message context by gathering all necessary data
     */
    private fun buildMessageContext(
        clientId: ClientId,
        petId: PetId?,
        appointmentId: AppointmentId?,
        ): MessageContext {
        logger.info("🔍 [RACE_CONDITION_DEBUG] Building message context - Client: ${clientId.value}, Pet: ${petId?.value}, Appointment: ${appointmentId?.value}")

        val client = try {
            val result = clientRepository.findById(clientId)
            logger.info("👤 [RACE_CONDITION_DEBUG] Client lookup result: ${if (result != null) "FOUND (${result.name})" else "NOT_FOUND"}")
            result
        } catch (e: Exception) {
            logger.warn("Failed to retrieve client ${clientId.value}: ${e.message}")
            null
        }

        val pet = petId?.let {
            try {
                val result = petRepository.findById(it)
                logger.info("🐕 [RACE_CONDITION_DEBUG] Pet lookup result: ${if (result != null) "FOUND (${result.name})" else "NOT_FOUND"}")
                result
            } catch (e: Exception) {
                logger.warn("Failed to retrieve pet ${it.value}: ${e.message}")
                null
            }
        }

        val appointment = appointmentId?.let {
            try {
                val result = appointmentRepository.findById(it)
                logger.info("📅 [RACE_CONDITION_DEBUG] Appointment lookup result: ${if (result != null) "FOUND (salon: ${result.salonId.value})" else "NOT_FOUND"}")
                result
            } catch (e: Exception) {
                logger.warn("Failed to retrieve appointment ${it.value}: ${e.message}")
                null
            }
        }

        val salon = appointment?.salonId?.let {
            try {
                val result = salonRepository.findById(it)
                logger.info("🏢 [RACE_CONDITION_DEBUG] Salon lookup result: ${if (result != null) "FOUND (${result.name})" else "NOT_FOUND"}")
                result
            } catch (e: Exception) {
                logger.warn("Failed to retrieve salon ${it.value}: ${e.message}")
                null
            }
        }

        val context = MessageContext(
            clientName = sanitizeClientName(client?.name),
            petName = pet?.name,
            salonName = salon?.name,
            salonAddress = salon?.address,
            salonPhone = salon?.phone?.toInternationalFormat(),
            clientPhone = client?.phone,
            salonId = salon?.id,
            appointmentDate = appointment?.appointmentDate?.format(dateFormatter),
            appointmentTime = appointment?.startTime?.format(timeFormatter),
            googleLink = salon?.googleReviewLink
        )

        logger.info("✅ [RACE_CONDITION_DEBUG] Final context - SalonId: ${context.salonId?.value ?: "NULL"}, HasAllInfo: ${context.hasAllInfo()}")
        return context
    }

    /**
     * Build appointment scheduled message using dynamic template
     */
    private fun buildAppointmentScheduledMessage(
        context: MessageContext,
        appointmentDate: LocalDate,
        startTime: LocalTime,
    ): String {
        val template = getTemplateContent(context.salonId, SmsTemplateType.APPOINTMENT_CONFIRMATION)

        return populateTemplate(template, mapOf(
            "SALON_NAME" to (context.salonName ?: "Salonul nostru"),
            "OWNER_NAME" to (context.clientName ?: "Stimate client"),
            "PET_NAME" to (context.petName ?: "animalul dumneavoastră"),
            "APPOINTMENT_DATE" to appointmentDate.format(dateFormatter),
            "APPOINTMENT_TIME" to startTime.format(timeFormatter),
            "SALON_ADDRESS" to (context.salonAddress ?: "salonul nostru"),
            "SALON_PHONE" to (context.salonPhone ?: "0721 000 000")
        ))
    }

    /**
     * Build appointment cancelled message using dynamic template
     */
    private fun buildAppointmentCancelledMessage(
        context: MessageContext,
        appointmentDate: LocalDate,
    ): String {
        val template = getTemplateContent(context.salonId, SmsTemplateType.APPOINTMENT_CANCELLATION)

        return populateTemplate(template, mapOf(
            "SALON_NAME" to (context.salonName ?: "Salonul nostru"),
            "OWNER_NAME" to (context.clientName ?: "Stimate client"),
            "PET_NAME" to (context.petName ?: "animalul dumneavoastră"),
            "APPOINTMENT_DATE" to appointmentDate.format(dateFormatter),
            "SALON_ADDRESS" to (context.salonAddress ?: "salonul nostru"),
            "SALON_PHONE" to (context.salonPhone ?: "0721 000 000")
        ))
    }

    /**
     * Build appointment rescheduled message using dynamic template
     */
    private fun buildAppointmentRescheduledMessage(
        context: MessageContext,
        oldDate: LocalDate,
        newDate: LocalDate,
        newStartTime: LocalTime,
    ): String {
        val template = getTemplateContent(context.salonId, SmsTemplateType.APPOINTMENT_RESCHEDULE)

        logger.info("🔧 [RESCHEDULE_DEBUG] Building reschedule message with template: $template")
        logger.info("🔧 [RESCHEDULE_DEBUG] New date: ${newDate.format(dateFormatter)}, New time: ${newStartTime.format(timeFormatter)}")

        val variables = mapOf(
            "SALON_NAME" to (context.salonName ?: "Salonul nostru"),
            "OWNER_NAME" to (context.clientName ?: "Stimate client"),
            "PET_NAME" to (context.petName ?: "animalul dumneavoastră"),
            "OLD_DATE" to oldDate.format(dateFormatter),
            "NEW_DATE" to newDate.format(dateFormatter),
            "APPOINTMENT_DATE" to newDate.format(dateFormatter),
            "NEW_TIME" to newStartTime.format(timeFormatter),
            "APPOINTMENT_TIME" to newStartTime.format(timeFormatter),
            "SALON_ADDRESS" to (context.salonAddress ?: "salonul nostru"),
            "SALON_PHONE" to (context.salonPhone ?: "0721 000 000")
        )

        val result = populateTemplate(template, variables)
        logger.info("🔧 [RESCHEDULE_DEBUG] Final reschedule message: $result")

        return result
    }

    /**
     * Build appointment deleted message
     */
    private fun buildAppointmentDeletedMessage(
        context: MessageContext,
        appointmentDate: LocalDate,
    ): String {
        val template = getTemplateContent(context.salonId, SmsTemplateType.APPOINTMENT_CANCELLATION)

        return populateTemplate(template, mapOf(
            "SALON_NAME" to (context.salonName ?: "Salonul nostru"),
            "OWNER_NAME" to (context.clientName ?: "Stimate client"),
            "PET_NAME" to (context.petName ?: "animalul dumneavoastră"),
            "APPOINTMENT_DATE" to appointmentDate.format(dateFormatter),
            "SALON_ADDRESS" to (context.salonAddress ?: "salonul nostru"),
            "SALON_PHONE" to (context.salonPhone ?: "0721 000 000")
        ))
    }

    /**
     * Build appointment reminder message using dynamic template with precise timing
     */
    private fun buildAppointmentReminderMessage(
        context: MessageContext,
        appointmentDate: LocalDate,
        startTime: LocalTime?,
    ): String {
        val template = getTemplateContent(context.salonId, SmsTemplateType.REMINDER)

        return populateTemplate(template, mapOf(
            "SALON_NAME" to (context.salonName ?: "Salonul nostru"),
            "OWNER_NAME" to (context.clientName ?: "Stimate client"),
            "PET_NAME" to (context.petName ?: "animalul dumneavoastră"),
            "APPOINTMENT_DATE" to appointmentDate.format(dateFormatter),
            "APPOINTMENT_TIME" to (startTime?.format(timeFormatter) ?: "ora programată"),
            "SALON_ADDRESS" to (context.salonAddress ?: "salonul nostru"),
            "SALON_PHONE" to (context.salonPhone ?: "0721 000 000")
        ))
    }

    /**
     * Get salon ID from context or appointment
     */
    private fun getSalonIdFromContext(appointmentId: AppointmentId?): SalonId? {
        // Try to get salon ID from appointment if available
        return appointmentId?.let {
            try {
                appointmentRepository.findById(it)?.salonId
            } catch (e: Exception) {
                logger.warn("Failed to retrieve appointment ${it.value} for salon ID: ${e.message}")
                null
            }
        }
    }



    /**
     * Send SMS to client with proper error handling and length-based quota tracking
     * Public for unit testing
     */
    fun sendMessageToClient(
        clientId: ClientId,
        message: String,
        salonId: SalonId?,
        messageType: SmsMessageType,
        appointmentId: AppointmentId? = null,
        messageContext: MessageContext?
    ) {
        require(salonId != null) { "Salon ID is required to send SMS" }

        // Calculate SMS units required based on message length
        val smsInfo = smsLengthCalculator.getSmsInfo(message)
        val smsUnitsRequired = smsInfo.smsUnits
        logger.info("📊 SMS Analysis: ${smsInfo.getCostDescription()} - Encoding: ${smsInfo.encoding.displayName}")

        // Check SMS quota and compute overage if any
        val remainingQuota = smsQuotaService.getRemainingQuota(salonId)
        var unitsWithinQuota = smsUnitsRequired
        var unitsOverage = 0

        if (remainingQuota < smsUnitsRequired) {
            // Determine if we can allow overage via Stripe metered billing
            val stripeCtx = stripeSmsBillingService.getStripeUsageContext(salonId)
            val allowOverage = stripeCtx != null
            if (allowOverage) {
                unitsWithinQuota = maxOf(0, remainingQuota)
                unitsOverage = smsUnitsRequired - unitsWithinQuota
                logger.info("Allowing SMS overage for salon ${salonId.value}: within=$unitsWithinQuota, overage=$unitsOverage")
            } else {
                logger.warn("Insufficient SMS quota for salon ${salonId.value}. Required: $smsUnitsRequired, Available: $remainingQuota")
                return
            }
        }

        // Send SMS and handle quota recording
        val client = clientRepository.findById(clientId)
        val phone = client?.phone?.toInternationalFormat()
        if (phone == null) {
            logger.warn("No phone number found for client ${clientId.value}")
            return
        }

        try {
            // Send message using MessagingStrategyService with salon's preferred channel (WhatsApp/SMS)
            val sendResult = messagingStrategyService.sendMessage(phone, message, salonId, messageType, messageContext)

            // Determine what to log based on the channel used
            val messageToLog = if (sendResult.channelType == MessagingChannelType.WHATSAPP) {
                // For WhatsApp, get the template text and replace variables with actual values
                val templateText = sendResult.templateId ?: "Template not available"
                buildWhatsAppLogMessage(templateText, messageContext)
            } else {
                message // For SMS, log the actual message content
            }

            // Log message after successful delivery (both SMS and WhatsApp)
            logSmsDelivery(salonId, phone, messageToLog, messageType, appointmentId, clientId, client.name, SmsStatus.SENT)

            // Record SMS usage
            recordSmsUsage(salonId, unitsWithinQuota, unitsOverage)

        } catch (ex: Exception) {
            logger.error("Failed to send message to client ${clientId.value}", ex)
            throw ex
        }
    }

    /**
     * Build a WhatsApp log message by replacing template variables with actual values.
     * Takes the WhatsApp template text and populates it with real data.
     */
    private fun buildWhatsAppLogMessage(
        templateText: String,
        context: MessageContext?
    ): String {
        if (context == null) {
            return "[WhatsApp] $templateText"
        }

        var result = templateText

        // Replace all variables in the template with actual values
        context.clientName?.let { result = result.replace("{{OWNER_NAME}}", it) }
        context.petName?.let { result = result.replace("{{PET_NAME}}", it) }
        context.salonName?.let { result = result.replace("{{SALON_NAME}}", it) }
        context.salonAddress?.let { result = result.replace("{{SALON_ADDRESS}}", it) }
        context.salonPhone?.let { result = result.replace("{{SALON_PHONE}}", it) }
        context.appointmentDate?.let { result = result.replace("{{APPOINTMENT_DATE}}", it) }
        context.appointmentTime?.let { result = result.replace("{{APPOINTMENT_TIME}}", it) }

        // Add [WhatsApp] prefix to distinguish from SMS
        return "[WhatsApp] $result"
    }

    /**
     * Log SMS delivery
     */
    private fun logSmsDelivery(
        salonId: SalonId,
        phone: String,
        message: String,
        messageType: SmsMessageType,
        appointmentId: AppointmentId?,
        clientId: ClientId,
        clientName: String,
        status: SmsStatus
    ) {
        try {
            val appointment = appointmentId?.let { appointmentRepository.findById(it) }
            smsLogService.logSms(
                salonId = salonId.value,
                phoneNumber = phone,
                messageContent = message,
                messageType = messageType,
                appointmentId = appointmentId?.value,
                clientId = clientId.value,
                clientName = clientName,
                petName = appointment?.petId?.let { petRepository.findById(it) }?.name,
                appointmentDate = appointment?.appointmentDate,
                appointmentTime = appointment?.startTime,
                status = status
            )
            logger.info("Message logged successfully for client ${clientId.value} with status: $status")
        } catch (e: Exception) {
            logger.error("Failed to log message for client ${clientId.value}", e)
            // Continue even if logging fails
        }
    }

    /**
     * Record SMS usage
     */
    private fun recordSmsUsage(salonId: SalonId, unitsWithinQuota: Int, unitsOverage: Int) {
        try {
            // Record regular quota units if any
            if (unitsWithinQuota > 0) {
                smsQuotaService.recordMultipleSmsUnits(salonId, unitsWithinQuota)
                logger.info("SMS quota updated for salon ${salonId.value}. Units recorded: $unitsWithinQuota")
            }

            // Report overage to Stripe if any
            if (unitsOverage > 0) {
                stripeSmsBillingService.reportSmsUsage(salonId, unitsOverage)
                logger.info("Reported $unitsOverage SMS overage units to Stripe for salon ${salonId.value}")
            }
        } catch (e: Exception) {
            logger.error("Failed to update SMS quota or report usage for salon ${salonId.value}. SMS was sent but quota/usage update failed!", e)
            // This is a critical error - SMS was sent but quota/usage may be inconsistent
            // In production, this should trigger an alert or compensation mechanism
        }
    }

    /**
     * Data class to hold message context information
     */
    data class MessageContext(
        val clientName: String?,
        val petName: String?,
        val salonName: String?,
        val salonAddress: String?,
        val salonPhone: String?,
        val clientPhone: PhoneNumber?,
        val salonId: SalonId?,
        val appointmentDate: String?,
        val appointmentTime: String?,
        val googleLink: String? = null,
    ) {
        fun hasAllInfo(): Boolean = clientName != null && petName != null && salonName != null
    }

    /**
     * Get template content from database or fallback to default
     */
    private fun getTemplateContent(salonId: SalonId?, templateType: SmsTemplateType): String {
        logger.info("🔍 Looking up template - Salon: ${salonId?.value ?: "null"}, Type: $templateType")

        val template = salonId?.let {
            try {
                val dbTemplate = smsTemplateRepository.findBySalonIdAndType(it, templateType)
                logger.info("📄 Database template found: ${dbTemplate != null}")
                if (dbTemplate != null) {
                    logger.info("📝 Template content: ${dbTemplate.templateContent}")
                }
                dbTemplate?.templateContent
            } catch (e: Exception) {
                logger.warn("❌ Failed to retrieve template for salon ${it.value}, type $templateType: ${e.message}")
                null
            }
        } ?: run {
            logger.info("🔄 Using default template for type: $templateType")
            val defaultContent = SmsTemplate.getDefaultContent(templateType)
            logger.info("📝 Default template content: $defaultContent")
            defaultContent
        }

        logger.info("✅ Final template selected: $template")
        return template
    }

    /**
     * Populate template with variables, handling both old and new variable naming conventions
     */
    private fun populateTemplate(template: String, variables: Map<String, String>): String {
        var result = template

        logger.info("🔧 [TEMPLATE_DEBUG] Original template: $template")
        logger.info("🔧 [TEMPLATE_DEBUG] Variables: $variables")

        // Replace all variables in the template
        variables.forEach { (key, value) ->
            val originalResult = result

            // Handle both {KEY} and {key} formats for backward compatibility
            result = result.replace("{$key}", value, ignoreCase = true)
            result = result.replace("{${key.lowercase()}}", value, ignoreCase = true)
            result = result.replace("{${key.uppercase()}}", value, ignoreCase = true)

            // Handle common variable name variations
            when (key) {
                "APPOINTMENT_DATE" -> {
                    result = result.replace("{date}", value, ignoreCase = true)
                    result = result.replace("{appointmentDate}", value, ignoreCase = true)
                    result = result.replace("{APPOINTMENT_DATE}", value, ignoreCase = true)
                }
                "APPOINTMENT_TIME" -> {
                    result = result.replace("{time}", value, ignoreCase = true)
                    result = result.replace("{appointmentTime}", value, ignoreCase = true)
                    result = result.replace("{APPOINTMENT_TIME}", value, ignoreCase = true)
                }
                "OWNER_NAME" -> {
                    result = result.replace("{clientName}", value, ignoreCase = true)
                    result = result.replace("{ownerName}", value, ignoreCase = true)
                }
                "PET_NAME" -> {
                    result = result.replace("{petName}", value, ignoreCase = true)
                }
                "SALON_NAME" -> {
                    result = result.replace("{salonName}", value, ignoreCase = true)
                }
                "SALON_ADDRESS" -> {
                    result = result.replace("{salonAddress}", value, ignoreCase = true)
                }
                "SALON_PHONE" -> {
                    result = result.replace("{salonPhone}", value, ignoreCase = true)
                }
            }

            if (originalResult != result) {
                logger.info("🔧 [TEMPLATE_DEBUG] Replaced $key with '$value'")
            }
        }

        logger.info("🔧 [TEMPLATE_DEBUG] Final result: $result")
        return result
    }

    /**
     * Send appointment completion notification
     */
    fun sendAppointmentCompletionNotification(
        clientId: ClientId,
        petId: PetId?,
        appointmentId: AppointmentId?,
    ) {
        logger.info("📱 Sending appointment completion notification - Client: ${clientId.value}, Appointment: ${appointmentId?.value}")

        val messageContext = buildMessageContext(clientId, petId, appointmentId)
        val salonId = getSalonIdFromContext(appointmentId)

        logger.info("🏪 Salon ID for template lookup: ${salonId?.value ?: "null"}")

        // Get appointment details for date and time
        val appointment = appointmentId?.let { appointmentRepository.findById(it) }
        val appointmentDate = appointment?.appointmentDate
        val appointmentTime = appointment?.startTime

        val message = buildAppointmentCompletionMessage(messageContext, appointmentDate, appointmentTime)

        logger.info("💬 Final SMS completion message: $message")

        sendMessageToClient(clientId, message, salonId, SmsMessageType.APPOINTMENT_COMPLETION, appointmentId, messageContext)
    }

    /**
     * Send appointment follow-up notification
     */
    fun sendAppointmentFollowUpNotification(
        clientId: ClientId,
        petId: PetId?,
        appointmentId: AppointmentId?,
    ) {
        logger.info("📬 Sending appointment follow-up notification - Client: ${clientId.value}, Appointment: ${appointmentId?.value}")

        val messageContext = buildMessageContext(clientId, petId, appointmentId)
        val salonId = getSalonIdFromContext(appointmentId)

        logger.info("🏪 Salon ID for template lookup: ${salonId?.value ?: "null"}")

        // Get appointment details for date and time
        val appointment = appointmentId?.let { appointmentRepository.findById(it) }
        val appointmentDate = appointment?.appointmentDate
        val appointmentTime = appointment?.startTime

        val message = buildAppointmentFollowUpMessage(messageContext, appointmentDate, appointmentTime)

        logger.info("💬 Final SMS follow-up message: $message")

        sendMessageToClient(clientId, message, salonId, SmsMessageType.FOLLOW_UP, appointmentId, messageContext)
    }

    /**
     * Build appointment completion message using SMS template system
     */
    private fun buildAppointmentCompletionMessage(
        context: MessageContext,
        appointmentDate: LocalDate?,
        appointmentTime: LocalTime?,
    ): String {
        logger.info("Building appointment completion message using SMS template system for salon: ${context.salonId}")

        // Try to get the appointment completion SMS template
        val smsTemplate = try {
            context.salonId?.let { salonId ->
                smsTemplateRepository.findBySalonIdAndType(salonId, SmsTemplateType.APPOINTMENT_COMPLETION)
            }
        } catch (e: Exception) {
            logger.warn("Failed to retrieve appointment completion SMS template for salon ${context.salonId}: ${e.message}")
            null
        }

        val template = if (smsTemplate != null && smsTemplate.isActive) {
            logger.info("Using custom appointment completion SMS template for salon: ${context.salonId}")
            smsTemplate.templateContent
        } else {
            logger.info("Using default appointment completion template for salon: ${context.salonId}")
            SmsTemplate.getDefaultContent(SmsTemplateType.APPOINTMENT_COMPLETION)
        }

        return populateTemplate(template, mapOf(
            "SALON_NAME" to (context.salonName ?: "Salonul nostru"),
            "OWNER_NAME" to (context.clientName ?: "Stimate client"),
            "PET_NAME" to (context.petName ?: "animalul dumneavoastră"),
            "SALON_ADDRESS" to (context.salonAddress ?: "salonul nostru"),
            "SALON_PHONE" to (context.salonPhone ?: "0721 000 000"),
            "APPOINTMENT_DATE" to (appointmentDate?.format(dateFormatter) ?: "data programării"),
            "APPOINTMENT_TIME" to (appointmentTime?.format(timeFormatter) ?: "ora programării")
        ))
    }

    /**
     * Build appointment follow-up message using SMS template system
     */
    private fun buildAppointmentFollowUpMessage(
        context: MessageContext,
        appointmentDate: LocalDate?,
        appointmentTime: LocalTime?,
    ): String {
        logger.info("Building appointment follow-up message using SMS template system for salon: ${context.salonId}")

        // Try to get the follow-up SMS template
        val smsTemplate = try {
            context.salonId?.let { salonId ->
                smsTemplateRepository.findBySalonIdAndType(salonId, SmsTemplateType.FOLLOW_UP)
            }
        } catch (e: Exception) {
            logger.warn("Failed to retrieve follow-up SMS template for salon ${context.salonId}: ${e.message}")
            null
        }

        val template = if (smsTemplate != null && smsTemplate.isActive) {
            logger.info("Using custom follow-up SMS template for salon: ${context.salonId}")
            smsTemplate.templateContent
        } else {
            logger.info("Using default follow-up template for salon: ${context.salonId}")
            SmsTemplate.getDefaultContent(SmsTemplateType.FOLLOW_UP)
        }

        return populateTemplate(template, mapOf(
            "SALON_NAME" to (context.salonName ?: "Salonul nostru"),
            "OWNER_NAME" to (context.clientName ?: "Stimate client"),
            "PET_NAME" to (context.petName ?: "animalul dumneavoastră"),
            "SALON_ADDRESS" to (context.salonAddress ?: "salonul nostru"),
            "SALON_PHONE" to (context.salonPhone ?: "0721 000 000"),
            "APPOINTMENT_DATE" to (appointmentDate?.format(dateFormatter) ?: "data programării"),
            "APPOINTMENT_TIME" to (appointmentTime?.format(timeFormatter) ?: "ora programării"),
            "GOOGLE_LINK" to (context.googleLink ?: "")
        ))
    }


}
