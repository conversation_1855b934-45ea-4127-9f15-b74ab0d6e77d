package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalDateTime

/**
 * Domain entity representing a client (pet owner)
 * Pure domain model with no infrastructure dependencies
 */
data class Client(
    val id: ClientId,
    val salonId: SalonId?, // Foreign key to salon
    val name: String,
    val phone: PhoneNumber?,
    val secondaryPhone: PhoneNumber?,
    val email: Email?,
    val address: String?,
    val notes: String?,
    val isActive: Boolean = true,
    val userIds: Set<UserId> = emptySet(),
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) {
    constructor(name: String, phone: PhoneNumber, salonId: SalonId? = null) : this(
        ClientId.generate(),
        salonId,
        name,
        phone,
        null,
        null,
        null,
        null,
        true,
        emptySet(),
        LocalDateTime.now(),
        LocalDateTime.now(),
    )

    // Validation is now handled by ValidationService in the application layer
    // This keeps the domain model clean and validation logic centralized

    /**
     * Create a new client with updated information
     */
    fun update(
        name: String = this.name,
        phone: PhoneNumber? = this.phone,
        secondaryPhone: PhoneNumber? = this.secondaryPhone,
        email: Email? = this.email,
        address: String? = this.address,
        notes: String? = this.notes,
        isActive: Boolean = this.isActive,
        userIds: Set<UserId> = this.userIds,
    ): Client {
        return copy(
            name = name,
            phone = phone,
            secondaryPhone = secondaryPhone,
            email = email,
            address = address,
            notes = notes,
            isActive = isActive,
            userIds = userIds,
            updatedAt = LocalDateTime.now(),
        )
    }

    /**
     * Deactivate the client
     */
    fun deactivate(): Client = copy(isActive = false, updatedAt = LocalDateTime.now())

    /**
     * Activate the client
     */
    fun activate(): Client = copy(isActive = true, updatedAt = LocalDateTime.now())

    companion object {
        /**
         * Create a new client
         */
        fun create(
            name: String,
            phone: PhoneNumber? = null,
            secondaryPhone: PhoneNumber? = null,
            email: Email? = null,
            address: String? = null,
            notes: String? = null,
            salonId: SalonId? = null,
            userIds: Set<UserId> = emptySet(),
        ): Client {
            return Client(
                id = ClientId.generate(),
                salonId = salonId,
                name = name,
                phone = phone,
                secondaryPhone = secondaryPhone,
                email = email,
                address = address,
                notes = notes,
                userIds = userIds,
            )
        }
    }
}
