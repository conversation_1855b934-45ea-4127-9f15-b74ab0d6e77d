package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalDateTime

/**
 * Domain entity representing customizable SMS reminder timing for a salon
 */
data class SmsReminderTiming(
    val id: SmsReminderTimingId,
    val salonId: SalonId,
    val hoursBefore: Int,
    val isEnabled: Boolean = true,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) {
    init {
        require(hoursBefore > 0) { "Hours before must be positive" }
        require(hoursBefore <= 168) { "Hours before cannot exceed 168 (7 days)" }
    }

    /**
     * Update timing hours
     */
    fun updateHours(newHours: Int): SmsReminderTiming {
        require(newHours > 0) { "Hours before must be positive" }
        require(newHours <= 168) { "Hours before cannot exceed 168 (7 days)" }

        return copy(
            hoursBefore = newHours,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Enable or disable this timing
     */
    fun setEnabled(enabled: Boolean): SmsReminderTiming {
        return copy(
            isEnabled = enabled,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Get human-readable description of this timing
     */
    fun getDescription(): String {
        return when {
            hoursBefore == 1 -> "Cu o oră înainte"
            hoursBefore < 24 -> "Cu $hoursBefore ore înainte"
            hoursBefore == 24 -> "Cu o zi înainte"
            hoursBefore == 48 -> "Cu 2 zile înainte"
            hoursBefore % 24 == 0 -> "Cu ${hoursBefore / 24} zile înainte"
            else -> "Cu $hoursBefore ore înainte"
        }
    }

    companion object {
        /**
         * Create a new SMS reminder timing
         */
        fun create(
            salonId: SalonId,
            hoursBefore: Int
        ): SmsReminderTiming {
            return SmsReminderTiming(
                id = SmsReminderTimingId.generate(),
                salonId = salonId,
                hoursBefore = hoursBefore
            )
        }

    }
}

/**
 * Value object for SMS reminder timing ID
 */
@JvmInline
value class SmsReminderTimingId(val value: String) {
    companion object {
        fun generate(): SmsReminderTimingId = SmsReminderTimingId(java.util.UUID.randomUUID().toString())
        fun of(value: String): SmsReminderTimingId = SmsReminderTimingId(value)
    }
}
