package ro.animaliaprogramari.animalia.domain.model

import java.util.*

/**
 * Value object representing a phone verification code ID
 */
@JvmInline
value class PhoneVerificationCodeId(val value: String) {
    init {
        require(value.isNotBlank()) { "Phone verification code ID cannot be blank" }
    }

    companion object {
        fun generate(): PhoneVerificationCodeId = PhoneVerificationCodeId(UUID.randomUUID().toString())
        fun of(value: String): PhoneVerificationCodeId = PhoneVerificationCodeId(value)
    }

    override fun toString(): String = value
}
