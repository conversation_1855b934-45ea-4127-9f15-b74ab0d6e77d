package ro.animaliaprogramari.animalia.domain.model

import java.util.UUID

/**
 * Value object representing a unique client identifier
 */
@JvmInline
value class ClientId(val value: String) {
    init {
        require(value.isNotBlank()) { "Client ID cannot be blank" }
    }

    companion object {
        fun generate(): ClientId = ClientId(UUID.randomUUID().toString())

        fun of(value: String): ClientId = ClientId(value)
    }

    override fun toString(): String = value
}
