package ro.animaliaprogramari.animalia.domain.event.appointment

import ro.animaliaprogramari.animalia.domain.event.DomainEvent
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDateTime

/**
 * Event fired when an appointment is confirmed (e.g., by client via SMS)
 */
data class AppointmentConfirmedEvent(
    override val eventId: String,
    override val occurredAt: LocalDateTime,
    override val aggregateId: String,
    val appointmentId: AppointmentId,
    val clientId: ClientId,
    val petId: PetId?,
    val appointmentDate: java.time.LocalDate,
    val startTime: java.time.LocalTime,
    val salonId: SalonId,
    val confirmedBy: String? = null, // "SMS", "CLIENT_PORTAL", "STAFF", etc.
) : DomainEvent

