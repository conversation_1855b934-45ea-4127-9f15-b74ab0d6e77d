package ro.animaliaprogramari.animalia.domain.event.appointment

import ro.animaliaprogramari.animalia.domain.event.DomainEvent
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDateTime

/**
 * Event fired when a proto-appointment (PLACEHOLDER) is created during online booking
 * This triggers push notifications to salon staff to see the reservation in real-time
 */
data class ProtoAppointmentCreatedEvent(
    override val eventId: String,
    override val occurredAt: LocalDateTime,
    override val aggregateId: String,
    val appointmentId: AppointmentId,
    val staffId: StaffId,
    val salonId: SalonId,
    val appointmentDate: java.time.LocalDate,
    val startTime: java.time.LocalTime,
    val endTime: java.time.LocalTime,
    val serviceIds: List<ServiceId>,
    val expiresAt: LocalDateTime,
) : DomainEvent

