package ro.animaliaprogramari.animalia.domain.exception

import ro.animaliaprogramari.animalia.domain.model.BlockTimeConflict
import ro.animaliaprogramari.animalia.domain.model.SuggestedAction

/**
 * Exception thrown when there are scheduling conflicts during block time operations
 */
class SchedulingConflictException(
    message: String,
    val conflicts: List<BlockTimeConflict> = emptyList(),
    val suggestedActions: List<SuggestedAction> = emptyList(),
) : DomainException(message)

/**
 * Exception thrown when block time operations fail due to business rules
 */
class BlockTimeOperationException(
    message: String,
    cause: Throwable? = null,
) : DomainException(message, cause)

/**
 * Exception thrown when trying to block time in the past
 */
class PastTimeBlockException(
    message: String = "Cannot block time in the past",
) : DomainException(message)

/**
 * Exception thrown when block duration violates business rules
 */
class InvalidBlockDurationException(
    message: String,
) : DomainException(message)

