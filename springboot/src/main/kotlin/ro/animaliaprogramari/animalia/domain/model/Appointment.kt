package ro.animaliaprogramari.animalia.domain.model

import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.CustomServiceData
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

/**
 * Domain entity representing an appointment
 * Pure domain model with no infrastructure dependencies
 * Enhanced to support recurring appointments through AppointmentSubscription
 */
data class Appointment(
    val id: AppointmentId,
    val salonId: SalonId,
    val clientId: ClientId,
    val petId: PetId,
    val staffId: StaffId, // Staff member (formerly userId)
    val appointmentDate: LocalDate,
    val startTime: LocalTime,
    val endTime: LocalTime,
    val status: AppointmentStatus,
    val serviceIds: List<ServiceId>,
    val customServices: Map<ServiceId, CustomServiceData>? = null, // Ad-hoc service modifications
    val totalPrice: Money,
    val totalMyDuration: MyDuration,
    val notes: String?,
    val photos: List<String> = emptyList(),

    // Recurring appointment support
    val subscriptionId: AppointmentSubscriptionId? = null, // Reference to parent subscription
    val sequenceNumber: Int? = null, // Position in recurring series (1, 2, 3, etc.)
    val isRecurring: Boolean = false, // Flag to indicate if this is part of a recurring series

    val completedAt: LocalDateTime? = null,
    val actualDurationMinutes: Int? = null,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val version: Long = 0, // For optimistic locking
) {
    init {
        require(startTime.isBefore(endTime)) { "Start time must be before end time" }
        require(serviceIds.isNotEmpty()) { "Appointment must have at least one service" }
        require(totalPrice.amount > BigDecimal.ZERO) { "Total price must be positive" }
        require(totalMyDuration.minutes > 0) { "Total duration must be positive" }
        notes?.let { require(it.length <= 2000) { "Notes cannot exceed 2000 characters" } }

        // Validate recurring appointment fields consistency
        if (isRecurring) {
            require(subscriptionId != null) { "Recurring appointments must have a subscription ID" }
            require(sequenceNumber != null && sequenceNumber > 0) { "Recurring appointments must have a valid sequence number" }
        }

        // If subscription ID is provided, appointment should be marked as recurring
        if (subscriptionId != null) {
            require(isRecurring) { "Appointments with subscription ID must be marked as recurring" }
        }
    }

    /**
     * Update appointment status
     */
    fun updateStatus(newStatus: AppointmentStatus): Appointment {
        return copy(status = newStatus, updatedAt = LocalDateTime.now())
    }

    /**
     * Update appointment notes
     */
    fun updateNotes(newNotes: String?): Appointment {
        return copy(notes = newNotes, updatedAt = LocalDateTime.now())
    }

    /**
     * Reschedule the appointment
     */
    fun reschedule(
        newDate: LocalDate,
        newStartTime: LocalTime,
        newEndTime: LocalTime,
        newStaffId: StaffId? = null,
    ): Appointment {
        require(newStartTime.isBefore(newEndTime)) { "Start time must be before end time" }

        return copy(
            appointmentDate = newDate,
            startTime = newStartTime,
            endTime = newEndTime,
            staffId = newStaffId ?: staffId,
            status = AppointmentStatus.RESCHEDULED,
            updatedAt = LocalDateTime.now(),
        )
    }

    /**
     * Cancel the appointment
     */
    fun cancel(reason: String? = null): Appointment {
        require(canBeCancelled()) { "Appointment cannot be cancelled in current status: $status" }

        return copy(
            status = AppointmentStatus.CANCELLED,
            notes =
                if (reason != null) {
                    if (notes.isNullOrBlank()) "Cancelled: $reason" else "$notes\nCancelled: $reason"
                } else {
                    notes
                },
            updatedAt = LocalDateTime.now(),
        )
    }

    /**
     * Complete the appointment with optional custom completion time
     */
    fun complete(completionNotes: String? = null, customCompletedAt: LocalDateTime? = null): Appointment {
        require(canBeCompleted()) { "Appointment cannot be completed in current status: $status" }

        val actualCompletionTime = customCompletedAt ?: LocalDateTime.now()

        // Calculate actual duration from start time to completion time
        val startDateTime = LocalDateTime.of(appointmentDate, startTime)
        val actualDuration = java.time.Duration.between(startDateTime, actualCompletionTime).toMinutes().toInt()

        return copy(
            status = AppointmentStatus.COMPLETED,
            completedAt = actualCompletionTime,
            actualDurationMinutes = actualDuration,
            notes =
                if (completionNotes != null) {
                    if (notes.isNullOrBlank()) completionNotes else "$notes\n$completionNotes"
                } else {
                    notes
                },
            updatedAt = LocalDateTime.now(),
        )
    }

    /**
     * Mark appointment as no-show
     */
    fun markNoShow(noShowNotes: String? = null): Appointment {
        return copy(
            status = AppointmentStatus.NO_SHOW,
            notes =
                if (noShowNotes != null) {
                    if (notes.isNullOrBlank()) "No-show: $noShowNotes" else "$notes\nNo-show: $noShowNotes"
                } else {
                    notes
                },
            updatedAt = LocalDateTime.now(),
        )
    }

    /**
     * Check if appointment overlaps with given time range
     */
    fun overlaps(
        otherStartTime: LocalTime,
        otherEndTime: LocalTime,
    ): Boolean {
        return !(
            endTime.isBefore(otherStartTime) || endTime.equals(otherStartTime) ||
                startTime.isAfter(otherEndTime) || startTime.equals(otherEndTime)
        )
    }

    /**
     * Check if appointment is active (not cancelled, completed, or no-show)
     * Note: PLACEHOLDER status is considered active to block time slots during online booking
     */
    fun isActive(): Boolean {
        return status !in listOf(AppointmentStatus.CANCELLED, AppointmentStatus.COMPLETED, AppointmentStatus.NO_SHOW)
    }

    /**
     * Check if appointment is in the past
     */
    fun isPast(): Boolean {
        val appointmentDateTime = LocalDateTime.of(appointmentDate, startTime)
        return appointmentDateTime.isBefore(LocalDateTime.now())
    }

    /**
     * Check if appointment can be cancelled
     */
    fun canBeCancelled(): Boolean {
        return status in listOf(
            AppointmentStatus.SCHEDULED,
            AppointmentStatus.CONFIRMED,
            AppointmentStatus.RESCHEDULED,
            AppointmentStatus.REQUESTED  // Allow cancelling requested appointments (rejection)
        )
    }

    /**
     * Check if appointment can be completed
     */
    fun canBeCompleted(): Boolean {
        return status in listOf(AppointmentStatus.SCHEDULED, AppointmentStatus.CONFIRMED, AppointmentStatus.IN_PROGRESS, AppointmentStatus.RESCHEDULED)
    }

    /**
     * Check if appointment can be rescheduled
     */
    fun canBeRescheduled(): Boolean {
        return status in listOf(AppointmentStatus.SCHEDULED, AppointmentStatus.CONFIRMED, AppointmentStatus.RESCHEDULED) && !isPast()
    }

    // ========== RECURRING APPOINTMENT METHODS ==========


    /**
     * Update appointment with subscription information (for recurring appointments)
     */
    fun linkToSubscription(
        subscriptionId: AppointmentSubscriptionId,
        sequenceNumber: Int
    ): Appointment {
        require(sequenceNumber > 0) { "Sequence number must be positive" }

        return copy(
            subscriptionId = subscriptionId,
            sequenceNumber = sequenceNumber,
            isRecurring = true,
            updatedAt = LocalDateTime.now(),
            version = version + 1
        )
    }

    companion object {
        /**
         * Create a new regular (non-recurring) appointment
         */
        fun create(
            salonId: SalonId,
            clientId: ClientId,
            petId: PetId,
            staffId: StaffId,
            appointmentDate: LocalDate,
            startTime: LocalTime,
            endTime: LocalTime,
            salonServices: List<SalonService>,
            customServices: Map<ServiceId, CustomServiceData>? = null,
            notes: String? = null,
        ): Appointment =
            Appointment(
                id = AppointmentId.generate(),
                salonId = salonId,
                clientId = clientId,
                petId = petId,
                staffId = staffId,
                appointmentDate = appointmentDate,
                startTime = startTime,
                endTime = endTime,
                status = AppointmentStatus.SCHEDULED,
                serviceIds = salonServices.map { it.id },
                customServices = customServices,
                totalPrice = calculateTotalPrice(salonServices, customServices),
                totalMyDuration = calculateTotalDuration(salonServices, customServices),
                notes = notes,
                isRecurring = false,
            )

        /**
         * Create a new recurring appointment linked to a subscription
         */
        fun createRecurring(
            salonId: SalonId,
            clientId: ClientId,
            petId: PetId,
            staffId: StaffId,
            appointmentDate: LocalDate,
            startTime: LocalTime,
            endTime: LocalTime,
            salonServices: List<SalonService>,
            subscriptionId: AppointmentSubscriptionId,
            sequenceNumber: Int,
            notes: String? = null,
        ): Appointment {
            require(sequenceNumber > 0) { "Sequence number must be positive" }

            return Appointment(
                id = AppointmentId.generate(),
                salonId = salonId,
                clientId = clientId,
                petId = petId,
                staffId = staffId,
                appointmentDate = appointmentDate,
                startTime = startTime,
                endTime = endTime,
                status = AppointmentStatus.SCHEDULED,
                serviceIds = salonServices.map { it.id },
                totalPrice =
                    salonServices.fold(Money.ZERO) { total, service ->
                        total.add(service.basePrice)
                    },
                totalMyDuration =
                    salonServices.fold(MyDuration.ofMinutes(0)) { total, service ->
                        total.add(service.myDuration)
                    },
                notes = notes,
                subscriptionId = subscriptionId,
                sequenceNumber = sequenceNumber,
                isRecurring = true,
            )
        }

        /**
         * Calculate total price considering custom services
         */
        private fun calculateTotalPrice(
            salonServices: List<SalonService>,
            customServices: Map<ServiceId, CustomServiceData>?
        ): Money {
            return salonServices.fold(Money.ZERO) { total, service ->
                val customService = customServices?.get(service.id)
                if (customService != null) {
                    total.add(Money.of(customService.customPrice.toBigDecimal()))
                } else {
                    total.add(service.basePrice)
                }
            }
        }

        /**
         * Calculate total duration using original service durations only
         */
        private fun calculateTotalDuration(
            salonServices: List<SalonService>,
            customServices: Map<ServiceId, CustomServiceData>?
        ): MyDuration {
            return salonServices.fold(MyDuration.ofMinutes(0)) { total, service ->
                // Always use original service duration, ignore custom duration
                total.add(service.myDuration)
            }
        }

    }
}
