package ro.animaliaprogramari.animalia.domain.model

/**
 * Value object representing one or more international phone numbers
 * Supports multiple phone numbers separated by comma (max 3)
 */
@JvmInline
value class PhoneNumber(val value: String) {
    init {
        require(value.isNotBlank()) { "Phone number cannot be blank" }
        require(isValidPhoneNumber(value)) { "Invalid phone number format: $value" }
    }

    private fun isValidPhoneNumber(phone: String): Boolean {
        // Handle multiple phone numbers separated by comma
        if (phone.contains(",")) {
            val numbers = phone.split(",").map { it.trim() }
            return numbers.all { number ->
                if (number.isBlank()) false
                else {
                    val normalizedPhone = normalize(number)
                    normalizedPhone.matches(Regex("^\\+[1-9][0-9]{0,2}[0-9]{4,14}$"))
                }
            }
        }
        
        val normalizedPhone = normalize(phone)
        // Must be in international format: +[country code][number]
        // Country code: 1-3 digits, followed by 4-15 digits for the phone number
        return normalizedPhone.matches(Regex("^\\+[1-9][0-9]{0,2}[0-9]{4,14}$"))
    }

    /**
     * Convert to international format (+CountryCodeXXXXXXXXX)
     */
    fun toInternationalFormat(): String {
        val cleanPhone = value.replace(Regex("[\\s\\-\\(\\)\\.]"), "")
        return when {
            // Already in international format with +
            cleanPhone.startsWith("+") -> cleanPhone
            // Romanian format with 0040
            cleanPhone.startsWith("0040") -> "+${cleanPhone.substring(2)}"
            // Romanian local format starting with 0
            cleanPhone.startsWith("0") && cleanPhone.length == 10 -> "+40${cleanPhone.substring(1)}"
            // 9 digits without country code - assume Romanian
            cleanPhone.matches(Regex("\\d{9}")) -> "+40$cleanPhone"
            else -> value // Return original if can't convert
        }
    }

    /**
     * Check if this is a Romanian phone number
     */
    fun isRomanianNumber(): Boolean {
        val cleanPhone = value.replace(Regex("[\\s\\-\\(\\)\\.]"), "")
        return cleanPhone.startsWith("+40") || cleanPhone.startsWith("0040") ||
            (cleanPhone.startsWith("0") && cleanPhone.length >= 9)
    }

    companion object {
        fun of(value: String): PhoneNumber = PhoneNumber(value)

        /**
         * Create phone number from string, returning null if blank or invalid
         */
        fun ofNullable(value: String?): PhoneNumber? {
            return if (value.isNullOrBlank()) {
                null
            } else {
                try {
                    PhoneNumber(value)
                } catch (e: IllegalArgumentException) {
                    null
                }
            }
        }

        /**
         * Validate and normalize a phone number
         * Returns normalized phone number or null if invalid
         */
        fun validateAndNormalize(phone: String?): String? {
            if (phone.isNullOrBlank()) {
                return null
            }

            return try {
                val normalized = normalize(phone)
                if (normalized.matches(Regex("^\\+[1-9][0-9]{0,2}[0-9]{4,14}$"))) {
                    normalized
                } else {
                    null
                }
            } catch (e: Exception) {
                null
            }
        }

        /**
         * Check if a phone number is in valid international format
         * Used for validation before creating PhoneNumber instance
         */
        fun isValidInternationalFormat(phone: String): Boolean {
            if (phone.isBlank()) return false
            return try {
                val normalized = normalize(phone)
                normalized.matches(Regex("^\\+[1-9][0-9]{0,2}[0-9]{4,14}$"))
            } catch (e: Exception) {
                false
            }
        }

        /**
         * Normalize phone number to international format
         * This is a public utility function for validation purposes
         */
        fun normalize(phone: String): String {
            // Remove all non-digit characters except + at the beginning
            val cleanPhone = phone.replace(Regex("[^+0-9]"), "")

            return when {
                // Already starts with +
                cleanPhone.startsWith("+") -> cleanPhone
                // Romanian format with 0040
                cleanPhone.startsWith("0040") -> "+${cleanPhone.substring(2)}"
                // Romanian local format
                cleanPhone.startsWith("0") && cleanPhone.length >= 9 -> "+40${cleanPhone.substring(1)}"
                // 9 digits - assume Romanian
                cleanPhone.matches(Regex("\\d{9}")) -> "+40$cleanPhone"
                // Add + if missing but looks international
                cleanPhone.matches(Regex("[1-9][0-9]{6,14}")) -> "+$cleanPhone"
                else -> cleanPhone
            }
        }
    }

    override fun toString(): String = value
}
