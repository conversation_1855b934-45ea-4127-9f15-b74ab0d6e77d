package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalDateTime

/**
 * Domain entity representing a salon
 * Pure domain model with no infrastructure dependencies
 */
data class Salon(
    val id: SalonId,
    val name: String,
    val address: String?,
    val city: String?,
    val phone: PhoneNumber?,
    val email: Email?,
    val ownerId: UserId,
    val isActive: Boolean = true,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val description: String?,
    val additionalSlotsCount: Int = 0,
    val googleReviewLink: String? = null,
) {
    // Validation is now handled by ValidationService in the application layer
    // This keeps the domain model clean and validation logic centralized

    /**
     * Activate salon
     */
    fun activate(): Salon {
        return copy(
            isActive = true,
            updatedAt = LocalDateTime.now(),
        )
    }

    /**
     * Deactivate salon
     */
    fun deactivate(): Salon {
        return copy(
            isActive = false,
            updatedAt = LocalDateTime.now(),
        )
    }

    /**
     * Increment additional slots counter
     */
    fun incrementAdditionalSlotsCount(): Salon {
        return copy(
            additionalSlotsCount = additionalSlotsCount + 1,
            updatedAt = LocalDateTime.now(),
        )
    }

    companion object {
        /**
         * Create a new salon
         */
        fun create(
            name: String,
            address: String? = null,
            city: String? = null,
            phone: PhoneNumber? = null,
            email: Email? = null,
            ownerId: UserId,
            description: String?,
            googleReviewLink: String? = null,
        ): Salon {
            return Salon(
                id = SalonId.generate(),
                name = name,
                address = address,
                city = city,
                phone = phone,
                email = email,
                ownerId = ownerId,
                description = description,
                additionalSlotsCount = 0,
                googleReviewLink = googleReviewLink,
            )
        }
    }
}
