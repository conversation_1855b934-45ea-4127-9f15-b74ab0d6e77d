package ro.animaliaprogramari.animalia.domain.service

import io.micrometer.core.instrument.MeterRegistry
import org.springframework.stereotype.Service
import java.util.concurrent.TimeUnit

/**
 * Service for monitoring database query performance
 * Tracks execution times and identifies slow queries
 */
@Service
class QueryPerformanceMonitor(
    private val meterRegistry: MeterRegistry,
) {

    companion object {
        private const val SLOW_QUERY_THRESHOLD_MS = 100L
    }

    /**
     * Get performance statistics for a query
     */
    fun getQueryStatistics(queryName: String): QueryStatistics? {
        val timer = meterRegistry.find("database.query.duration")
            .tag("query.name", queryName)
            .timer()

        return timer?.let {
            QueryStatistics(
                queryName = queryName,
                count = it.count(),
                totalTimeMs = it.totalTime(TimeUnit.MILLISECONDS).toLong(),
                averageTimeMs = it.mean(TimeUnit.MILLISECONDS),
                maxTimeMs = it.max(TimeUnit.MILLISECONDS)
            )
        }
    }

    /**
     * Get all slow queries above threshold
     */
    fun getSlowQueries(): List<QueryStatistics> {
        return meterRegistry.find("database.query.duration")
            .timers()
            .mapNotNull { timer ->
                val queryName = timer.id.getTag("query.name") ?: "unknown"
                if (timer.mean(TimeUnit.MILLISECONDS) >= SLOW_QUERY_THRESHOLD_MS) {
                    QueryStatistics(
                        queryName = queryName,
                        count = timer.count(),
                        totalTimeMs = timer.totalTime(TimeUnit.MILLISECONDS).toLong(),
                        averageTimeMs = timer.mean(TimeUnit.MILLISECONDS),
                        maxTimeMs = timer.max(TimeUnit.MILLISECONDS)
                    )
                } else null
            }
            .sortedByDescending { it.averageTimeMs }
    }
}

/**
 * Query performance statistics
 */
data class QueryStatistics(
    val queryName: String,
    val count: Long,
    val totalTimeMs: Long,
    val averageTimeMs: Double,
    val maxTimeMs: Double,
) {
    val isSlowQuery: Boolean
        get() = averageTimeMs >= 100.0

    val isVerySlowQuery: Boolean
        get() = averageTimeMs >= 500.0
}
