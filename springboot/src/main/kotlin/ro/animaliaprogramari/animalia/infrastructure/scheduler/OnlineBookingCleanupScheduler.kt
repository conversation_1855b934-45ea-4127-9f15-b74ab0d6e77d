package ro.animaliaprogramari.animalia.infrastructure.scheduler

import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.application.usecase.OnlineBookingManagementUseCase

/**
 * Scheduler for automatically cleaning up expired online booking placeholders and proto-appointments
 *
 * This scheduler runs every minute to ensure that:
 * 1. Expired placeholder records are deleted from the database
 * 2. Expired proto-appointments (PLACEHOLDER status) are removed from the calendar
 *
 * Proto-appointments expire after 5 minutes if the client doesn't complete the booking form.
 * This prevents time slots from being blocked indefinitely by abandoned bookings.
 */
@Component
@ConditionalOnProperty(
    name = ["animalia.scheduling.online-booking-cleanup.enabled"],
    havingValue = "true",
    matchIfMissing = true
)
class OnlineBookingCleanupScheduler(
    private val onlineBookingManagementUseCase: OnlineBookingManagementUseCase
) {
    private val logger = LoggerFactory.getLogger(OnlineBookingCleanupScheduler::class.java)

    /**
     * Clean up expired placeholders and proto-appointments every minute
     *
     * Runs at the start of every minute (e.g., 14:00:00, 14:01:00, 14:02:00, etc.)
     * This ensures that expired proto-appointments are removed promptly, making the
     * time slots available for other clients.
     */
    @Scheduled(cron = "0 * * * * *") // Every minute at :00 seconds
    fun cleanupExpiredBookings() {
        try {
            logger.info("🧹 Starting cleanup of expired proto-appointments...")

            val deletedCount = onlineBookingManagementUseCase.cleanupExpiredProtoAppointments()

            if (deletedCount > 0) {
                logger.info("✅ Cleaned up $deletedCount expired proto-appointments")
            } else {
                logger.info("No expired proto-appointments found")
            }
        } catch (e: Exception) {
            logger.error("❌ Failed to cleanup expired proto-appointments", e)
        }
    }
}

