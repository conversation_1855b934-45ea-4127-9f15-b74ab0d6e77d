package ro.animaliaprogramari.animalia.application.service

import org.slf4j.LoggerFactory
import org.springframework.data.rest.webmvc.ResourceNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.application.port.outbound.SalonRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SalonWebPreferencesRepository
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDateTime
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import java.util.Optional

/**
 * Service for managing salon website preferences
 * Follows hexagonal architecture pattern with domain models and repository ports
 */
@Service
class SalonWebPreferencesService(
    private val salonWebPreferencesRepository: SalonWebPreferencesRepository,
    private val salonRepository: SalonRepository,
) {
    private val logger = LoggerFactory.getLogger(SalonWebPreferencesService::class.java)
    private val objectMapper = ObjectMapper()

    // Legacy methods for backward compatibility with existing controller

    /**
     * Get web preferences for a salon (legacy method)
     * Returns Optional for backward compatibility
     */
    @Transactional(readOnly = true)
    fun getWebPreferences(salonId: String): Optional<ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences> {
        logger.info("Getting web preferences for salon (legacy): {}", salonId)

        val salonIdDomain = SalonId.of(salonId)

        // Verify salon exists
        if (!salonRepository.existsById(salonIdDomain)) {
            return Optional.empty()
        }

        val domainPreferences = salonWebPreferencesRepository.findBySalonId(salonIdDomain)
            ?: return Optional.empty()

        // Convert domain model to JPA entity for backward compatibility
        val entity = convertDomainToEntity(domainPreferences)
        return Optional.of(entity)
    }

    /**
     * Save web preferences for a salon (legacy method)
     */
    @Transactional
    fun saveWebPreferences(salonId: String, preferences: ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences?): ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences {
        logger.info("Saving web preferences for salon (legacy): {}", salonId)

        if (preferences == null) {
            throw IllegalArgumentException("Preferences cannot be null")
        }

        val salonIdDomain = SalonId.of(salonId)

        // Verify salon exists
        if (!salonRepository.existsById(salonIdDomain)) {
            throw ResourceNotFoundException("Salon not found with ID: $salonId")
        }

        // Check if preferences already exist for this salon
        val existingPreferences = salonWebPreferencesRepository.findBySalonId(salonIdDomain)

        // If preferences exist, use the existing ID to prevent duplicate salon_id constraint violation
        if (existingPreferences != null) {
            logger.info("Updating existing web preferences with ID: {} for salon: {}", existingPreferences.id.value, salonId)
            preferences.id = existingPreferences.id.value
            preferences.salonId = salonId
        } else {
            logger.info("Creating new web preferences for salon: {}", salonId)
            // For new preferences, ensure salonId is set correctly
            preferences.salonId = salonId
        }

        // Convert JPA entity to domain model
        val domainPreferences = convertEntityToDomain(preferences, salonIdDomain)

        // Save using domain repository
        val savedDomain = salonWebPreferencesRepository.save(domainPreferences)

        // Convert back to entity for return
        return convertDomainToEntity(savedDomain)
    }

    /**
     * Check if web preferences exist for a salon (legacy method)
     */
    @Transactional(readOnly = true)
    fun hasWebPreferences(salonId: String): Boolean {
        logger.info("Checking web preferences existence for salon: {}", salonId)

        val salonIdDomain = SalonId.of(salonId)
        return salonWebPreferencesRepository.existsBySalonId(salonIdDomain)
    }

    /**
     * Delete web preferences for a salon (legacy method)
     */
    @Transactional
    fun deleteWebPreferences(salonId: String) {
        logger.info("Deleting web preferences for salon: {}", salonId)

        val salonIdDomain = SalonId.of(salonId)

        // Verify salon exists
        if (!salonRepository.existsById(salonIdDomain)) {
            throw ResourceNotFoundException("Salon not found with ID: $salonId")
        }

        salonWebPreferencesRepository.deleteBySalonId(salonIdDomain)
    }

    /**
     * Get web preferences by booking website URL (for public access)
     * Returns Optional for backward compatibility with controller
     */
    @Transactional(readOnly = true)
    fun getWebPreferencesByBookingUrl(bookingUrl: String): Optional<ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences> {
        logger.info("Getting web preferences for booking URL: {}", bookingUrl)

        val domainPreferences = salonWebPreferencesRepository.findByBookingUrl(bookingUrl)
            ?: return Optional.empty()

        // Only return active preferences
        if (!domainPreferences.isActive) {
            logger.warn("Web preferences found but not active for booking URL: {}", bookingUrl)
            return Optional.empty()
        }

        // Convert domain model to JPA entity for backward compatibility
        val entity = convertDomainToEntity(domainPreferences)
        return Optional.of(entity)
    }

    // Helper methods for conversion between domain and entity models

    /**
     * Convert domain model to JPA entity for backward compatibility
     */
    private fun convertDomainToEntity(domain: SalonWebPreferences): ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences {
        val entity = ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences()
        entity.id = domain.id.value
        entity.salonId = domain.salonId.value
        entity.bookingWebsiteUrl = domain.bookingWebsiteUrl
        entity.businessName = domain.businessName
        entity.businessDescription = domain.businessDescription
        entity.businessAddress = domain.businessAddress
        entity.contactPhone = domain.contactPhone
        entity.contactEmail = domain.contactEmail
        entity.facebookLink = domain.facebookLink
        entity.instagramLink = domain.instagramLink
        entity.tiktokLink = domain.tiktokLink
        entity.logoUrl = domain.logoUrl
        entity.primaryColor = domain.primaryColor
        entity.isActive = domain.isActive
        entity.createdAt = domain.createdAt
        entity.updatedAt = domain.updatedAt

        // Convert enums
        entity.cancellationPolicy = when (domain.cancellationPolicy) {
            CancellationPolicy.HOURS_24 -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.CancellationPolicy.HOURS_24
            CancellationPolicy.HOURS_48 -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.CancellationPolicy.HOURS_48
            CancellationPolicy.HOURS_72 -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.CancellationPolicy.HOURS_72
            CancellationPolicy.NO_CHANGES -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.CancellationPolicy.NO_CHANGES
        }

        entity.bookingAcceptance = when (domain.bookingAcceptance) {
            BookingAcceptance.AUTOMATIC -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.BookingAcceptance.AUTOMATIC
            BookingAcceptance.MANUAL -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.BookingAcceptance.MANUAL
        }

        // Convert online booking settings
        entity.onlineBookingEnabled = domain.onlineBookingEnabled

        // Convert available service IDs to JSON
        if (domain.availableServiceIds.isNotEmpty()) {
            entity.availableServiceIds = objectMapper.writeValueAsString(domain.availableServiceIds)
        } else {
            entity.availableServiceIds = null
        }

        // Booking password: persist only when not empty
        entity.bookingPassword = if (domain.bookingPassword.isNotEmpty()) domain.bookingPassword else null

        // Convert collections to JSON
        if (domain.websitePhotos.isNotEmpty()) {
            entity.websitePhotos = objectMapper.writeValueAsString(domain.websitePhotos)
        }

        if (domain.businessHours.isNotEmpty()) {
            entity.businessHours = objectMapper.writeValueAsString(domain.businessHours)
        }

        return entity
    }

    /**
     * Convert JPA entity to domain model
     */
    private fun convertEntityToDomain(entity: ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences, salonId: SalonId): SalonWebPreferences {
        // Parse JSON fields
        val photosJson = entity.websitePhotos
        val websitePhotos = if (photosJson.isNullOrBlank()) {
            emptyList<String>()
        } else {
            try {
                objectMapper.readValue<List<String>>(photosJson)
            } catch (e: Exception) {
                emptyList()
            }
        }

        val hoursJson = entity.businessHours
        val businessHours = if (hoursJson.isNullOrBlank()) {
            emptyMap<String, Map<String, Any>>()
        } else {
            try {
                objectMapper.readValue<Map<String, Map<String, Any>>>(hoursJson)
            } catch (e: Exception) {
                emptyMap()
            }
        }

        // Parse available service IDs from JSON
        val serviceIdsJson = entity.availableServiceIds
        val availableServiceIds = if (serviceIdsJson.isNullOrBlank()) {
            emptyList<String>()
        } else {
            try {
                objectMapper.readValue<List<String>>(serviceIdsJson)
            } catch (e: Exception) {
                logger.warn("Failed to parse available service IDs from JSON: {}", serviceIdsJson, e)
                emptyList()
            }
        }

        val entityId = entity.id
        return SalonWebPreferences(
            id = if (entityId.isNullOrBlank()) SalonWebPreferencesId.generate() else SalonWebPreferencesId.of(entityId),
            salonId = salonId,
            bookingWebsiteUrl = entity.bookingWebsiteUrl ?: "",
            businessName = entity.businessName ?: "",
            businessDescription = entity.businessDescription ?: "",
            businessAddress = entity.businessAddress ?: "",
            contactPhone = entity.contactPhone ?: "",
            contactEmail = entity.contactEmail ?: "",
            facebookLink = entity.facebookLink ?: "",
            instagramLink = entity.instagramLink ?: "",
            tiktokLink = entity.tiktokLink ?: "",
            logoUrl = entity.logoUrl ?: "",
            primaryColor = entity.primaryColor ?: "#D36135",
            websitePhotos = websitePhotos,
            businessHours = businessHours,
            cancellationPolicy = when (entity.cancellationPolicy) {
                ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.CancellationPolicy.HOURS_24 -> CancellationPolicy.HOURS_24
                ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.CancellationPolicy.HOURS_48 -> CancellationPolicy.HOURS_48
                ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.CancellationPolicy.HOURS_72 -> CancellationPolicy.HOURS_72
                ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.CancellationPolicy.NO_CHANGES -> CancellationPolicy.NO_CHANGES
                null -> CancellationPolicy.HOURS_24
            },
            bookingAcceptance = when (entity.bookingAcceptance) {
                ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.BookingAcceptance.AUTOMATIC -> BookingAcceptance.AUTOMATIC
                ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.BookingAcceptance.MANUAL -> BookingAcceptance.MANUAL
                null -> BookingAcceptance.AUTOMATIC
            },
            onlineBookingEnabled = entity.onlineBookingEnabled ?: true,
            availableServiceIds = availableServiceIds,
            bookingPassword = entity.bookingPassword ?: "",
            isActive = entity.isActive ?: true,
            createdAt = entity.createdAt ?: LocalDateTime.now(),
            updatedAt = entity.updatedAt ?: LocalDateTime.now()
        )
    }

    /**
     * Create default web preferences for a salon
     */
    private fun createDefaultPreferences(salonId: SalonId): SalonWebPreferences {
        val defaultPreferences = SalonWebPreferences(
            id = SalonWebPreferencesId.generate(),
            salonId = salonId,
            bookingWebsiteUrl = "",
            businessName = "",
            businessDescription = "",
            businessAddress = "",
            contactPhone = "",
            contactEmail = "",
            facebookLink = "",
            instagramLink = "",
            tiktokLink = "",
            websitePhotos = emptyList(),
            businessHours = emptyMap(),
            cancellationPolicy = CancellationPolicy.HOURS_24,
            bookingAcceptance = BookingAcceptance.AUTOMATIC,
            onlineBookingEnabled = true,
            availableServiceIds = emptyList(),
            bookingPassword = "",
            isActive = true,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )

        return salonWebPreferencesRepository.save(defaultPreferences)
    }
}
