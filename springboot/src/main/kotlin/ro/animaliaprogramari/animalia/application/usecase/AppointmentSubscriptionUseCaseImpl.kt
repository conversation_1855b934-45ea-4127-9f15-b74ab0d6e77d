package ro.animaliaprogramari.animalia.application.usecase

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.repository.*
import ro.animaliaprogramari.animalia.application.port.outbound.AppointmentRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SalonServiceRepository
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import java.math.BigDecimal
import java.time.LocalDate

/**
 * Implementation of appointment subscription use case
 */
@Service
@Transactional
class AppointmentSubscriptionUseCaseImpl(
    private val appointmentSubscriptionRepository: AppointmentSubscriptionRepository,
    private val appointmentRepository: AppointmentRepository,
    private val salonServiceRepository: SalonServiceRepository,
) : AppointmentSubscriptionUseCase {

    private val logger = LoggerFactory.getLogger(AppointmentSubscriptionUseCaseImpl::class.java)

    override fun createSubscription(
        appointment: Appointment,
        frequency: Int,
        period: RecurrencePeriod,
        totalRepetitions: Int,
        paymentModel: PaymentModel,
        discountPercentage: BigDecimal?
    ): AppointmentSubscription {
        logger.info("Creating appointment subscription for appointment: ${appointment.id.value}")

        // Calculate appointment price from services
        val appointmentPrice = appointment.serviceIds
            .mapNotNull { serviceId -> salonServiceRepository.findById(serviceId) }
            .sumOf { service -> service.basePrice.amount }

        val subscription = AppointmentSubscription.create(
            clientId = appointment.clientId,
            salonId = appointment.salonId,
            originalAppointmentId = appointment.id,
            staffId = appointment.staffId,
            serviceIds = appointment.serviceIds,
            frequency = frequency,
            period = period,
            totalRepetitions = totalRepetitions,
            startTime = appointment.startTime,
            endTime = appointment.endTime,
            notes = appointment.notes,
            paymentModel = paymentModel,
            discountPercentage = discountPercentage,
            appointmentPrice = appointmentPrice
        )

        val savedSubscription = appointmentSubscriptionRepository.save(subscription)
        logger.info("Created appointment subscription: ${savedSubscription.id.value}")

        return savedSubscription
    }

    override fun updateSubscription(command: UpdateAppointmentSubscriptionCommand): AppointmentSubscription {
        logger.info("Updating appointment subscription: ${command.subscriptionId.value}")

        val subscription = getSubscription(command.subscriptionId, command.salonId)
            ?: throw EntityNotFoundException("AppointmentSubscription", command.subscriptionId.value)

        var updatedSubscription = subscription

        // Update timing if provided
        if (command.newStartTime != null && command.newEndTime != null) {
            updatedSubscription = updatedSubscription.updateTiming(command.newStartTime, command.newEndTime)

            // Update future appointments with new timing
            updateFutureAppointmentTiming(subscription, command.newStartTime, command.newEndTime)
        }

        // Update repetitions if provided
        if (command.newTotalRepetitions != null) {
            val oldTotal = updatedSubscription.totalRepetitions
            updatedSubscription = updatedSubscription.updateRepetitions(command.newTotalRepetitions)

            // Cancel excess appointments if repetitions were reduced
            if (command.newTotalRepetitions < oldTotal) {
                cancelExcessAppointments(subscription, command.newTotalRepetitions)
            }
        }

        // Update services if provided
        if (command.newServiceIds != null) {
            updatedSubscription = updatedSubscription.copy(
                serviceIds = command.newServiceIds,
                updatedAt = java.time.LocalDateTime.now()
            )

            // Update future appointments with new services
            updateFutureAppointmentServices(subscription, command.newServiceIds)
        }

        // Update notes if provided
        if (command.newNotes != null) {
            updatedSubscription = updatedSubscription.copy(
                notes = command.newNotes,
                updatedAt = java.time.LocalDateTime.now()
            )
        }

        val savedSubscription = appointmentSubscriptionRepository.save(updatedSubscription)
        logger.info("Updated appointment subscription: ${savedSubscription.id.value}")

        return savedSubscription
    }

    override fun cancelSubscription(command: CancelAppointmentSubscriptionCommand): AppointmentSubscription {
        logger.info("Cancelling appointment subscription: ${command.subscriptionId.value}")

        val subscription = getSubscription(command.subscriptionId, command.salonId)
            ?: throw EntityNotFoundException("AppointmentSubscription", command.subscriptionId.value)

        val cancelledSubscription = subscription.cancel()

        if (command.cancelFutureAppointments) {
            cancelFutureAppointments(subscription)
        }

        val savedSubscription = appointmentSubscriptionRepository.save(cancelledSubscription)
        logger.info("Cancelled appointment subscription: ${savedSubscription.id.value}")

        return savedSubscription
    }

    override fun pauseSubscription(command: PauseAppointmentSubscriptionCommand): AppointmentSubscription {
        logger.info("Pausing appointment subscription: ${command.subscriptionId.value}")

        val subscription = getSubscription(command.subscriptionId, command.salonId)
            ?: throw EntityNotFoundException("AppointmentSubscription", command.subscriptionId.value)

        val pausedSubscription = subscription.pause()
        val savedSubscription = appointmentSubscriptionRepository.save(pausedSubscription)

        logger.info("Paused appointment subscription: ${savedSubscription.id.value}")
        return savedSubscription
    }

    override fun resumeSubscription(command: ResumeAppointmentSubscriptionCommand): AppointmentSubscription {
        logger.info("Resuming appointment subscription: ${command.subscriptionId.value}")

        val subscription = getSubscription(command.subscriptionId, command.salonId)
            ?: throw EntityNotFoundException("AppointmentSubscription", command.subscriptionId.value)

        val resumedSubscription = subscription.resume()
        val savedSubscription = appointmentSubscriptionRepository.save(resumedSubscription)

        logger.info("Resumed appointment subscription: ${savedSubscription.id.value}")
        return savedSubscription
    }

    @Transactional(readOnly = true)
    override fun getClientSubscriptions(clientId: ClientId, salonId: SalonId): List<AppointmentSubscription> {
        return appointmentSubscriptionRepository.findByClientIdAndSalonId(clientId, salonId)
    }

    @Transactional(readOnly = true)
    override fun getSubscription(subscriptionId: AppointmentSubscriptionId, salonId: SalonId): AppointmentSubscription? {
        return appointmentSubscriptionRepository.findByIdAndSalonId(subscriptionId, salonId)
    }

    override fun processSubscriptionForNextAppointment(
        subscriptionId: AppointmentSubscriptionId,
        lastAppointmentDate: LocalDate
    ): Appointment? {
        val subscription = appointmentSubscriptionRepository.findById(subscriptionId)
            ?: return null

        if (subscription.status != AppointmentSubscriptionStatus.ACTIVE || subscription.remainingRepetitions <= 0) {
            return null
        }

        val nextDate = subscription.getNextAppointmentDate(lastAppointmentDate)
        val services = salonServiceRepository.findByIds(subscription.serviceIds)

        val nextAppointment = Appointment.create(
            salonId = subscription.salonId,
            clientId = subscription.clientId,
            petId = PetId.generate(), // This should be retrieved from the original appointment
            staffId = subscription.staffId,
            appointmentDate = nextDate,
            startTime = subscription.startTime,
            endTime = subscription.endTime,
            salonServices = services,
            notes = subscription.notes,
        )

        val savedAppointment = appointmentRepository.save(nextAppointment)

        // Update subscription
        val updatedSubscription = subscription.decrementRepetitions()
        appointmentSubscriptionRepository.save(updatedSubscription)

        return savedAppointment
    }

    override fun getAffectedAppointments(
        subscriptionId: AppointmentSubscriptionId,
        modificationType: SubscriptionModificationType
    ): List<Appointment> {
        val subscription = appointmentSubscriptionRepository.findById(subscriptionId)
            ?: return emptyList()

        // Get future appointments related to this subscription
        return appointmentRepository.findFutureAppointmentsByClientAndStaff(
            subscription.clientId,
            subscription.staffId,
            LocalDate.now()
        ).filter { appointment ->
            // Filter appointments that match the subscription pattern
            appointment.serviceIds == subscription.serviceIds &&
            appointment.startTime == subscription.startTime &&
            appointment.endTime == subscription.endTime
        }
    }

    private fun updateFutureAppointmentTiming(
        subscription: AppointmentSubscription,
        newStartTime: java.time.LocalTime,
        newEndTime: java.time.LocalTime
    ) {
        val futureAppointments = getAffectedAppointments(subscription.id, SubscriptionModificationType.TIME_CHANGE)

        futureAppointments.forEach { appointment ->
            val updatedAppointment = appointment.copy(
                startTime = newStartTime,
                endTime = newEndTime,
                updatedAt = java.time.LocalDateTime.now()
            )
            appointmentRepository.save(updatedAppointment)
        }

        logger.info("Updated timing for ${futureAppointments.size} future appointments")
    }

    private fun updateFutureAppointmentServices(
        subscription: AppointmentSubscription,
        newServiceIds: List<ServiceId>
    ) {
        val futureAppointments = getAffectedAppointments(subscription.id, SubscriptionModificationType.SERVICE_CHANGE)
        val services = salonServiceRepository.findByIds(newServiceIds)

        futureAppointments.forEach { appointment ->
            val totalPrice = services.fold(Money.ZERO) { total: Money, service ->
                total.add(service.basePrice)
            }
            val updatedAppointment = appointment.copy(
                serviceIds = newServiceIds,
                totalPrice = totalPrice,
                updatedAt = java.time.LocalDateTime.now()
            )
            appointmentRepository.save(updatedAppointment)
        }

        logger.info("Updated services for ${futureAppointments.size} future appointments")
    }

    private fun cancelExcessAppointments(subscription: AppointmentSubscription, newTotalRepetitions: Int) {
        val futureAppointments = getAffectedAppointments(subscription.id, SubscriptionModificationType.REPETITION_REDUCTION)
        val completedRepetitions = subscription.totalRepetitions - subscription.remainingRepetitions
        val appointmentsToCancel = subscription.totalRepetitions - newTotalRepetitions

        if (appointmentsToCancel > 0) {
            val sortedAppointments = futureAppointments.sortedBy { it.appointmentDate }
            val toCancel = sortedAppointments.takeLast(appointmentsToCancel)

            toCancel.forEach { appointment ->
                val cancelledAppointment = appointment.updateStatus(AppointmentStatus.CANCELLED)
                appointmentRepository.save(cancelledAppointment)
            }

            logger.info("Cancelled ${toCancel.size} excess appointments due to repetition reduction")
        }
    }

    private fun cancelFutureAppointments(subscription: AppointmentSubscription) {
        // Get all appointments directly linked to this subscription
        val subscriptionAppointments = appointmentRepository.findBySubscriptionIdAndSalonId(
            subscription.id,
            subscription.salonId
        )

        // Filter to only future appointments that are not already cancelled
        val futureAppointments = subscriptionAppointments.filter { appointment ->
            appointment.appointmentDate >= LocalDate.now() &&
            appointment.status != AppointmentStatus.CANCELLED
        }

        futureAppointments.forEach { appointment ->
            val cancelledAppointment = appointment.updateStatus(AppointmentStatus.CANCELLED)
            appointmentRepository.save(cancelledAppointment)
        }

        logger.info("Cancelled ${futureAppointments.size} future appointments for subscription cancellation")
    }

    override fun updateRemainingRepetitions(
        subscriptionId: AppointmentSubscriptionId,
        remainingRepetitions: Int
    ): AppointmentSubscription {
        logger.info("Updating remaining repetitions for subscription: ${subscriptionId.value} to $remainingRepetitions")

        val subscription = appointmentSubscriptionRepository.findById(subscriptionId)
            ?: throw EntityNotFoundException("Appointment subscription not found: ${subscriptionId.value}")

        val updatedSubscription = subscription.copy(
            remainingRepetitions = remainingRepetitions,
            updatedAt = java.time.LocalDateTime.now()
        )

        val savedSubscription = appointmentSubscriptionRepository.save(updatedSubscription)
        logger.info("Updated subscription ${subscriptionId.value}: remaining repetitions set to $remainingRepetitions")

        return savedSubscription
    }

    @Transactional(readOnly = true)
    override fun getSubscriptionAppointments(
        subscriptionId: AppointmentSubscriptionId,
        salonId: SalonId
    ): List<Appointment> {
        logger.info("Getting appointments for subscription: ${subscriptionId.value}")

        // Verify subscription exists and belongs to salon
        val subscription = getSubscription(subscriptionId, salonId)
            ?: throw EntityNotFoundException("AppointmentSubscription", subscriptionId.value)

        return appointmentRepository.findBySubscriptionIdAndSalonId(subscriptionId, salonId)
    }

    @Transactional(readOnly = true)
    override fun getSubscriptionByAppointmentId(
        appointmentId: AppointmentId,
        salonId: SalonId
    ): AppointmentSubscription? {
        logger.info("Getting subscription for appointment: ${appointmentId.value}")

        val subscriptionId = appointmentRepository.findSubscriptionIdByAppointmentId(appointmentId)
            ?: return null

        return getSubscription(subscriptionId, salonId)
    }
}
