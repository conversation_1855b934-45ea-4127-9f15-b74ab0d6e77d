package ro.animaliaprogramari.animalia.application.usecase

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.application.port.outbound.AppointmentRepository
import ro.animaliaprogramari.animalia.application.port.outbound.BlockTimeRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SalonServiceRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffWorkingHoursRepository
import ro.animaliaprogramari.animalia.application.port.outbound.WorkingHoursRepository
import ro.animaliaprogramari.animalia.application.query.AvailableTimeSlotsResponse
import ro.animaliaprogramari.animalia.application.query.GetAvailableTimeSlotsQuery
import ro.animaliaprogramari.animalia.application.query.TimeSlotInfo
import ro.animaliaprogramari.animalia.domain.model.MyDuration
import ro.animaliaprogramari.animalia.domain.model.StaffId
import ro.animaliaprogramari.animalia.domain.model.StaffWorkingHoursSettings
import ro.animaliaprogramari.animalia.domain.service.SchedulingConflictService
import java.time.LocalTime
import java.time.ZoneId

/**
 * Use case for querying available time slots for online booking
 * Searches across all staff members to find available slots
 */
interface OnlineBookingQueryUseCase {
    /**
     * Get available time slots for the given date and services
     * Returns slots with 30-minute granularity
     */
    fun getAvailableTimeSlots(query: GetAvailableTimeSlotsQuery): AvailableTimeSlotsResponse
}

@Service
class OnlineBookingQueryUseCaseImpl(
    private val staffRepository: StaffRepository,
    private val workingHoursRepository: WorkingHoursRepository,
    private val staffWorkingHoursRepository: StaffWorkingHoursRepository,
    private val appointmentRepository: AppointmentRepository,
    private val blockTimeRepository: BlockTimeRepository,
    private val conflictService: SchedulingConflictService,
    private val serviceRepository: SalonServiceRepository,
) : OnlineBookingQueryUseCase {
    private val logger = LoggerFactory.getLogger(OnlineBookingQueryUseCaseImpl::class.java)

    companion object {
        private const val SLOT_DURATION_MINUTES = 30

        /**
         * All possible 30-minute time slots for a day (47 slots total)
         * Note: We don't include 23:30-00:00 because it crosses midnight and causes validation issues
         */
        private val ALL_DAY_TIME_SLOTS: List<Pair<LocalTime, LocalTime>> = listOf(
            LocalTime.of(0, 0) to LocalTime.of(0, 30),
            LocalTime.of(0, 30) to LocalTime.of(1, 0),
            LocalTime.of(1, 0) to LocalTime.of(1, 30),
            LocalTime.of(1, 30) to LocalTime.of(2, 0),
            LocalTime.of(2, 0) to LocalTime.of(2, 30),
            LocalTime.of(2, 30) to LocalTime.of(3, 0),
            LocalTime.of(3, 0) to LocalTime.of(3, 30),
            LocalTime.of(3, 30) to LocalTime.of(4, 0),
            LocalTime.of(4, 0) to LocalTime.of(4, 30),
            LocalTime.of(4, 30) to LocalTime.of(5, 0),
            LocalTime.of(5, 0) to LocalTime.of(5, 30),
            LocalTime.of(5, 30) to LocalTime.of(6, 0),
            LocalTime.of(6, 0) to LocalTime.of(6, 30),
            LocalTime.of(6, 30) to LocalTime.of(7, 0),
            LocalTime.of(7, 0) to LocalTime.of(7, 30),
            LocalTime.of(7, 30) to LocalTime.of(8, 0),
            LocalTime.of(8, 0) to LocalTime.of(8, 30),
            LocalTime.of(8, 30) to LocalTime.of(9, 0),
            LocalTime.of(9, 0) to LocalTime.of(9, 30),
            LocalTime.of(9, 30) to LocalTime.of(10, 0),
            LocalTime.of(10, 0) to LocalTime.of(10, 30),
            LocalTime.of(10, 30) to LocalTime.of(11, 0),
            LocalTime.of(11, 0) to LocalTime.of(11, 30),
            LocalTime.of(11, 30) to LocalTime.of(12, 0),
            LocalTime.of(12, 0) to LocalTime.of(12, 30),
            LocalTime.of(12, 30) to LocalTime.of(13, 0),
            LocalTime.of(13, 0) to LocalTime.of(13, 30),
            LocalTime.of(13, 30) to LocalTime.of(14, 0),
            LocalTime.of(14, 0) to LocalTime.of(14, 30),
            LocalTime.of(14, 30) to LocalTime.of(15, 0),
            LocalTime.of(15, 0) to LocalTime.of(15, 30),
            LocalTime.of(15, 30) to LocalTime.of(16, 0),
            LocalTime.of(16, 0) to LocalTime.of(16, 30),
            LocalTime.of(16, 30) to LocalTime.of(17, 0),
            LocalTime.of(17, 0) to LocalTime.of(17, 30),
            LocalTime.of(17, 30) to LocalTime.of(18, 0),
            LocalTime.of(18, 0) to LocalTime.of(18, 30),
            LocalTime.of(18, 30) to LocalTime.of(19, 0),
            LocalTime.of(19, 0) to LocalTime.of(19, 30),
            LocalTime.of(19, 30) to LocalTime.of(20, 0),
            LocalTime.of(20, 0) to LocalTime.of(20, 30),
            LocalTime.of(20, 30) to LocalTime.of(21, 0),
            LocalTime.of(21, 0) to LocalTime.of(21, 30),
            LocalTime.of(21, 30) to LocalTime.of(22, 0),
            LocalTime.of(22, 0) to LocalTime.of(22, 30),
            LocalTime.of(22, 30) to LocalTime.of(23, 0),
            LocalTime.of(23, 0) to LocalTime.of(23, 30),
            // Note: 23:30-00:00 is excluded because it crosses midnight
        )
    }

    override fun getAvailableTimeSlots(query: GetAvailableTimeSlotsQuery): AvailableTimeSlotsResponse {
        logger.info("Getting available time slots for salon: ${query.salonId.value}, date: ${query.date}")

        // Load salon working hours
        val salonHours = workingHoursRepository.findBySalonId(query.salonId)

        // Check if salon is open on this date
        if (!salonHours.isOpenOn(query.date)) {
            logger.info("Salon is closed on ${query.date}")
            return AvailableTimeSlotsResponse(
                date = query.date,
                slots = emptyList(),
            )
        }

        // Get all active staff for the salon
        val allStaff = staffRepository.findBySalonId(query.salonId)
        if (allStaff.isEmpty()) {
            logger.warn("No staff found for salon: ${query.salonId.value}")
            return AvailableTimeSlotsResponse(
                date = query.date,
                slots = emptyList(),
            )
        }

        // Load services to calculate total duration
        val services = serviceRepository.findByIds(query.serviceIds)
        if (services.isEmpty()) {
            logger.warn("No services found for IDs: ${query.serviceIds}")
            return AvailableTimeSlotsResponse(
                date = query.date,
                slots = emptyList(),
            )
        }

        // Calculate total duration of all selected services
        val totalDuration = services.fold(MyDuration.ofMinutes(0)) { acc, service ->
            acc.add(service.myDuration)
        }
        logger.info("Total service duration: ${totalDuration.minutes} minutes for ${services.size} services")

        // Calculate how many consecutive 30-minute slots are needed
        val requiredSlots: Int = (totalDuration.minutes + SLOT_DURATION_MINUTES - 1) / SLOT_DURATION_MINUTES
        logger.info("Required consecutive slots: $requiredSlots")

        // Use pre-calculated 30-minute time slots for the day
        val timeSlots = ALL_DAY_TIME_SLOTS

        // For each time slot, check if there are enough consecutive free slots
        val availableSlots = timeSlots.mapIndexedNotNull { index, (startTime, _) ->
            // Check if we have enough slots remaining in the day
            if (index + requiredSlots > timeSlots.size) {
                return@mapIndexedNotNull null
            }

            // Get the consecutive slots needed for this appointment
            val consecutiveSlots = timeSlots.subList(index, (index + requiredSlots).toInt())
            val endTime = consecutiveSlots.last().second

            // Find staff members available for ALL consecutive slots
            val availableStaffIds = findAvailableStaffForConsecutiveSlots(
                query = query,
                startTime = startTime,
                endTime = endTime,
                consecutiveSlots = consecutiveSlots,
                allStaff = allStaff,
                salonHours = salonHours,
            )

            if (availableStaffIds.isNotEmpty()) {
                TimeSlotInfo(
                    startTime = startTime.toString(),
                    endTime = endTime.toString(),
                    availableStaffIds = availableStaffIds,
                )
            } else {
                null
            }
        }

        logger.info("Found ${availableSlots.size} available time slots for ${query.date}")

        return AvailableTimeSlotsResponse(
            date = query.date,
            slots = availableSlots,
        )
    }

    /**
     * Find all staff members available for consecutive time slots (for appointments spanning multiple slots)
     */
    private fun findAvailableStaffForConsecutiveSlots(
        query: GetAvailableTimeSlotsQuery,
        startTime: LocalTime,
        endTime: LocalTime,
        consecutiveSlots: List<Pair<LocalTime, LocalTime>>,
        allStaff: List<ro.animaliaprogramari.animalia.domain.model.Staff>,
        salonHours: ro.animaliaprogramari.animalia.domain.model.WorkingHoursSettings,
    ): List<String> {
        val availableStaffIds = mutableListOf<String>()

        for (staff in allStaff) {
            // Check if staff is available for the entire duration (all consecutive slots)
            val isAvailableForAllSlots = consecutiveSlots.all { (slotStart, slotEnd) ->
                isStaffAvailableForSlot(
                    salonId = query.salonId,
                    staffId = staff.id,
                    date = query.date,
                    startTime = slotStart,
                    endTime = slotEnd,
                    salonHours = salonHours,
                )
            }

            if (isAvailableForAllSlots) {
                availableStaffIds.add(staff.id.value)
            }
        }

        return availableStaffIds
    }

    /**
     * Check if a specific staff member is available for a time slot
     */
    private fun isStaffAvailableForSlot(
        salonId: ro.animaliaprogramari.animalia.domain.model.SalonId,
        staffId: StaffId,
        date: java.time.LocalDate,
        startTime: LocalTime,
        endTime: LocalTime,
        salonHours: ro.animaliaprogramari.animalia.domain.model.WorkingHoursSettings,
    ): Boolean {
        // Load staff working hours
        val staffHours = staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId)
            ?: StaffWorkingHoursSettings.createDefault(staffId, salonId)

        // Get existing appointments for this staff on this date
        val existingAppointments = appointmentRepository.findBySalonIdAndStaffIdAndDate(
            salonId,
            staffId,
            date,
        )

        // Get block times for this staff on this date
        val blocks = blockTimeRepository.findOverlappingBlocks(
            salonId,
            date.atTime(startTime).atZone(ZoneId.systemDefault()),
            date.atTime(endTime).atZone(ZoneId.systemDefault()),
            setOf(staffId),
        )

        // Use the conflict service to check if the slot is available
        return conflictService.isSlotAvailable(
            date = date,
            startTime = startTime,
            endTime = endTime,
            staffId = staffId,
            salonHours = salonHours,
            staffHours = staffHours,
            appointments = existingAppointments,
            blocks = blocks,
        )
    }
}

