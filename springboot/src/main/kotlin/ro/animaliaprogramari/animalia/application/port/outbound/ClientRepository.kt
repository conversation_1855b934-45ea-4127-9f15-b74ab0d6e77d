package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Outbound port for client persistence
 * This is an interface that will be implemented by the persistence adapter
 */
interface ClientRepository {
    /**
     * Save a client
     */
    fun save(client: Client): Client

    /**
     * Save multiple clients in batch
     */
    fun saveAll(clients: List<Client>): List<Client>

    /**
     * Find a client by ID
     */
    fun findById(id: ClientId): Client?

    /**
     * Find a client by email
     */
    fun findByEmail(email: Email): Client?

    /**
     * Find all clients with optional filtering
     */
    fun findAll(
        search: String? = null,
        isActive: Boolean? = null,
        userId: UserId? = null,
        limit: Int? = null,
        offset: Int? = null,
    ): List<Client>

    /**
     * Find clients by groomer
     */
    fun findByGroomer(
        userId: UserId,
        isActive: Boolean? = null,
        limit: Int? = null,
        offset: Int? = null,
    ): List<Client>

    /**
     * Check if a client exists by ID
     */
    fun existsById(id: ClientId): Bo<PERSON>an

    /**
     * Check if a client exists by email
     */
    fun existsByEmail(email: Email): Boolean

    /**
     * Delete a client by ID
     */
    fun deleteById(id: ClientId): Boolean

    /**
     * Count total clients with optional filtering
     */
    fun count(
        search: String? = null,
        isActive: Boolean? = null,
    ): Long

    /**
     * Find clients by salon ID (for salon-scoped access)
     */
    fun findBySalonId(
        salonId: SalonId,
        isActive: Boolean? = null,
        limit: Int? = null,
        offset: Int? = null,
        search: String? = null,
    ): List<Client>

    /**
     * Count total clients by salon ID with optional filters
     * Returns the total count regardless of pagination
     */
    fun countBySalonId(
        salonId: SalonId,
        search: String? = null,
        isActive: Boolean? = null,
    ): Long

    /**
     * Find clients by salon IDs (for salon-scoped access)
     */
    fun findBySalonIds(
        salonIds: List<SalonId>,
        search: String? = null,
        isActive: Boolean? = null,
        limit: Int? = null,
        offset: Int? = null,
    ): List<Client>

    /**
     * Count clients by salon IDs
     */
    fun countBySalonIds(
        salonIds: List<SalonId>,
        search: String? = null,
        isActive: Boolean? = null,
    ): Long

    /**
     * Find a client by phone number and salon ID
     */
    fun findByPhoneAndSalonId(phone: PhoneNumber, salonId: SalonId): Client?
}
