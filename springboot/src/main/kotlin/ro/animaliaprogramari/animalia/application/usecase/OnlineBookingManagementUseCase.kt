package ro.animaliaprogramari.animalia.application.usecase

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.application.command.ScheduleAppointmentCommand
import ro.animaliaprogramari.animalia.application.port.inbound.AppointmentManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.*
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.Appointment
import ro.animaliaprogramari.animalia.domain.model.AppointmentId
import ro.animaliaprogramari.animalia.domain.model.AppointmentStatus
import ro.animaliaprogramari.animalia.domain.model.BookingAcceptance
import ro.animaliaprogramari.animalia.domain.model.Client
import ro.animaliaprogramari.animalia.domain.model.ClientId
import ro.animaliaprogramari.animalia.domain.model.Money
import ro.animaliaprogramari.animalia.domain.model.MyDuration
import ro.animaliaprogramari.animalia.domain.model.Pet
import ro.animaliaprogramari.animalia.domain.model.PetId
import ro.animaliaprogramari.animalia.domain.model.PhoneNumber
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.ServiceId
import ro.animaliaprogramari.animalia.domain.model.StaffId
import ro.animaliaprogramari.animalia.domain.model.UserId
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.format.DateTimeFormatter

/**
 * Command to create a proto-appointment for online booking
 */
data class CreateProtoAppointmentCommand(
    val salonId: SalonId,
    val date: LocalDate,
    val startTime: LocalTime,
    val endTime: LocalTime,
    val serviceIds: List<ServiceId>,
)

/**
 * Result of creating a proto-appointment
 */
data class ProtoAppointmentResult(
    val appointmentId: AppointmentId,
    val expiresAt: String, // ISO-8601 timestamp
)

/**
 * Command to finalize an online booking
 */
data class FinalizeBookingCommand(
    val appointmentId: AppointmentId,
    val clientPhone: String,
    val petName: String,
    val petSpecies: String,
    val petBreed: String,
    val petSize: String,
    val clientName: String?,
    val notes: String?,
    val password: String?,
)

/**
 * Result of finalizing a booking
 */
data class BookingResult(
    val appointmentId: String,
    val status: AppointmentStatus,
    val isAutoConfirmed: Boolean,
    val message: String,
)

/**
 * Use case for managing online bookings
 */
interface OnlineBookingManagementUseCase {
    /**
     * Create a proto-appointment to reserve a time slot
     */
    fun createProtoAppointment(command: CreateProtoAppointmentCommand): ProtoAppointmentResult

    /**
     * Finalize an online booking by filling in client and pet information
     */
    fun finalizeBooking(command: FinalizeBookingCommand): BookingResult

    /**
     * Clean up expired proto-appointments
     */
    fun cleanupExpiredProtoAppointments(): Int
}

@Service
class OnlineBookingManagementUseCaseImpl(
    private val salonRepository: SalonRepository,
    private val salonWebPreferencesRepository: SalonWebPreferencesRepository,
    private val staffRepository: StaffRepository,
    private val clientRepository: ClientRepository,
    private val petRepository: PetRepository,
    private val salonServiceRepository: SalonServiceRepository,
    private val appointmentRepository: AppointmentRepository,
    private val appointmentManagementUseCase: AppointmentManagementUseCase,
    private val eventPublisher: DomainEventPublisher,
) : OnlineBookingManagementUseCase {
    private val logger = LoggerFactory.getLogger(OnlineBookingManagementUseCaseImpl::class.java)

    companion object {
        private const val PLACEHOLDER_EXPIRATION_MINUTES = 5L // Proto-appointments expire after 5 minutes
        private val dateFormatter = DateTimeFormatter.ofPattern("dd MMM yyyy")
        private val timeFormatter = DateTimeFormatter.ofPattern("HH:mm")

        // Temporary client/pet IDs for proto-appointments
        private val PLACEHOLDER_CLIENT_ID = ClientId.of("placeholder-client")
        private val PLACEHOLDER_PET_ID = PetId.of("placeholder-pet")
    }

    @Transactional
    override fun createProtoAppointment(command: CreateProtoAppointmentCommand): ProtoAppointmentResult {
        logger.info("Creating proto-appointment for salon: ${command.salonId.value}, date: ${command.date}")

        // Validate salon exists and online booking is enabled
        val salon = salonRepository.findById(command.salonId)
            ?: throw EntityNotFoundException("Salon not found: ${command.salonId.value}")

        val webPreferences = salonWebPreferencesRepository.findBySalonId(command.salonId)
            ?: throw BusinessRuleViolationException("Online booking is not configured for this salon")

        if (!webPreferences.onlineBookingEnabled) {
            throw BusinessRuleViolationException("Online booking is not enabled for this salon")
        }

        // Validate services are available for online booking
        command.serviceIds.forEach { serviceId ->
            if (!webPreferences.availableServiceIds.contains(serviceId.value)) {
                throw BusinessRuleViolationException("Service ${serviceId.value} is not available for online booking")
            }
        }

        // Load services to calculate total price and duration
        val services = salonServiceRepository.findByIds(command.serviceIds)
        if (services.isEmpty()) {
            throw BusinessRuleViolationException("No valid services found")
        }

        // Find an available staff member for this time slot
        val availableStaff = findAvailableStaffForProtoAppointment(command)
            ?: throw BusinessRuleViolationException("No staff available for the selected time slot")

        // Create proto-appointment (visible in calendar with PLACEHOLDER status)
        val expiresAt = LocalDateTime.now().plusMinutes(PLACEHOLDER_EXPIRATION_MINUTES)
        val protoAppointment = Appointment(
            id = AppointmentId.generate(),
            salonId = command.salonId,
            clientId = PLACEHOLDER_CLIENT_ID,
            petId = PLACEHOLDER_PET_ID,
            staffId = availableStaff.id,
            appointmentDate = command.date,
            startTime = command.startTime,
            endTime = command.endTime,
            status = AppointmentStatus.PLACEHOLDER,
            serviceIds = command.serviceIds,
            totalPrice = services.fold(Money.ZERO) { total, service -> total.add(service.basePrice) },
            totalMyDuration = services.fold(MyDuration.ofMinutes(0)) { total, service -> total.add(service.myDuration) },
            notes = "Online booking placeholder - expires at $expiresAt",
            isRecurring = false,
        )

        val savedProtoAppointment = appointmentRepository.save(protoAppointment)
        logger.info("Proto-appointment created: ${savedProtoAppointment.id.value}, staff: ${availableStaff.id.value}, expires at: $expiresAt")

        // Publish ProtoAppointmentCreatedEvent for push notification
        try {
            val event = ro.animaliaprogramari.animalia.domain.event.appointment.ProtoAppointmentCreatedEvent(
                eventId = java.util.UUID.randomUUID().toString(),
                occurredAt = LocalDateTime.now(),
                aggregateId = savedProtoAppointment.id.value,
                appointmentId = savedProtoAppointment.id,
                staffId = availableStaff.id,
                salonId = command.salonId,
                appointmentDate = command.date,
                startTime = command.startTime,
                endTime = command.endTime,
                serviceIds = command.serviceIds,
                expiresAt = expiresAt,
            )
            eventPublisher.publish(event)
            logger.info("📢 Published ProtoAppointmentCreatedEvent for proto-appointment: ${savedProtoAppointment.id.value}")
        } catch (e: Exception) {
            logger.error("❌ Failed to publish ProtoAppointmentCreatedEvent", e)
            // Don't throw - proto-appointment was created successfully
        }

        return ProtoAppointmentResult(
            appointmentId = savedProtoAppointment.id,
            expiresAt = expiresAt.toString(),
        )
    }

    @Transactional
    override fun finalizeBooking(command: FinalizeBookingCommand): BookingResult {
        logger.info("Finalizing booking for proto-appointment: ${command.appointmentId.value}")

        // Load and validate proto-appointment
        val protoAppointment = appointmentRepository.findById(command.appointmentId)
            ?: throw EntityNotFoundException("Proto-appointment not found or expired")

        if (protoAppointment.status != AppointmentStatus.PLACEHOLDER) {
            throw BusinessRuleViolationException("Invalid appointment status. Expected PLACEHOLDER but got ${protoAppointment.status}")
        }

        // Check if proto-appointment has expired (older than 5 minutes)
        val expirationThreshold = LocalDateTime.now().minusMinutes(PLACEHOLDER_EXPIRATION_MINUTES)
        if (protoAppointment.createdAt.isBefore(expirationThreshold)) {
            appointmentRepository.deleteById(command.appointmentId)
            throw BusinessRuleViolationException("Proto-appointment has expired. Please select a new time slot.")
        }

        // Load salon and web preferences
        val salon = salonRepository.findById(protoAppointment.salonId)
            ?: throw EntityNotFoundException("Salon not found")

        val webPreferences = salonWebPreferencesRepository.findBySalonId(protoAppointment.salonId)
            ?: throw BusinessRuleViolationException("Online booking is not configured for this salon")

        // Validate password if required
        if (webPreferences.bookingPassword.isNotBlank()) {
            if (command.password.isNullOrBlank() || command.password != webPreferences.bookingPassword) {
                throw BusinessRuleViolationException("Invalid booking password")
            }
        }

        // Create or find client by phone number
        val phoneNumber = PhoneNumber.of(command.clientPhone)
        val client = findOrCreateClient(
            phone = phoneNumber,
            name = command.clientName,
            salonId = protoAppointment.salonId,
        )

        // Create pet
        val pet = Pet.create(
            clientId = client.id,
            name = command.petName,
            breed = command.petBreed,
            petSize = command.petSize,
            species = command.petSpecies,
        )
        val savedPet = petRepository.save(pet)

        // Determine appointment status based on booking acceptance setting
        val isAutoConfirmed = webPreferences.bookingAcceptance == BookingAcceptance.AUTOMATIC
        val finalStatus = if (isAutoConfirmed) AppointmentStatus.SCHEDULED else AppointmentStatus.REQUESTED

        // Update the proto-appointment with real client/pet information and new status
        val finalAppointment = protoAppointment.copy(
            clientId = client.id,
            petId = savedPet.id,
            status = finalStatus,
            notes = command.notes,
        )
        val savedAppointment = appointmentRepository.save(finalAppointment)

        // Publish event for push notification
        publishOnlineBookingEvent(savedAppointment, client, savedPet, isAutoConfirmed)

        val message = if (isAutoConfirmed) {
            "Your appointment is confirmed for ${protoAppointment.appointmentDate.format(dateFormatter)} at ${protoAppointment.startTime.format(timeFormatter)}"
        } else {
            "Your appointment request has been sent. Please wait for confirmation from the salon."
        }

        logger.info("Online booking finalized: ${savedAppointment.id.value}, status: ${savedAppointment.status}")

        return BookingResult(
            appointmentId = savedAppointment.id.value,
            status = savedAppointment.status,
            isAutoConfirmed = isAutoConfirmed,
            message = message,
        )
    }

    @Transactional
    override fun cleanupExpiredProtoAppointments(): Int {
        // Delete expired proto-appointments (PLACEHOLDER status older than 5 minutes)
        val expirationThreshold = LocalDateTime.now().minusMinutes(PLACEHOLDER_EXPIRATION_MINUTES)
        val protoAppointments = appointmentRepository.findByStatuses(mutableListOf(AppointmentStatus.PLACEHOLDER))
        var deletedCount = 0

        protoAppointments.forEach { protoAppointment ->
            if (protoAppointment.createdAt.isBefore(expirationThreshold)) {
                appointmentRepository.deleteById(protoAppointment.id)
                deletedCount++
                logger.debug("Deleted expired proto-appointment: ${protoAppointment.id.value}")
            }
        }

        if (deletedCount > 0) {
            logger.info("Cleaned up $deletedCount expired proto-appointments")
        }
        return deletedCount
    }

    /**
     * Find an available staff member for the proto-appointment creation command
     */
    private fun findAvailableStaffForProtoAppointment(command: CreateProtoAppointmentCommand): ro.animaliaprogramari.animalia.domain.model.Staff? {
        val allStaff = staffRepository.findBySalonId(command.salonId)
        // For simplicity, return the first active staff member
        // In a real implementation, you would check availability using the conflict service
        return allStaff.firstOrNull { it.isActive }
    }

    /**
     * Find existing client by phone or create a new one
     */
    private fun findOrCreateClient(
        phone: PhoneNumber,
        name: String?,
        salonId: SalonId,
    ): Client {
        // Try to find existing client by phone
        val existingClient = clientRepository.findByPhoneAndSalonId(phone, salonId)
        if (existingClient != null) {
            logger.info("Found existing client: ${existingClient.id.value}")
            return existingClient
        }

        // Create new client
        val clientName = name ?: "Online Client"
        val newClient = Client.create(
            name = clientName,
            phone = phone,
            secondaryPhone = null,
            salonId = salonId,
        )
        val savedClient = clientRepository.save(newClient)
        logger.info("Created new client: ${savedClient.id.value}")
        return savedClient
    }

    /**
     * Publish event for online booking notification
     */
    private fun publishOnlineBookingEvent(
        appointment: Appointment,
        client: Client,
        pet: Pet,
        isAutoConfirmed: Boolean,
    ) {
        // Get staff user ID
        val staffUserId = staffRepository.findById(appointment.staffId)?.userId
            ?: UserId.of(appointment.staffId.value)

        if (isAutoConfirmed) {
            // Publish AppointmentScheduledEvent for auto-confirmed appointments
            val event = ro.animaliaprogramari.animalia.domain.event.appointment.AppointmentScheduledEvent(
                eventId = java.util.UUID.randomUUID().toString(),
                occurredAt = java.time.LocalDateTime.now(),
                aggregateId = appointment.id.value,
                appointmentId = appointment.id,
                clientId = client.id,
                petId = pet.id,
                userId = staffUserId,
                appointmentDate = appointment.appointmentDate,
                startTime = appointment.startTime,
                endTime = appointment.endTime,
                totalPrice = appointment.totalPrice,
                salonId = appointment.salonId,
            )
            eventPublisher.publish(event)
            logger.info("Published AppointmentScheduledEvent for auto-confirmed appointment: ${appointment.id.value}")
        } else {
            // Publish AppointmentRequestedEvent for manual approval appointments
            val event = ro.animaliaprogramari.animalia.domain.event.appointment.AppointmentRequestedEvent(
                eventId = java.util.UUID.randomUUID().toString(),
                occurredAt = java.time.LocalDateTime.now(),
                aggregateId = appointment.id.value,
                appointmentId = appointment.id,
                clientId = client.id,
                petId = pet.id,
                staffId = appointment.staffId,
                salonId = appointment.salonId,
                appointmentDate = appointment.appointmentDate,
                startTime = appointment.startTime,
                endTime = appointment.endTime,
                serviceIds = appointment.serviceIds,
                clientPhone = client.phone?.value ?: "",
                clientName = client.name,
                petName = pet.name,
            )
            eventPublisher.publish(event)
            logger.info("Published AppointmentRequestedEvent for manual approval appointment: ${appointment.id.value}")
        }
    }
}

