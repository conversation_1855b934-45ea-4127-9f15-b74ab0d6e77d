package ro.animaliaprogramari.animalia.application.command

import ro.animaliaprogramari.animalia.domain.model.ClientId
import ro.animaliaprogramari.animalia.domain.model.Email
import ro.animaliaprogramari.animalia.domain.model.PhoneNumber
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.UserId

/**
 * Command to register a new client
 */
data class RegisterClientCommand(
    val name: String,
    val phone: PhoneNumber? = null,
    val secondaryPhone: PhoneNumber? = null,
    val email: Email? = null,
    val address: String? = null,
    val notes: String? = null,
)

/**
 * Command to update client information
 */
data class UpdateClientCommand(
    val clientId: ClientId,
    val name: String,
    val phone: PhoneNumber? = null,
    val secondaryPhone: PhoneNumber? = null,
    val email: Email? = null,
    val address: String? = null,
    val notes: String? = null,
)

/**
 * Command to deactivate a client
 */
data class DeactivateClientCommand(
    val clientId: ClientId,
)

/**
 * Command to activate a client
 */
data class ActivateClientCommand(
    val clientId: ClientId,
)

/**
 * Command to set primary phone number for automated SMS
 */
data class SetPrimaryPhoneCommand(
    val clientId: ClientId,
    val primaryPhone: String,
)

/**
 * Command to delete a client
 */
data class DeleteClientCommand(
    val clientId: ClientId,
    val salonId: SalonId,
    val requestedBy: UserId,
)
