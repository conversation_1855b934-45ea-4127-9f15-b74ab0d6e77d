package ro.animaliaprogramari.animalia.application.query

import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.ServiceId
import java.time.LocalDate

/**
 * Query to get available time slots for online booking
 * Searches across all staff members for the given date and services
 */
data class GetAvailableTimeSlotsQuery(
    val salonId: SalonId,
    val date: LocalDate,
    val serviceIds: List<ServiceId>,
)

/**
 * Response containing available time slots
 */
data class AvailableTimeSlotsResponse(
    val date: LocalDate,
    val slots: List<TimeSlotInfo>,
)

/**
 * Information about a single available time slot
 */
data class TimeSlotInfo(
    val startTime: String, // HH:mm format
    val endTime: String, // HH:mm format
    val availableStaffIds: List<String>, // Staff members available for this slot
)

