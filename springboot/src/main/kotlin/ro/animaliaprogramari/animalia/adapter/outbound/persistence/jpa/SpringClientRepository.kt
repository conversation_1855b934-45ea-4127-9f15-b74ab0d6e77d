package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.Client

@Repository
interface SpringClientRepository : JpaRepository<Client, String> {
    @Query(
        "SELECT DISTINCT c FROM Client c " +
            "LEFT JOIN c.pets p WITH p.isActive = true WHERE " +
            "(:search IS NULL OR :search = '' OR " +
            "LOWER(c.name) LIKE LOWER(CONCAT('%', CAST(:search AS string), '%')) OR " +
            "LOWER(c.phone) LIKE LOWER(CONCAT('%', CAST(:search AS string), '%')) OR " +
            "LOWER(c.secondaryPhone) LIKE LOWER(CONCAT('%', CAST(:search AS string), '%')) OR " +
            "LOWER(c.email) LIKE LOWER(CONCAT('%', CAST(:search AS string), '%')) OR " +
            "LOWER(p.name) LIKE LOWER(CONCAT('%', CAST(:search AS string), '%'))) AND " +
            "(:isActive IS NULL OR c.isActive = :isActive) AND " +
            "(:salonId IS NULL OR c.salonId = CAST(:salonId AS string)) " +
            "ORDER BY c.name ASC",
    )
    fun findBySearchAndActiveStatus(
        @Param("search") search: String?,
        @Param("isActive") isActive: Boolean?,
        @Param("salonId") salonId: String?,
    ): List<Client>

    @Query(
        "SELECT c FROM Client c WHERE c.salonId = :salonId AND " +
            "(:isActive IS NULL OR c.isActive = :isActive) " +
            "ORDER BY c.name ASC",
    )
    fun findBySalonId(
        @Param("salonId") salonId: String,
        @Param("isActive") isActive: Boolean?,
    ): List<Client>

    fun findByEmailIgnoreCase(email: String): Client?

    fun findByPhoneAndSalonId(phone: String, salonId: String): Client?

    /**
     * Count clients matching search and active status criteria
     */
    @Query(
        "SELECT COUNT(DISTINCT c) FROM Client c " +
            "LEFT JOIN c.pets p WITH p.isActive = true WHERE " +
            "(:search IS NULL OR :search = '' OR " +
            "LOWER(c.name) LIKE LOWER(CONCAT('%', CAST(:search AS string), '%')) OR " +
            "LOWER(c.phone) LIKE LOWER(CONCAT('%', CAST(:search AS string), '%')) OR " +
            "LOWER(c.secondaryPhone) LIKE LOWER(CONCAT('%', CAST(:search AS string), '%')) OR " +
            "LOWER(c.email) LIKE LOWER(CONCAT('%', CAST(:search AS string), '%')) OR " +
            "LOWER(p.name) LIKE LOWER(CONCAT('%', CAST(:search AS string), '%'))) AND " +
            "(:isActive IS NULL OR c.isActive = :isActive) AND " +
            "(:salonId IS NULL OR c.salonId = CAST(:salonId AS string))",
    )
    fun countBySearchAndActiveStatus(
        @Param("search") search: String?,
        @Param("isActive") isActive: Boolean?,
        @Param("salonId") salonId: String?,
    ): Long

    /**
     * Find client by phone number (for incoming messages from Twilio)
     * Returns the first matching client if multiple salons have same phone
     */
    fun findFirstByPhone(phone: String): Client?
}
