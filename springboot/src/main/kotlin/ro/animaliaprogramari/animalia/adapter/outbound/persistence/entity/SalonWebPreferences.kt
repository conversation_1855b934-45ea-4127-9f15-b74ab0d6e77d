package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.time.LocalDateTime

/**
 * Entity representing salon website management preferences
 */
@Entity
@Table(name = "salon_web_preferences")
@JsonIgnoreProperties(ignoreUnknown = true)
class SalonWebPreferences {
    // Getters and Setters
    @Id
    @Column(name = "id")
    var id: String? = null

    @Column(name = "salon_id", nullable = false, unique = true)
    @JsonProperty("salon_id")
    var salonId: String? = null

    // Basic Info
    @Column(name = "booking_website_url", length = 500)
    @JsonProperty("booking_website_url")
    var bookingWebsiteUrl: String? = null

    @Column(name = "business_name")
    @JsonProperty("business_name")
    var businessName: String? = null

    @Column(name = "business_description", columnDefinition = "TEXT")
    @JsonProperty("business_description")
    var businessDescription: String? = null

    @Column(name = "business_address", columnDefinition = "TEXT")
    @JsonProperty("business_address")
    var businessAddress: String? = null

    @Column(name = "contact_phone", length = 50)
    @JsonProperty("contact_phone")
    var contactPhone: String? = null

    @Column(name = "contact_email")
    @JsonProperty("contact_email")
    var contactEmail: String? = null

    @Column(name = "facebook_link", length = 500)
    @JsonProperty("facebook_link")
    var facebookLink: String? = null

    @Column(name = "instagram_link", length = 500)
    @JsonProperty("instagram_link")
    var instagramLink: String? = null

    @Column(name = "tiktok_link", length = 500)
    @JsonProperty("tiktok_link")
    var tiktokLink: String? = null

    // Website Customization
    @Column(name = "logo_url", length = 500)
    @JsonProperty("logo_url")
    var logoUrl: String? = null

    @Column(name = "primary_color", length = 7)
    @JsonProperty("primary_color")
    var primaryColor: String? = null

    // Website Photos (stored as JSON)
    @Column(name = "website_photos", columnDefinition = "TEXT")
    @JsonProperty("website_photos")
    var websitePhotos: String? = null // JSON array of photo URLs

    // Business Hours (stored as JSON)
    @Column(name = "business_hours", columnDefinition = "TEXT")
    @JsonProperty("business_hours")
    var businessHours: String? = null // JSON object with daily hours

    // Extra Info
    @Enumerated(EnumType.STRING)
    @Column(name = "cancellation_policy", length = 50)
    @JsonProperty("cancellation_policy")
    var cancellationPolicy: CancellationPolicy? = null

    // Booking Acceptance
    @Enumerated(EnumType.STRING)
    @Column(name = "booking_acceptance", length = 20)
    @JsonProperty("booking_acceptance")
    var bookingAcceptance: BookingAcceptance? = null

    // Online Booking Settings
    @Column(name = "online_booking_enabled", nullable = false)
    @JsonProperty("online_booking_enabled")
    var onlineBookingEnabled: Boolean? = true

    // Available Services for Online Booking (stored as JSON array of service IDs)
    @Column(name = "available_service_ids", columnDefinition = "TEXT")
    @JsonProperty("available_service_ids")
    var availableServiceIds: String? = null // JSON array of service IDs

    // Booking Password for Online Appointments
    @Column(name = "booking_password", length = 255)
    @JsonProperty("booking_password")
    var bookingPassword: String? = null

    // Metadata
    @Column(name = "is_active", nullable = false)
    @JsonProperty("is_active")
    var isActive: Boolean? = true

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    @JsonProperty("created_at")
    var createdAt: LocalDateTime? = null

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    @JsonProperty("updated_at")
    var updatedAt: LocalDateTime? = null

    // Enums
    enum class CancellationPolicy {
        HOURS_24,
        HOURS_48,
        HOURS_72,
        NO_CHANGES
    }

    enum class BookingAcceptance {
        AUTOMATIC,
        MANUAL
    }

    // Constructors
    constructor()

    constructor(id: String?, salonId: String?) {
        this.id = id
        this.salonId = salonId
        this.isActive = true
        this.cancellationPolicy = CancellationPolicy.HOURS_24
        this.bookingAcceptance = BookingAcceptance.AUTOMATIC
    }
}
