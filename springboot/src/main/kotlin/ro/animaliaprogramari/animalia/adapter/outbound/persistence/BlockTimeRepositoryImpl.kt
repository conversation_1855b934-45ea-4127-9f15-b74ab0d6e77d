package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.BlockTimeEntity
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.JpaBlockTimeRepository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper.BlockTimeEntityMapper
import ro.animaliaprogramari.animalia.application.port.outbound.BlockTimeRepository
import ro.animaliaprogramari.animalia.application.port.outbound.BlockTimeStatisticsData
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate
import java.time.ZonedDateTime

/**
 * Implementation of BlockTimeRepository using JPA
 * Adapts JPA operations to domain repository interface
 */
@Repository
class BlockTimeRepositoryImpl(
    private val jpaRepository: JpaBlockTimeRepository,
    private val mapper: BlockTimeEntityMapper,
) : BlockTimeRepository {
    override fun save(blockTime: BlockTime): BlockTime {
        val entity = mapper.toEntity(blockTime)
        val savedEntity = jpaRepository.save(entity)
        return mapper.toDomain(savedEntity)
    }

    override fun update(blockTime: BlockTime): BlockTime {
        val entity = mapper.toEntity(blockTime)
        val updatedEntity = jpaRepository.save(entity)
        return mapper.toDomain(updatedEntity)
    }

    override fun findById(blockId: BlockTimeId): BlockTime? {
        return jpaRepository.findById(blockId.value)
            .map { mapper.toDomain(it) }
            .orElse(null)
    }

    override fun findByIdAndSalonId(
        blockId: BlockTimeId,
        salonId: SalonId,
    ): BlockTime? {
        return jpaRepository.findByIdAndSalonId(blockId.value, salonId.value)
            ?.let { mapper.toDomain(it) }
    }

    override fun findBySalonId(
        salonId: SalonId,
        startDate: LocalDate?,
        endDate: LocalDate?,
        staffId: StaffId?,
        reason: BlockReason?,
        status: BlockTimeStatus?,
        page: Int,
        limit: Int,
    ): List<BlockTime> {
        val pageable = PageRequest.of(page - 1, limit)

        // Use appropriate query based on filters to avoid parameter binding issues
        val entities =
            when {
                reason != null && status != null -> {
                    jpaRepository.findBySalonIdAndReasonAndStatusOrderByStartTimeDesc(
                        salonId.value,
                        reason.name,
                        status.name,
                        pageable,
                    )
                }
                reason != null -> {
                    jpaRepository.findBySalonIdAndReasonOrderByStartTimeDesc(
                        salonId.value,
                        reason.name,
                        pageable,
                    )
                }
                status != null -> {
                    jpaRepository.findBySalonIdAndStatusOrderByStartTimeDesc(
                        salonId.value,
                        status.name,
                        pageable,
                    )
                }
                else -> {
                    jpaRepository.findBySalonIdOrderByStartTimeDesc(salonId.value, pageable)
                }
            }

        // Apply date and staff filtering in application code
        var filteredEntities = entities

        // Filter by date range
        if (startDate != null || endDate != null) {
            filteredEntities =
                filteredEntities.filter { entity ->
                    val entityStartDate = entity.startTime.toLocalDate()
                    val entityEndDate = entity.endTime.toLocalDate()

                    val afterStart = startDate?.let { entityStartDate >= it } ?: true
                    val beforeEnd = endDate?.let { entityEndDate <= it } ?: true

                    afterStart && beforeEnd
                }
        }

        // Filter by staff ID
        if (staffId != null) {
            filteredEntities =
                filteredEntities.filter { entity ->
                    entity.staffIds.contains(staffId.value)
                }
        }

        return mapper.toDomainList(filteredEntities)
    }

    override fun countBySalonId(
        salonId: SalonId,
        startDate: LocalDate?,
        endDate: LocalDate?,
        staffId: StaffId?,
        reason: BlockReason?,
        status: BlockTimeStatus?,
    ): Long {
        // Use appropriate count method based on filters
        val baseCount =
            when {
                reason != null && status != null -> {
                    jpaRepository.countBySalonIdAndReasonAndStatus(salonId.value, reason.name, status.name)
                }
                reason != null -> {
                    jpaRepository.countBySalonIdAndReason(salonId.value, reason.name)
                }
                status != null -> {
                    jpaRepository.countBySalonIdAndStatus(salonId.value, status.name)
                }
                else -> {
                    jpaRepository.countBySalonId(salonId.value)
                }
            }

        // If we need date or staff filtering, get all entities and count in code
        if (startDate != null || endDate != null || staffId != null) {
            val allEntities = findBySalonId(salonId, startDate, endDate, staffId, reason, status, 1, Int.MAX_VALUE)
            return allEntities.size.toLong()
        }

        return baseCount
    }

    override fun findOverlappingBlocks(
        salonId: SalonId,
        startTime: ZonedDateTime,
        endTime: ZonedDateTime,
        staffIds: Set<StaffId>,
        excludeBlockId: BlockTimeId?,
    ): List<BlockTime> {
        // Get all active blocks for the salon and filter by time and staff in code
        // This avoids H2 time zone comparison issues
        val allActiveEntities = jpaRepository.findActiveBySalonId(salonId.value)

        // Filter by time overlap in code
        val timeOverlappingEntities =
            allActiveEntities.filter { entity ->
                // Check if times overlap: entity.start < appointment.end AND entity.end > appointment.start
                entity.startTime.isBefore(endTime) && entity.endTime.isAfter(startTime)
            }.filter { entity ->
                // Exclude specific block if requested
                excludeBlockId?.value?.let { excludeId ->
                    entity.id != excludeId
                } ?: true
            }

        // Filter by staff IDs in code (H2-compatible)
        val staffIdValues = staffIds.map { it.value }.toSet()
        val filteredEntities =
            timeOverlappingEntities.filter { entity ->
                entity.staffIds.any { staffId -> staffId in staffIdValues }
            }
        return mapper.toDomainList(filteredEntities)
    }

    override fun findActiveBlocksForStaff(
        salonId: SalonId,
        staffIds: Set<UserId>,
        startDate: LocalDate,
        endDate: LocalDate,
    ): List<BlockTime> {
        // For H2 compatibility, we'll check each staff ID individually

        val allEntities = mutableListOf<BlockTimeEntity>()
        for (staffId in staffIds) {
            val entities =
                jpaRepository.findActiveBlocksForStaff(
                    salonId = salonId.value,
                    staffId1 = staffId.value,
                    startDate = startDate,
                    endDate = endDate,
                )
            allEntities.addAll(entities)
        }

        // Remove duplicates and sort by start time
        val uniqueEntities = allEntities.distinctBy { it.id }.sortedBy { it.startTime }
        return mapper.toDomainList(uniqueEntities)
    }

    override fun findByDateRange(
        salonId: SalonId,
        startDate: LocalDate,
        endDate: LocalDate,
        staffId: StaffId?,
    ): List<BlockTime> {
        val entities =
            jpaRepository.findByDateRange(
                salonId = salonId.value,
                startDate = startDate,
                endDate = endDate,
                staffId = null, // Always pass null, filter in code
            )

        // Filter by staff ID in application code for H2 compatibility
        val filteredEntities =
            if (staffId != null) {
                entities.filter { entity ->
                    entity.staffIds.contains(staffId.value)
                }
            } else {
                entities
            }

        return mapper.toDomainList(filteredEntities)
    }

    override fun delete(blockId: BlockTimeId): Boolean {
        return try {
            jpaRepository.deleteById(blockId.value)
            true
        } catch (e: Exception) {
            false
        }
    }

    override fun findExpiredBlocks(): List<BlockTime> {
        val entities = jpaRepository.findExpiredBlocks(ZonedDateTime.now())
        return mapper.toDomainList(entities)
    }

    override fun saveAll(blockTimes: List<BlockTime>): List<BlockTime> {
        val entities = mapper.toEntityList(blockTimes)
        val savedEntities = jpaRepository.saveAll(entities)
        return mapper.toDomainList(savedEntities)
    }

    override fun getStatisticsData(
        salonId: SalonId,
        startDate: LocalDate,
        endDate: LocalDate,
        staffId: StaffId?,
    ): BlockTimeStatisticsData {
        // Get basic statistics - filter by staff in application code
        val statisticsResults =
            jpaRepository.getStatisticsData(
                salonId = salonId.value,
                startDate = startDate,
                endDate = endDate,
                staffId = null, // Always pass null, filter in code
            )

        // Get daily counts
        val dailyResults =
            jpaRepository.getDailyBlockCounts(
                salonId = salonId.value,
                startDate = startDate,
                endDate = endDate,
            )

        // Get hourly counts
        val hourlyResults =
            jpaRepository.getHourlyBlockCounts(
                salonId = salonId.value,
                startDate = startDate,
                endDate = endDate,
            )

        // If staff filtering is needed, get all blocks and filter in code
        val filteredBlocks =
            if (staffId != null) {
                val allBlocks = findByDateRange(salonId, startDate, endDate, staffId)
                allBlocks
            } else {
                null
            }

        // Process results
        var totalBlocks = 0L
        var totalMinutes = 0L
        val reasonCounts = mutableMapOf<BlockReason, Long>()

        if (filteredBlocks != null) {
            // Use filtered blocks for staff-specific statistics
            totalBlocks = filteredBlocks.size.toLong()
            filteredBlocks.groupBy { it.reason }.forEach { (reason, blocks) ->
                reasonCounts[reason] = blocks.size.toLong()
                totalMinutes +=
                    blocks.sumOf {
                        java.time.Duration.between(it.startTime, it.endTime).toMinutes()
                    }
            }
        } else {
            // Use database results for salon-wide statistics
            statisticsResults.forEach { result ->
                val reason = BlockReason.fromString(result[2] as String)
                val count = (result[3] as Number).toLong()
                reasonCounts[reason] = count
                totalBlocks += count
                totalMinutes += (result[1] as Number).toLong()
            }
        }

        val dailyCounts =
            dailyResults.associate { result ->
                val date = result[0] as LocalDate
                val count = (result[1] as Number).toLong()
                date to count
            }

        val hourlyCounts =
            hourlyResults.associate { result ->
                val hour = (result[0] as Number).toInt()
                val count = (result[1] as Number).toLong()
                hour to count
            }

        // For staff counts, we would need additional queries or process existing data
        val staffCounts = mutableMapOf<StaffId, Long>()

        return BlockTimeStatisticsData(
            totalBlocks = totalBlocks,
            totalMinutes = totalMinutes,
            reasonCounts = reasonCounts,
            staffCounts = staffCounts,
            dailyCounts = dailyCounts,
            hourlyCounts = hourlyCounts,
        )
    }

    override fun existsById(blockId: BlockTimeId): Boolean {
        return jpaRepository.existsById(blockId.value)
    }

    override fun existsByIdAndSalonId(
        blockId: BlockTimeId,
        salonId: SalonId,
    ): Boolean {
        return jpaRepository.existsByIdAndSalonId(blockId.value, salonId.value)
    }
}
