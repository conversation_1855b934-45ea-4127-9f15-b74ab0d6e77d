package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.Appointment
import java.time.LocalDate

@Repository
interface SpringAppointmentRepository : JpaRepository<Appointment, String> {
    fun findByAppointmentDateBetween(
        startDate: LocalDate,
        endDate: LocalDate,
    ): List<Appointment>

    fun findByClientId(clientId: String): List<Appointment>

    fun findByPetId(petId: String): List<Appointment>

    fun findByStatus(status: String): List<Appointment>

    fun findByStatusIn(statuses: List<String>): List<Appointment>

    // Remove problematic query with nullable parameters

    // New salon-specific queries

    // Simple queries without nullable parameters
    fun findBySalonId(salonId: String): List<Appointment>

    fun findByStaffId(staffId: String): List<Appointment>

    fun findBySalonIdAndStaffIdAndAppointmentDate(
        salonId: String,
        staffId: String,
        appointmentDate: LocalDate,
    ): List<Appointment>

    fun findByStaffIdAndAppointmentDate(
        staffId: String,
        appointmentDate: LocalDate,
    ): List<Appointment>

    fun findByAppointmentDateAndStatus(
        appointmentDate: LocalDate,
        status: String,
    ): List<Appointment>

    fun findByStaffIdAndAppointmentDateBetween(
        staffId: String,
        startDate: LocalDate,
        endDate: LocalDate,
    ): List<Appointment>

    /**
     * Find appointments by salon ID and date range
     */
    @Query(
        "SELECT a FROM Appointment a WHERE a.salonId = :salonId " +
            "AND a.appointmentDate BETWEEN :startDate AND :endDate",
    )
    fun findBySalonIdAndDateRange(
        @Param("salonId") salonId: String,
        @Param("startDate") startDate: LocalDate,
        @Param("endDate") endDate: LocalDate,
    ): List<Appointment>


    /**
     * Find appointments by subscription ID
     */
    fun findBySubscriptionId(subscriptionId: String): List<Appointment>

    /**
     * Find appointments by subscription ID and salon ID
     */
    @Query(
        "SELECT a FROM Appointment a WHERE a.subscriptionId = :subscriptionId " +
            "AND a.salonId = :salonId " +
            "ORDER BY a.appointmentDate, a.startTime",
    )
    fun findBySubscriptionIdAndSalonId(
        @Param("subscriptionId") subscriptionId: String,
        @Param("salonId") salonId: String,
    ): List<Appointment>

    /**
     * Find subscription by appointment ID
     */
    @Query(
        "SELECT a.subscriptionId FROM Appointment a WHERE a.id = :appointmentId " +
            "AND a.subscriptionId IS NOT NULL",
    )
    fun findSubscriptionIdByAppointmentId(@Param("appointmentId") appointmentId: String): String?

    /**
     * Find appointments by salon ID and client IDs
     */
    fun findBySalonIdAndClientIdIn(salonId: String, clientIds: List<String>): List<Appointment>

    /**
     * Find last appointment date for each client in a salon
     * Returns a map of clientId to last appointment date for completed/confirmed appointments
     */
    @Query(
        "SELECT a.clientId, MAX(a.appointmentDate) " +
            "FROM Appointment a WHERE a.salonId = :salonId " +
            "AND a.status IN ('COMPLETED', 'CONFIRMED', 'SCHEDULED', 'IN_PROGRESS') " +
            "GROUP BY a.clientId",
    )
    fun findLastAppointmentDatesByClientForSalon(
        @Param("salonId") salonId: String,
    ): List<Array<Any>>
}
