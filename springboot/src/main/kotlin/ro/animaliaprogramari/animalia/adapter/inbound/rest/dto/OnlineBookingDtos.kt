package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import com.fasterxml.jackson.annotation.JsonFormat
import java.time.LocalDate
import java.time.LocalTime

/**
 * Request to get available time slots for online booking
 */
data class GetAvailableTimeSlotsRequest(
    @JsonFormat(pattern = "yyyy-MM-dd")
    val date: LocalDate,
    val serviceIds: List<String>,
)

/**
 * Response containing available time slots
 */
data class AvailableTimeSlotsDto(
    @JsonFormat(pattern = "yyyy-MM-dd")
    val date: LocalDate,
    val slots: List<TimeSlotDto>,
)

/**
 * Information about a single available time slot
 */
data class TimeSlotDto(
    @JsonFormat(pattern = "HH:mm")
    val startTime: String,
    @JsonFormat(pattern = "HH:mm")
    val endTime: String,
    val availableStaffIds: List<String>,
)

/**
 * Request to create a proto-appointment for online booking
 */
data class CreateProtoAppointmentRequest(
    @JsonFormat(pattern = "yyyy-MM-dd")
    val date: LocalDate,
    @JsonFormat(pattern = "HH:mm")
    val startTime: String,
    @JsonFormat(pattern = "HH:mm")
    val endTime: String,
    val serviceIds: List<String>,
)

/**
 * Response after creating a proto-appointment
 */
data class ProtoAppointmentResponse(
    val appointmentId: String,
    val expiresAt: String, // ISO-8601 timestamp
)

/**
 * Request to finalize an online booking
 */
data class FinalizeBookingRequest(
    val appointmentId: String,
    val clientPhone: String,
    val petName: String,
    val petSpecies: String,
    val petBreed: String,
    val petSize: String,
    val clientName: String?,
    val notes: String?,
    val password: String?, // Required if salon has password protection enabled
)

/**
 * Response after finalizing a booking
 */
data class FinalizeBookingResponse(
    val appointmentId: String,
    val status: String, // SCHEDULED or REQUESTED
    val isAutoConfirmed: Boolean,
    val message: String,
)

