package ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ClientResponse
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ClientPetInfo
import ro.animaliaprogramari.animalia.domain.model.Client
import ro.animaliaprogramari.animalia.domain.model.ClientDataAccess
import ro.animaliaprogramari.animalia.domain.model.Email
import ro.animaliaprogramari.animalia.domain.model.Pet
import ro.animaliaprogramari.animalia.domain.model.PhoneNumber

/**
 * Mapper for converting between domain models and DTOs for clients
 */
@Component
class ClientDtoMapper {
    /**
     * Convert domain model to response DTO
     */
    fun toResponse(
        client: Client,
        petCount: Int = 0,
        petNames: List<String> = emptyList(),
        matchingPetNames: List<String> = emptyList(),
        pets: List<Pet> = emptyList(),
    ): ClientResponse {
        val uniquePetNames = petNames.distinct()
        val matchedPetNames = matchingPetNames.distinct()
        val petInfos = pets.map { pet ->
            ClientPetInfo(
                id = pet.id.value,
                name = pet.name,
                breed = pet.breed,
                species = pet.species,
                size = pet.size
            )
        }
        return ClientResponse(
            id = client.id.value,
            name = client.name,
            phone = client.phone?.value,
            secondaryPhone = client.secondaryPhone?.value,
            email = client.email?.value,
            address = client.address,
            notes = client.notes,
            isActive = client.isActive,
            createdAt = client.createdAt,
            updatedAt = client.updatedAt,
            petCount = petCount,
            petNames = uniquePetNames,
            matchingPetNames = matchedPetNames,
            matchedViaPetName = matchedPetNames.isNotEmpty(),
            pets = petInfos,
        )
    }

    /**
     * Convert domain model to response DTO with permission-based filtering
     */
    fun toResponse(
        client: Client,
        petCount: Int = 0,
        permission: ClientDataAccess,
        petNames: List<String> = emptyList(),
        matchingPetNames: List<String> = emptyList(),
        pets: List<Pet> = emptyList(),
    ): ClientResponse {
        val uniquePetNames = petNames.distinct()
        val matchedPetNames = matchingPetNames.distinct()
        val petInfos = pets.map { pet ->
            ClientPetInfo(
                id = pet.id.value,
                name = pet.name,
                breed = pet.breed,
                species = pet.species,
                size = pet.size
            )
        }
        return when (permission) {
            ClientDataAccess.NONE -> {
                // Should not happen - if no access, client shouldn't be returned
                throw IllegalStateException("Cannot create response for client with no access permission")
            }
            ClientDataAccess.FULL -> {
                ClientResponse(
                    id = client.id.value,
                    name = client.name,
                    phone = client.phone?.value,
                    secondaryPhone = client.secondaryPhone?.value,
                    email = client.email?.value,
                    address = client.address,
                    notes = client.notes,
                    isActive = client.isActive,
                    createdAt = client.createdAt,
                    updatedAt = client.updatedAt,
                    petCount = petCount,
                    petNames = uniquePetNames,
                    matchingPetNames = matchedPetNames,
                    matchedViaPetName = matchedPetNames.isNotEmpty(),
                    pets = petInfos,
                )
            }
        }
    }

    /**
     * Convert string to PhoneNumber value object
     * Handles legacy format: if phone contains comma, only takes the first number
     */
    fun toPhoneNumber(phone: String?): PhoneNumber? {
        if (phone.isNullOrBlank()) return null

        // If phone contains comma, take only the first number (primary)
        val cleanPhone = if (phone.contains(",")) {
            phone.split(",").firstOrNull()?.trim()
        } else {
            phone.trim()
        }

        return cleanPhone?.let { PhoneNumber.ofNullable(it) }
    }

    /**
     * Convert string to Email value object
     */
    fun toEmail(email: String?): Email? {
        return email?.let { Email.ofNullable(it) }
    }

    /**
     * Parse phone numbers from request, handling both legacy (comma-separated) and new format
     * Returns pair of (primaryPhone, secondaryPhone)
     */
    fun parsePhoneNumbers(phone: String?, secondaryPhone: String?): Pair<PhoneNumber?, PhoneNumber?> {
        println("DEBUG: parsePhoneNumbers - phone: '$phone', secondaryPhone: '$secondaryPhone'")
        
        // If phone contains comma, split it (legacy format from frontend)
        return if (phone?.contains(",") == true) {
            val parts = phone.split(",").map { it.trim() }.filter { it.isNotBlank() }
            println("DEBUG: parts: $parts")
            
            val primary = parts.getOrNull(0)?.let { PhoneNumber.ofNullable(it) }
            println("DEBUG: primary: $primary")
            
            // Join all secondary numbers (from index 1 onwards) with comma
            val secondaryNumbers = parts.drop(1)
            println("DEBUG: secondaryNumbers: $secondaryNumbers")
            
            val secondary = if (secondaryNumbers.isNotEmpty()) {
                PhoneNumber.ofNullable(secondaryNumbers.joinToString(","))
            } else null
            println("DEBUG: secondary: $secondary")
            
            Pair(primary, secondary)
        } else {
            // New format: separate fields
            Pair(
                phone?.let { PhoneNumber.ofNullable(it) },
                secondaryPhone?.let { PhoneNumber.ofNullable(it) }
            )
        }
    }
}
