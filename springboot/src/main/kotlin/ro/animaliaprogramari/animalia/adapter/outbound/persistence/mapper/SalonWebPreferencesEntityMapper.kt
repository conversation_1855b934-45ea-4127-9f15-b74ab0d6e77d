package ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences as SalonWebPreferencesEntity
import java.time.LocalDateTime

/**
 * Mapper between domain SalonWebPreferences and JPA SalonWebPreferencesEntity
 * This handles the translation between the pure domain model and persistence model
 */
@Component
class SalonWebPreferencesEntityMapper {
    private val objectMapper = ObjectMapper()

    /**
     * Convert domain model to JPA entity
     */
    fun toEntity(domain: SalonWebPreferences): SalonWebPreferencesEntity {
        val entity = SalonWebPreferencesEntity()

        entity.id = domain.id.value
        entity.salonId = domain.salonId.value
        entity.bookingWebsiteUrl = domain.bookingWebsiteUrl
        entity.businessName = domain.businessName
        entity.businessDescription = domain.businessDescription
        entity.businessAddress = domain.businessAddress
        entity.contactPhone = domain.contactPhone
        entity.contactEmail = domain.contactEmail
        entity.facebookLink = domain.facebookLink
        entity.instagramLink = domain.instagramLink
        entity.tiktokLink = domain.tiktokLink

        // Website Customization
        entity.logoUrl = domain.logoUrl.ifEmpty { null }
        entity.primaryColor = domain.primaryColor.ifEmpty { null }

        // Convert lists and maps to JSON strings
        entity.websitePhotos = if (domain.websitePhotos.isNotEmpty()) {
            objectMapper.writeValueAsString(domain.websitePhotos)
        } else {
            null
        }

        entity.businessHours = if (domain.businessHours.isNotEmpty()) {
            objectMapper.writeValueAsString(domain.businessHours)
        } else {
            null
        }

        // Convert enums
        entity.cancellationPolicy = when (domain.cancellationPolicy) {
            CancellationPolicy.HOURS_24 -> SalonWebPreferencesEntity.CancellationPolicy.HOURS_24
            CancellationPolicy.HOURS_48 -> SalonWebPreferencesEntity.CancellationPolicy.HOURS_48
            CancellationPolicy.HOURS_72 -> SalonWebPreferencesEntity.CancellationPolicy.HOURS_72
            CancellationPolicy.NO_CHANGES -> SalonWebPreferencesEntity.CancellationPolicy.NO_CHANGES
        }

        entity.bookingAcceptance = when (domain.bookingAcceptance) {
            BookingAcceptance.AUTOMATIC -> SalonWebPreferencesEntity.BookingAcceptance.AUTOMATIC
            BookingAcceptance.MANUAL -> SalonWebPreferencesEntity.BookingAcceptance.MANUAL
        }

        // Online Booking Settings
        entity.onlineBookingEnabled = domain.onlineBookingEnabled
        entity.availableServiceIds = if (domain.availableServiceIds.isNotEmpty()) {
            objectMapper.writeValueAsString(domain.availableServiceIds)
        } else {
            null
        }
        entity.bookingPassword = domain.bookingPassword.ifEmpty { null }

        entity.isActive = domain.isActive
        entity.createdAt = domain.createdAt
        entity.updatedAt = domain.updatedAt

        return entity
    }

    /**
     * Convert JPA entity to domain model
     */
    fun toDomain(entity: SalonWebPreferencesEntity): SalonWebPreferences {
        return SalonWebPreferences(
            id = SalonWebPreferencesId.of(entity.id ?: ""),
            salonId = SalonId.of(entity.salonId ?: ""),
            bookingWebsiteUrl = entity.bookingWebsiteUrl ?: "",
            businessName = entity.businessName ?: "",
            businessDescription = entity.businessDescription ?: "",
            businessAddress = entity.businessAddress ?: "",
            contactPhone = entity.contactPhone ?: "",
            contactEmail = entity.contactEmail ?: "",
            facebookLink = entity.facebookLink ?: "",
            instagramLink = entity.instagramLink ?: "",
            tiktokLink = entity.tiktokLink ?: "",
            logoUrl = entity.logoUrl ?: "",
            primaryColor = entity.primaryColor ?: "#6366F1",
            websitePhotos = parseWebsitePhotos(entity.websitePhotos),
            businessHours = parseBusinessHours(entity.businessHours),
            cancellationPolicy = mapCancellationPolicy(entity.cancellationPolicy),
            bookingAcceptance = mapBookingAcceptance(entity.bookingAcceptance),
            onlineBookingEnabled = entity.onlineBookingEnabled ?: true,
            availableServiceIds = parseAvailableServiceIds(entity.availableServiceIds),
            bookingPassword = entity.bookingPassword ?: "",
            isActive = entity.isActive ?: true,
            createdAt = entity.createdAt ?: LocalDateTime.now(),
            updatedAt = entity.updatedAt ?: LocalDateTime.now()
        )
    }

    /**
     * Parse website photos from JSON string
     */
    private fun parseWebsitePhotos(photosJson: String?): List<String> {
        return if (photosJson.isNullOrBlank()) {
            emptyList()
        } else {
            try {
                objectMapper.readValue<List<String>>(photosJson)
            } catch (e: Exception) {
                emptyList()
            }
        }
    }

    /**
     * Parse business hours from JSON string
     */
    private fun parseBusinessHours(hoursJson: String?): Map<String, Map<String, Any>> {
        return if (hoursJson.isNullOrBlank()) {
            emptyMap()
        } else {
            try {
                objectMapper.readValue<Map<String, Map<String, Any>>>(hoursJson)
            } catch (e: Exception) {
                emptyMap()
            }
        }
    }

    /**
     * Parse available service IDs from JSON string
     */
    private fun parseAvailableServiceIds(serviceIdsJson: String?): List<String> {
        if (serviceIdsJson.isNullOrBlank()) return emptyList()

        // Try parsing robustly: accept JSON arrays of strings or numbers, a single id string,
        // or a bracketed string. Return each element as String.
        try {
            // First try generic List<Any> and convert elements to String
            val anyList: List<Any> = objectMapper.readValue(serviceIdsJson)
            return anyList.map { it.toString() }
        } catch (e: Exception) {
            // Fallback parsing: handle bracketed CSV-like formats or single values
            try {
                val trimmed = serviceIdsJson.trim()
                if (trimmed.startsWith("[") && trimmed.endsWith("]")) {
                    val inner = trimmed.substring(1, trimmed.length - 1)
                    if (inner.isBlank()) return emptyList()
                    return inner.split(",").map { it.trim().trim('"', '\'') }.filter { it.isNotEmpty() }
                }
                // Single id string (possibly quoted)
                return listOf(trimmed.trim('"', '\''))
            } catch (ex: Exception) {
                return emptyList()
            }
        }
    }

    /**
     * Map entity cancellation policy to domain enum
     */
    private fun mapCancellationPolicy(policy: SalonWebPreferencesEntity.CancellationPolicy?): CancellationPolicy {
        return when (policy) {
            SalonWebPreferencesEntity.CancellationPolicy.HOURS_24 -> CancellationPolicy.HOURS_24
            SalonWebPreferencesEntity.CancellationPolicy.HOURS_48 -> CancellationPolicy.HOURS_48
            SalonWebPreferencesEntity.CancellationPolicy.HOURS_72 -> CancellationPolicy.HOURS_72
            SalonWebPreferencesEntity.CancellationPolicy.NO_CHANGES -> CancellationPolicy.NO_CHANGES
            null -> CancellationPolicy.HOURS_24 // Default value
        }
    }

    /**
     * Map entity booking acceptance to domain enum
     */
    private fun mapBookingAcceptance(acceptance: SalonWebPreferencesEntity.BookingAcceptance?): BookingAcceptance {
        return when (acceptance) {
            SalonWebPreferencesEntity.BookingAcceptance.AUTOMATIC -> BookingAcceptance.AUTOMATIC
            SalonWebPreferencesEntity.BookingAcceptance.MANUAL -> BookingAcceptance.MANUAL
            null -> BookingAcceptance.AUTOMATIC // Default value
        }
    }
}
