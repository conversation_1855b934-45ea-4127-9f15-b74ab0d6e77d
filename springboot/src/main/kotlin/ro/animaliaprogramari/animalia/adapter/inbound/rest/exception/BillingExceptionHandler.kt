package ro.animaliaprogramari.animalia.adapter.inbound.rest.exception

import com.stripe.exception.*
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.FieldError
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.RestControllerAdvice
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ApiResponse

/**
 * Global exception handler for billing-related controllers
 * Provides consistent error responses and proper logging
 */
@RestControllerAdvice
class BillingExceptionHandler {
    private val logger = LoggerFactory.getLogger(BillingExceptionHandler::class.java)

    /**
     * Handle validation errors from @Valid annotations
     */
    @ExceptionHandler(MethodArgumentNotValidException::class)
    fun handleValidationErrors(ex: MethodArgumentNotValidException): ResponseEntity<ApiResponse<Nothing>> {
        val errors = ex.bindingResult.allErrors.map { error ->
            when (error) {
                is FieldError -> "${error.field}: ${error.defaultMessage}"
                else -> error.defaultMessage ?: "Validation error"
            }
        }

        val errorMessage = "Validation failed: ${errors.joinToString(", ")}"
        logger.warn("Validation error: $errorMessage")

        return ResponseEntity.badRequest().body(ApiResponse.error(errorMessage))
    }

    /**
     * Handle Stripe API errors
     */
    @ExceptionHandler(StripeException::class)
    fun handleStripeException(ex: StripeException): ResponseEntity<ApiResponse<Nothing>> {
        return when (ex) {
            is CardException -> {
                // Card was declined
                logger.warn("Stripe card error: ${ex.message}")
                ResponseEntity.badRequest().body(
                    ApiResponse.error("Payment failed: ${ex.message}")
                )
            }
            is RateLimitException -> {
                // Too many requests made to the API too quickly
                logger.error("Stripe rate limit exceeded: ${ex.message}")
                ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).body(
                    ApiResponse.error("Service temporarily unavailable. Please try again later.")
                )
            }
            is InvalidRequestException -> {
                // Invalid parameters were supplied to Stripe's API
                logger.error("Stripe invalid request: ${ex.message}")
                ResponseEntity.badRequest().body(
                    ApiResponse.error("Invalid request: ${ex.message}")
                )
            }
            is AuthenticationException -> {
                // Authentication with Stripe's API failed
                logger.error("Stripe authentication failed: ${ex.message}")
                ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                    ApiResponse.error("Payment service configuration error")
                )
            }
            is ApiConnectionException -> {
                // Network communication with Stripe failed
                logger.error("Stripe API connection failed: ${ex.message}")
                ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(
                    ApiResponse.error("Payment service temporarily unavailable")
                )
            }
            is ApiException -> {
                // Generic API error
                logger.error("Stripe API error: ${ex.message}")
                ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                    ApiResponse.error("Payment service error")
                )
            }
            else -> {
                // Unknown Stripe error
                logger.error("Unknown Stripe error: ${ex.message}", ex)
                ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                    ApiResponse.error("Payment processing error")
                )
            }
        }
    }

    /**
     * Handle illegal argument exceptions
     */
    @ExceptionHandler(IllegalArgumentException::class)
    fun handleIllegalArgumentException(ex: IllegalArgumentException): ResponseEntity<ApiResponse<Nothing>> {
        logger.warn("Illegal argument: ${ex.message}")
        ex.printStackTrace()
        return ResponseEntity.badRequest().body(ApiResponse.error(ex.message ?: "Invalid request"))
    }

    /**
     * Handle illegal state exceptions
     */
    @ExceptionHandler(IllegalStateException::class)
    fun handleIllegalStateException(ex: IllegalStateException): ResponseEntity<ApiResponse<Nothing>> {
        logger.error("Illegal state: ${ex.message}", ex)
        return ResponseEntity.status(HttpStatus.CONFLICT).body(
            ApiResponse.error(ex.message ?: "Request cannot be processed in current state")
        )
    }

    /**
     * Handle security exceptions
     */
    @ExceptionHandler(SecurityException::class)
    fun handleSecurityException(ex: SecurityException): ResponseEntity<ApiResponse<Nothing>> {
        logger.warn("Security error: ${ex.message}")
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(
            ApiResponse.error("Access denied")
        )
    }

    /**
     * Handle generic exceptions
     */
//    @ExceptionHandler(Exception::class)
//    fun handleGenericException(ex: Exception): ResponseEntity<ApiResponse<Nothing>> {
//        logger.error("Unexpected error in billing controller", ex)
//        ex.printStackTrace()
//        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
//            ApiResponse.error("An unexpected error occurred")
//        )
//    }
}
