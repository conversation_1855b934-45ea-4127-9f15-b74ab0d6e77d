package ro.animaliaprogramari.animalia.adapter.inbound.web.dto.referral

import com.fasterxml.jackson.annotation.JsonFormat
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import ro.animaliaprogramari.animalia.domain.model.referral.ReferralConstants
import java.time.LocalDateTime

/**
 * Request to claim a referral code
 */
data class ClaimReferralCodeRequest(
    @field:NotBlank(message = "Referral code is required")
    @field:Size(min = ReferralConstants.CODE_LENGTH, max = ReferralConstants.CODE_LENGTH, message = "Referral code must be exactly ${ReferralConstants.CODE_LENGTH} characters")
    @field:Pattern(regexp = ReferralConstants.CODE_VALIDATION_PATTERN, message = "Referral code must contain only uppercase letters and numbers")
    val code: String
)

/**
 * Response after claiming a referral code
 */
data class ClaimReferralCodeResponse(
    val success: Boolean,
    val message: String,
    val smsCreditsAwarded: Int,
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val claimedAt: LocalDateTime?
)

/**
 * Request to validate a referral code
 */
data class ValidateReferralCodeRequest(
    @field:NotBlank(message = "Referral code is required")
    @field:Size(min = ReferralConstants.CODE_LENGTH, max = ReferralConstants.CODE_LENGTH, message = "Referral code must be exactly ${ReferralConstants.CODE_LENGTH} characters")
    @field:Pattern(regexp = ReferralConstants.CODE_VALIDATION_PATTERN, message = "Referral code must contain only uppercase letters and numbers")
    val code: String
)

/**
 * Response after validating a referral code
 */
data class ValidateReferralCodeResponse(
    val isValid: Boolean,
    val message: String,
    val smsCreditsAwarded: Int
)

/**
 * Response after generating a referral code
 */
data class GenerateReferralCodeResponse(
    val code: String,
    val salonId: String,
    val smsCreditsAwarded: Int,
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val createdAt: LocalDateTime,
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val expiresAt: LocalDateTime?
)

/**
 * Response representing a referral code
 */
data class ReferralCodeResponse(
    val id: String,
    val code: String,
    val status: String,
    val smsCreditsAwarded: Int,
    val isClaimed: Boolean,
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val claimedAt: LocalDateTime?,
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val createdAt: LocalDateTime,
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val expiresAt: LocalDateTime?
)

/**
 * Response for referral statistics
 */
data class ReferralStatsResponse(
    val totalReferrals: Long,
    val nextRewardAmount: Int,
    val progressiveRewards: List<ProgressiveRewardResponse>,
    val totalCreditsEarned: Int
)

/**
 * Response for progressive reward information
 */
data class ProgressiveRewardResponse(
    val referralNumber: Int,
    val rewardAmount: Int,
    val isCompleted: Boolean
)
