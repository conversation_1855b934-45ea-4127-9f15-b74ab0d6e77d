package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

/**
 * Response DTO for inactive client information
 */
@Schema(description = "Inactive client information with last appointment date")
data class InactiveClientResponse(
    @JsonProperty("client")
    val client: ClientResponse,

    @JsonProperty("lastAppointmentDate")
    val lastAppointmentDate: LocalDateTime?,

    @JsonProperty("daysSinceLastAppointment")
    val daysSinceLastAppointment: Int,
)

