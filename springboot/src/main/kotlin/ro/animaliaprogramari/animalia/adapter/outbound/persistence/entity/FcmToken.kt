package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.Id
import jakarta.persistence.Table
import jakarta.validation.constraints.NotBlank
import ro.animaliaprogramari.animalia.domain.model.notification.DeviceType
import java.time.LocalDateTime

@Entity
@Table(name = "fcm_tokens")
data class FcmToken(
    @Id
    var id: String,
    @field:NotBlank(message = "User ID is required")
    @Column(name = "user_id", nullable = false)
    var userId: String,
    @field:NotBlank(message = "Salon ID is required")
    @Column(name = "salon_id", nullable = false)
    var salonId: String,
    @field:NotBlank(message = "FCM token is required")
    @Column(name = "fcm_token", nullable = false)
    var fcmToken: String,
    @Column(name = "device_id", nullable = true)
    var deviceId: String? = null,
    @Enumerated(EnumType.STRING)
    @Column(name = "device_type", nullable = false)
    var deviceType: DeviceType = DeviceType.MOBILE,
    @Column(name = "is_active", nullable = false)
    var isActive: Boolean = true,
    @Column(name = "last_used", nullable = false)
    var lastUsed: LocalDateTime = LocalDateTime.now(),
    @Column(name = "created_at", nullable = false)
    var createdAt: LocalDateTime = LocalDateTime.now(),
    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now(),
) {
    // Default constructor for JPA
    constructor() : this(
        id = "",
        userId = "",
        salonId = "",
        fcmToken = "",
        deviceId = null,
        deviceType = DeviceType.MOBILE,
        isActive = true,
        lastUsed = LocalDateTime.now(),
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
    )
}
