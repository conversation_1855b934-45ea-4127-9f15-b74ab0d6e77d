package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper.SalonWebPreferencesEntityMapper
import ro.animaliaprogramari.animalia.application.port.outbound.SalonWebPreferencesRepository
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * JPA adapter implementing the SalonWebPreferencesRepository port
 * This adapter translates between domain models and JPA entities
 */
@Repository
class JpaSalonWebPreferencesRepository(
    private val springRepository: SpringSalonWebPreferencesRepository,
    private val mapper: SalonWebPreferencesEntityMapper,
) : SalonWebPreferencesRepository {

    override fun save(preferences: SalonWebPreferences): SalonWebPreferences {
        // For updates, find existing entity and update only changed fields to avoid version conflicts
        val existingEntity = springRepository.findById(preferences.id.value).orElse(null)
        if (existingEntity != null) {
            // Update the existing entity with new values
            existingEntity.salonId = preferences.salonId.value
            existingEntity.bookingWebsiteUrl = preferences.bookingWebsiteUrl
            existingEntity.businessName = preferences.businessName
            existingEntity.businessDescription = preferences.businessDescription
            existingEntity.businessAddress = preferences.businessAddress
            existingEntity.contactPhone = preferences.contactPhone
            existingEntity.contactEmail = preferences.contactEmail
            existingEntity.facebookLink = preferences.facebookLink
            existingEntity.instagramLink = preferences.instagramLink
            existingEntity.tiktokLink = preferences.tiktokLink
            existingEntity.logoUrl = preferences.logoUrl
            existingEntity.primaryColor = preferences.primaryColor

            // Update JSON fields
            existingEntity.websitePhotos = if (preferences.websitePhotos.isNotEmpty()) {
                com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(preferences.websitePhotos)
            } else {
                null
            }

            existingEntity.businessHours = if (preferences.businessHours.isNotEmpty()) {
                com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(preferences.businessHours)
            } else {
                null
            }

            // Update online booking settings
            existingEntity.onlineBookingEnabled = preferences.onlineBookingEnabled
            existingEntity.availableServiceIds = if (preferences.availableServiceIds.isNotEmpty()) {
                com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(preferences.availableServiceIds)
            } else {
                null
            }

            // Booking password: persist only when not empty, otherwise clear it
            existingEntity.bookingPassword = if (preferences.bookingPassword.isNotEmpty()) preferences.bookingPassword else null

            // Update enums
            existingEntity.cancellationPolicy = when (preferences.cancellationPolicy) {
                CancellationPolicy.HOURS_24 -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.CancellationPolicy.HOURS_24
                CancellationPolicy.HOURS_48 -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.CancellationPolicy.HOURS_48
                CancellationPolicy.HOURS_72 -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.CancellationPolicy.HOURS_72
                CancellationPolicy.NO_CHANGES -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.CancellationPolicy.NO_CHANGES
            }

            existingEntity.bookingAcceptance = when (preferences.bookingAcceptance) {
                BookingAcceptance.AUTOMATIC -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.BookingAcceptance.AUTOMATIC
                BookingAcceptance.MANUAL -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.BookingAcceptance.MANUAL
            }

            existingEntity.isActive = preferences.isActive
            existingEntity.updatedAt = preferences.updatedAt
            // Don't update createdAt for existing entities

            val savedEntity = springRepository.save(existingEntity)
            return mapper.toDomain(savedEntity)
        } else {
            // For new entities, save normally
            val entity = mapper.toEntity(preferences)
            val savedEntity = springRepository.save(entity)
            return mapper.toDomain(savedEntity)
        }
    }

    override fun findById(id: SalonWebPreferencesId): SalonWebPreferences? {
        return springRepository.findById(id.value)
            .map { mapper.toDomain(it) }
            .orElse(null)
    }

    override fun findBySalonId(salonId: SalonId): SalonWebPreferences? {
        return springRepository.findBySalonId(salonId.value)
            .map { mapper.toDomain(it) }
            .orElse(null)
    }

    override fun findActiveBySalonId(salonId: SalonId): SalonWebPreferences? {
        return springRepository.findActiveBySalonId(salonId.value)
            .map { mapper.toDomain(it) }
            .orElse(null)
    }

    override fun findByBookingUrl(bookingUrl: String): SalonWebPreferences? {
        return springRepository.findByBookingWebsiteUrl(bookingUrl)
            .map { mapper.toDomain(it) }
            .orElse(null)
    }

    override fun existsBySalonId(salonId: SalonId): Boolean {
        return springRepository.existsBySalonId(salonId.value)
    }

    override fun existsById(id: SalonWebPreferencesId): Boolean {
        return springRepository.existsById(id.value)
    }

    override fun deleteBySalonId(salonId: SalonId) {
        springRepository.deleteBySalonId(salonId.value)
    }

    override fun deleteById(id: SalonWebPreferencesId) {
        springRepository.deleteById(id.value)
    }

    override fun findAll(
        isActive: Boolean?,
        limit: Int?,
        offset: Int?,
    ): List<SalonWebPreferences> {
        // For simplicity, using basic filtering
        // In a real implementation, you might use Criteria API or custom queries
        var entities = if (isActive != null) {
            springRepository.findByIsActive(isActive)
        } else {
            springRepository.findAll()
        }

        // Apply pagination if specified
        if (offset != null && limit != null) {
            entities = entities.drop(offset).take(limit)
        } else if (limit != null) {
            entities = entities.take(limit)
        }

        return entities.map { mapper.toDomain(it) }
    }

    override fun count(isActive: Boolean?): Long {
        return if (isActive != null) {
            springRepository.countByIsActive(isActive)
        } else {
            springRepository.count()
        }
    }
}
