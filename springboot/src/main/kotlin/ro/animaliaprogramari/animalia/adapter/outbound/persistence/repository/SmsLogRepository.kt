package ro.animaliaprogramari.animalia.adapter.outbound.persistence.repository

import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsLog
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsMessageType
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsStatus
import java.time.LocalDateTime

@Repository
interface SmsLogRepository : JpaRepository<SmsLog, String> {

    /**
     * Find SMS logs by salon ID with pagination, ordered by sent date descending
     */
    fun findBySalonIdOrderBySentAtDesc(salonId: String, pageable: Pageable): Page<SmsLog>

    /**
     * Find SMS logs by salon ID and message type with pagination
     */
    fun findBySalonIdAndMessageTypeOrderBySentAtDesc(
        salonId: String,
        messageType: SmsMessageType,
        pageable: Pageable
    ): Page<SmsLog>

    /**
     * Find SMS logs by salon ID and status with pagination
     */
    fun findBySalonIdAndStatusOrderBySentAtDesc(
        salonId: String,
        status: SmsStatus,
        pageable: Pageable
    ): Page<SmsLog>

    /**
     * Find SMS logs by salon ID within date range
     */
    @Query("""
        SELECT s FROM SmsLog s
        WHERE s.salonId = :salonId
        AND s.sentAt BETWEEN :startDate AND :endDate
        ORDER BY s.sentAt DESC
    """)
    fun findBySalonIdAndSentAtBetween(
        @Param("salonId") salonId: String,
        @Param("startDate") startDate: LocalDateTime,
        @Param("endDate") endDate: LocalDateTime,
        pageable: Pageable
    ): Page<SmsLog>

    /**
     * Count SMS logs by salon ID
     */
    fun countBySalonId(salonId: String): Long

    /**
     * Count SMS logs by salon ID and message type
     */
    fun countBySalonIdAndMessageType(salonId: String, messageType: SmsMessageType): Long

    /**
     * Count SMS logs by salon ID and status
     */
    fun countBySalonIdAndStatus(salonId: String, status: SmsStatus): Long

    /**
     * Find SMS logs by appointment ID
     */
    fun findByAppointmentIdOrderBySentAtDesc(appointmentId: String): List<SmsLog>

    /**
     * Find SMS logs by client ID with pagination
     */
    fun findByClientIdOrderBySentAtDesc(clientId: String, pageable: Pageable): Page<SmsLog>

    /**
     * Find SMS logs by salon ID and status within date range (for SMS units calculation)
     */
    @Query("""
        SELECT s FROM SmsLog s
        WHERE s.salonId = :salonId
        AND s.status = :status
        AND s.sentAt BETWEEN :startDate AND :endDate
        ORDER BY s.sentAt DESC
    """)
    fun findBySalonIdAndStatusAndSentAtBetween(
        @Param("salonId") salonId: String,
        @Param("status") status: SmsStatus,
        @Param("startDate") startDate: LocalDateTime,
        @Param("endDate") endDate: LocalDateTime
    ): List<SmsLog>

    /**
     * Find SMS logs by salon ID and status (for total SMS units calculation)
     */
    fun findBySalonIdAndStatus(salonId: String, status: SmsStatus): List<SmsLog>

    /**
     * Find the most recent SMS log sent to a specific phone number
     * Used to determine which salon a client belongs to based on conversation context
     */
    fun findFirstByPhoneNumberOrderBySentAtDesc(phoneNumber: String): SmsLog?

    /**
     * Find all clients with the same phone number across different salons
     */
    @Query("""
        SELECT DISTINCT s.salonId FROM SmsLog s
        WHERE s.phoneNumber = :phoneNumber
        ORDER BY s.sentAt DESC
    """)
    fun findSalonIdsByPhoneNumber(@Param("phoneNumber") phoneNumber: String): List<String>
}
