package ro.animaliaprogramari.animalia.adapter.inbound.rest

import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences
import ro.animaliaprogramari.animalia.application.service.SalonWebPreferencesService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.core.Authentication
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ApiResponse
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.domain.model.AuthenticatedUser
import ro.animaliaprogramari.animalia.domain.model.SalonId

/**
 * REST controller for managing salon website preferences
 */
@RestController
@RequestMapping("/salon-web-preferences")
@CrossOrigin(origins = ["*"])
class SalonWebPreferencesController {
    @Autowired
    private val webPreferencesService: SalonWebPreferencesService? = null

    /**
     * Get web preferences for the current user's salon
     */
    @GetMapping
    fun getWebPreferences(authentication: Authentication): ResponseEntity<ApiResponse<SalonWebPreferences?>?> {
        try {
            val userId = authentication.getName()
            val currentUser: AuthenticatedUser? = SecurityUtils.getCurrentUser()
            val salonId: SalonId? = currentUser?.currentSalonId

            if (salonId == null) {
                logger.warn("User {} has no associated salon", userId)
                return ResponseEntity.ok(ApiResponse.error("No salon associated with user"))
            }

            val preferences = webPreferencesService!!.getWebPreferences(salonId.value)

            if (preferences.isPresent) {
                logger.debug("Retrieved web preferences for salon: {}", salonId)
                return ResponseEntity.ok(ApiResponse.success(preferences.get()))
            } else {
                logger.debug("No web preferences found for salon: {}", salonId)
                return ResponseEntity.ok(ApiResponse.success(null))
            }
        } catch (e: Exception) {
            logger.error("Error retrieving web preferences", e)
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to retrieve web preferences: " + e.message))
        }
    }

    /**
     * Save or update web preferences for the current user's salon
     */
    @PostMapping
    fun saveWebPreferences(
        @RequestBody preferences: SalonWebPreferences?
    ): ResponseEntity<ApiResponse<SalonWebPreferences?>?> {
        try {
            val currentUser: AuthenticatedUser? = SecurityUtils.getCurrentUser()
            val salonId: SalonId? = currentUser?.currentSalonId

            if (salonId == null) {
                logger.warn("User {} has no associated salon", currentUser?.userId?.value)
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("No salon associated with user"))
            }

            logger.info("Saving web preferences for salon: {} by user: {}", salonId, currentUser.userId.value)

            val savedPreferences = webPreferencesService!!.saveWebPreferences(salonId.value, preferences)

            logger.info("Successfully saved web preferences for salon: {}", salonId)
            return ResponseEntity.ok(ApiResponse.success(savedPreferences))
        } catch (e: Exception) {
            logger.error("Error saving web preferences", e)
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to save web preferences: " + e.message))
        }
    }

    /**
     * Update specific web preferences for the current user's salon
     */
    @PutMapping
    fun updateWebPreferences(
        @RequestBody preferences: SalonWebPreferences?,
        authentication: Authentication
    ): ResponseEntity<ApiResponse<SalonWebPreferences?>?> {
        try {
            val userId: AuthenticatedUser? = SecurityUtils.getCurrentUser()
            val salonId: SalonId? = userId?.currentSalonId

            if (salonId == null) {
                logger.warn("User {} has no associated salon", userId)
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("No salon associated with user"))
            }

            logger.info("Updating web preferences for salon: {} by user: {}", salonId, userId)

            val updatedPreferences = webPreferencesService!!.saveWebPreferences(salonId.value, preferences)

            logger.info("Successfully updated web preferences for salon: {}", salonId)
            return ResponseEntity.ok(ApiResponse.success(updatedPreferences))
        } catch (e: Exception) {
            logger.error("Error updating web preferences", e)
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to update web preferences: " + e.message))
        }
    }

    /**
     * Delete web preferences for the current user's salon
     */
    @DeleteMapping
    fun deleteWebPreferences(authentication: Authentication): ResponseEntity<ApiResponse<String?>?> {
        try {
            val userId: AuthenticatedUser? = SecurityUtils.getCurrentUser()
            val salonId: SalonId? = userId?.currentSalonId

            if (salonId == null) {
                logger.warn("User {} has no associated salon", userId)
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("No salon associated with user"))
            }

            logger.info("Deleting web preferences for salon: {} by user: {}", salonId, userId)

            webPreferencesService!!.deleteWebPreferences(salonId.value)

            logger.info("Successfully deleted web preferences for salon: {}", salonId)
            return ResponseEntity.ok(ApiResponse.success("Web preferences deleted successfully"))
        } catch (e: Exception) {
            logger.error("Error deleting web preferences", e)
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to delete web preferences: " + e.message))
        }
    }

    /**
     * Check if web preferences exist for the current user's salon
     */
    @GetMapping("/exists")
    fun hasWebPreferences(authentication: Authentication): ResponseEntity<ApiResponse<Boolean?>?> {
        try {
            val userId: AuthenticatedUser? = SecurityUtils.getCurrentUser()
            val salonId: SalonId? = userId?.currentSalonId

            if (salonId == null) {
                logger.warn("User {} has no associated salon", userId)
                return ResponseEntity.ok(ApiResponse.success(false))
            }

            val exists = webPreferencesService!!.hasWebPreferences(salonId.value)

            logger.debug("Web preferences exist for salon {}: {}", salonId, exists)
            return ResponseEntity.ok(ApiResponse.success(exists))
        } catch (e: Exception) {
            logger.error("Error checking web preferences existence", e)
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to check web preferences: " + e.message))
        }
    }

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(SalonWebPreferencesController::class.java)
    }

    /**
     * PUBLIC ENDPOINT: Get web preferences by booking website URL (no authentication required)
     * This endpoint allows public access to salon information for booking purposes
     */
    @GetMapping("/public/{bookingUrl}")
    fun getPublicWebPreferences(@PathVariable bookingUrl: String): ResponseEntity<ApiResponse<SalonWebPreferences?>?> {
        try {
            logger.info("Public request for booking URL: {}", bookingUrl)

            val preferences = webPreferencesService!!.getWebPreferencesByBookingUrl(bookingUrl)

            if (preferences.isPresent) {
                val prefs = preferences.get()

                // Check if online booking is enabled
                prefs.onlineBookingEnabled?.let {
                    if (!it) {
                        logger.warn("Online booking is disabled for booking URL: {}", bookingUrl)
                        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                            .body(ApiResponse.error("Online booking is not available for this salon"))
                    }
                }

                logger.info("Found web preferences for booking URL: {}", bookingUrl)
                return ResponseEntity.ok(ApiResponse.success(prefs))
            } else {
                logger.warn("No web preferences found for booking URL: {}", bookingUrl)
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Salon not found for this booking URL"))
            }
        } catch (e: Exception) {
            logger.error("Error retrieving public web preferences for URL: {}", bookingUrl, e)
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to retrieve salon information: " + e.message))
        }
    }
}
