package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences as SalonWebPreferencesEntity
import java.util.*

/**
 * Spring Data JPA repository for SalonWebPreferences entity
 * This is the low-level persistence interface used by the JPA adapter
 */
@Repository
interface SpringSalonWebPreferencesRepository : JpaRepository<SalonWebPreferencesEntity, String> {

    /**
     * Find web preferences by salon ID
     */
    fun findBySalonId(salonId: String): Optional<SalonWebPreferencesEntity>

    /**
     * Find active web preferences by salon ID
     */
    @Query("SELECT swp FROM SalonWebPreferences swp WHERE swp.salonId = :salonId AND swp.isActive = true")
    fun findActiveBySalonId(@Param("salonId") salonId: String): Optional<SalonWebPreferencesEntity>

    /**
     * Find web preferences by booking website URL (for public access)
     */
    fun findByBookingWebsiteUrl(bookingWebsiteUrl: String): Optional<SalonWebPreferencesEntity>

    /**
     * Check if web preferences exist for a salon
     */
    fun existsBySalonId(salonId: String): Boolean

    /**
     * Delete web preferences by salon ID
     */
    fun deleteBySalonId(salonId: String)

    /**
     * Find all active web preferences
     */
    fun findByIsActive(isActive: Boolean): List<SalonWebPreferencesEntity>

    /**
     * Count web preferences by active status
     */
    fun countByIsActive(isActive: Boolean): Long

    /**
     * Find web preferences by salon ID and active status
     */
    fun findBySalonIdAndIsActive(salonId: String, isActive: Boolean): Optional<SalonWebPreferencesEntity>
}
