package ro.animaliaprogramari.animalia.adapter.inbound.rest

import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.application.query.GetAvailableTimeSlotsQuery
import ro.animaliaprogramari.animalia.application.usecase.CreateProtoAppointmentCommand
import ro.animaliaprogramari.animalia.application.usecase.FinalizeBookingCommand
import ro.animaliaprogramari.animalia.application.usecase.OnlineBookingManagementUseCase
import ro.animaliaprogramari.animalia.application.usecase.OnlineBookingQueryUseCase
import ro.animaliaprogramari.animalia.domain.model.AppointmentId
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.ServiceId
import java.time.LocalTime

/**
 * Public REST controller for online booking
 * No authentication required - accessible to public clients
 */
@RestController
@RequestMapping("/public/salons/{salonId}/online-booking")
@CrossOrigin(origins = ["*"])
class PublicOnlineBookingController(
    private val onlineBookingQueryUseCase: OnlineBookingQueryUseCase,
    private val onlineBookingManagementUseCase: OnlineBookingManagementUseCase,
) {
    private val logger = LoggerFactory.getLogger(PublicOnlineBookingController::class.java)

    /**
     * Get available time slots for a specific date
     */
    @GetMapping("/available-slots")
    fun getAvailableTimeSlots(
        @PathVariable salonId: String,
        @RequestParam date: String, // yyyy-MM-dd format
        @RequestParam serviceIds: List<String>,
    ): ResponseEntity<ApiResponse<AvailableTimeSlotsDto>> {
        logger.info("Getting available time slots for salon: $salonId, date: $date")

        val query = GetAvailableTimeSlotsQuery(
            salonId = SalonId.of(salonId),
            date = java.time.LocalDate.parse(date),
            serviceIds = serviceIds.map { ServiceId.of(it) },
        )

        val result = onlineBookingQueryUseCase.getAvailableTimeSlots(query)

        val response = AvailableTimeSlotsDto(
            date = result.date,
            slots = result.slots.map { slot ->
                TimeSlotDto(
                    startTime = slot.startTime,
                    endTime = slot.endTime,
                    availableStaffIds = slot.availableStaffIds,
                )
            },
        )

        return ResponseEntity.ok(ApiResponse.success(response))
    }

    /**
     * Create a proto-appointment to reserve a time slot
     */
    @PostMapping("/proto-appointment")
    fun createProtoAppointment(
        @PathVariable salonId: String,
        @RequestBody request: CreateProtoAppointmentRequest,
    ): ResponseEntity<ApiResponse<ProtoAppointmentResponse>> {
        logger.info("Creating proto-appointment for salon: $salonId, date: ${request.date}, time: ${request.startTime}")

        val command = CreateProtoAppointmentCommand(
            salonId = SalonId.of(salonId),
            date = request.date,
            startTime = LocalTime.parse(request.startTime),
            endTime = LocalTime.parse(request.endTime),
            serviceIds = request.serviceIds.map { ServiceId.of(it) },
        )

        val result = onlineBookingManagementUseCase.createProtoAppointment(command)

        val response = ProtoAppointmentResponse(
            appointmentId = result.appointmentId.value,
            expiresAt = result.expiresAt,
        )

        return ResponseEntity.status(HttpStatus.CREATED)
            .body(ApiResponse.success(response))
    }

    /**
     * Finalize a booking by providing client and pet information
     */
    @PostMapping("/finalize")
    fun finalizeBooking(
        @PathVariable salonId: String,
        @RequestBody request: FinalizeBookingRequest,
    ): ResponseEntity<ApiResponse<FinalizeBookingResponse>> {
        logger.info("Finalizing booking for proto-appointment: ${request.appointmentId}")

        val command = FinalizeBookingCommand(
            appointmentId = AppointmentId.of(request.appointmentId),
            clientPhone = request.clientPhone,
            petName = request.petName,
            petSpecies = request.petSpecies,
            petBreed = request.petBreed,
            petSize = request.petSize,
            clientName = request.clientName,
            notes = request.notes,
            password = request.password,
        )

        val result = onlineBookingManagementUseCase.finalizeBooking(command)

        val response = FinalizeBookingResponse(
            appointmentId = result.appointmentId,
            status = result.status.name,
            isAutoConfirmed = result.isAutoConfirmed,
            message = result.message,
        )

        return ResponseEntity.status(HttpStatus.CREATED)
            .body(ApiResponse.success(response))
    }
}

