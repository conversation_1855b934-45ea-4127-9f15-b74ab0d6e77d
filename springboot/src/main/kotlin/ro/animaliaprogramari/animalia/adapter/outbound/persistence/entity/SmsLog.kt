package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

@Entity
@Table(name = "sms_logs")
data class SmsLog(
    @Id
    @Column(name = "id", nullable = false)
    val id: String = "",

    @Column(name = "salon_id", nullable = false)
    val salonId: String = "",

    @Column(name = "appointment_id", nullable = true)
    val appointmentId: String? = null,

    @Column(name = "client_id", nullable = true)
    val clientId: String? = null,

    @Column(name = "phone_number", nullable = false)
    val phoneNumber: String = "",

    @Column(name = "message_content", nullable = false, columnDefinition = "TEXT")
    val messageContent: String = "",

    @Enumerated(EnumType.STRING)
    @Column(name = "message_type", nullable = false)
    val messageType: SmsMessageType = SmsMessageType.CUSTOM,

    @Enumerated(EnumType.STRING)
    @Column(name = "direction", nullable = false)
    val direction: SmsDirection = SmsDirection.OUTGOING,

    @Column(name = "client_name", nullable = true)
    val clientName: String? = null,

    @Column(name = "pet_name", nullable = true)
    val petName: String? = null,

    @Column(name = "appointment_date", nullable = true)
    val appointmentDate: LocalDate? = null,

    @Column(name = "appointment_time", nullable = true)
    val appointmentTime: LocalTime? = null,

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    val status: SmsStatus = SmsStatus.SENT,

    @Column(name = "sent_at", nullable = false)
    val sentAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now()
)

enum class SmsDirection {
    OUTGOING,  // Message sent from salon to client
    INCOMING   // Message received from client
}

enum class SmsMessageType {
    APPOINTMENT_CONFIRMATION,
    REPLY,
    APPOINTMENT_CANCELLATION, // cancelation and reschedule messages are sent if confirmation is enabled
    APPOINTMENT_RESCHEDULE,
    REMINDER,
    FOLLOW_UP,
    APPOINTMENT_COMPLETION,
    BIRTHDAY,
    MASS_SMS,
    CUSTOM
}

enum class SmsStatus {
    SENT,
    FAILED,
    PENDING
}
