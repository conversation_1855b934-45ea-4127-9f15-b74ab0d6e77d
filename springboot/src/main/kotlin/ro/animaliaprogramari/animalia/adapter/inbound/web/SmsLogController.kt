package ro.animaliaprogramari.animalia.adapter.inbound.web

import org.springframework.data.domain.Page
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ApiResponse
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsLog
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsMessageType
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsStatus
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsDirection
import ro.animaliaprogramari.animalia.application.service.SmsLogService
import ro.animaliaprogramari.animalia.application.service.SmsStatistics
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@RestController
@RequestMapping("/salons/{salonId}/sms-logs")
class SmsLogController(
    private val smsLogService: SmsLogService
) {

    /**
     * Get SMS logs for a salon with pagination
     */
    @GetMapping
    fun getSmsLogs(
        @PathVariable salonId: String,
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "10") size: Int,
        @RequestParam(required = false) messageType: SmsMessageType?,
        @RequestParam(required = false) status: SmsStatus?,
        @RequestParam(required = false) startDate: String?,
        @RequestParam(required = false) endDate: String?
    ): ResponseEntity<ApiResponse<Page<SmsLogResponse>>> {

        val smsLogs = when {
            messageType != null -> smsLogService.getSmsLogsByType(salonId, messageType, page, size)
            status != null -> smsLogService.getSmsLogsByStatus(salonId, status, page, size)
            startDate != null && endDate != null -> {
                val start = LocalDateTime.parse(startDate, DateTimeFormatter.ISO_LOCAL_DATE_TIME)
                val end = LocalDateTime.parse(endDate, DateTimeFormatter.ISO_LOCAL_DATE_TIME)
                smsLogService.getSmsLogsInDateRange(salonId, start, end, page, size)
            }
            else -> smsLogService.getSmsLogs(salonId, page, size)
        }

        val response = smsLogs.map { it.toResponse() }
        return ResponseEntity.ok(ApiResponse.success(response))
    }

    /**
     * Get SMS statistics for a salon
     */
    @GetMapping("/statistics")
    fun getSmsStatistics(@PathVariable salonId: String): ResponseEntity<SmsStatistics> {
        val statistics = smsLogService.getSmsStatistics(salonId)
        return ResponseEntity.ok(statistics)
    }

    /**
     * Get SMS logs for a specific appointment
     */
    @GetMapping("/appointment/{appointmentId}")
    fun getSmsLogsForAppointment(@PathVariable appointmentId: String): ResponseEntity<List<SmsLogResponse>> {
        val smsLogs = smsLogService.getSmsLogsForAppointment(appointmentId)
        val response = smsLogs.map { it.toResponse() }
        return ResponseEntity.ok(response)
    }

    /**
     * Get SMS logs for a specific client
     */
    @GetMapping("/client/{clientId}")
    fun getSmsLogsForClient(
        @PathVariable clientId: String,
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "10") size: Int
    ): ResponseEntity<Page<SmsLogResponse>> {
        val smsLogs = smsLogService.getSmsLogsForClient(clientId, page, size)
        val response = smsLogs.map { it.toResponse() }
        return ResponseEntity.ok(response)
    }
}

/**
 * Response DTO for SMS log
 */
data class SmsLogResponse(
    val id: String,
    val salonId: String,
    val appointmentId: String?,
    val clientId: String?,
    val phoneNumber: String,
    val messageContent: String,
    val messageType: SmsMessageType,
    val direction: SmsDirection,
    val clientName: String?,
    val petName: String?,
    val appointmentDate: LocalDate?,
    val appointmentTime: String?,
    val status: SmsStatus,
    val sentAt: LocalDateTime,
    val createdAt: LocalDateTime
)

/**
 * Extension function to convert SmsLog entity to response DTO
 */
private fun SmsLog.toResponse(): SmsLogResponse {
    return SmsLogResponse(
        id = this.id,
        salonId = this.salonId,
        appointmentId = this.appointmentId,
        clientId = this.clientId,
        phoneNumber = this.phoneNumber,
        messageContent = this.messageContent,
        messageType = this.messageType,
        direction = this.direction,
        clientName = this.clientName,
        petName = this.petName,
        appointmentDate = this.appointmentDate,
        appointmentTime = this.appointmentTime?.toString(),
        status = this.status,
        sentAt = this.sentAt,
        createdAt = this.createdAt
    )
}
