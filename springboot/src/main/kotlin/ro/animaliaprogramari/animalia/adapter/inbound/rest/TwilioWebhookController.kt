package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.servlet.http.HttpServletRequest
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationEventPublisher
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsMessageType
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsStatus
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsDirection
import ro.animaliaprogramari.animalia.application.service.SmsLogService
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.SpringClientRepository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.repository.SmsLogRepository
import ro.animaliaprogramari.animalia.application.port.outbound.AppointmentRepository
import ro.animaliaprogramari.animalia.domain.model.AppointmentId
import ro.animaliaprogramari.animalia.domain.model.AppointmentStatus
import ro.animaliaprogramari.animalia.domain.event.appointment.AppointmentConfirmedEvent
import ro.animaliaprogramari.animalia.domain.event.appointment.AppointmentCancelledEvent
import ro.animaliaprogramari.animalia.domain.model.UserId
import com.twilio.security.RequestValidator
import java.util.UUID

/**
 * REST controller for Twilio webhook events (incoming SMS/WhatsApp messages)
 * Uses Twilio SDK's RequestValidator for secure webhook signature verification
 */
@RestController
@RequestMapping("/twilio/webhook")
@Tag(name = "Twilio Webhooks", description = "Handle incoming SMS/WhatsApp messages from Twilio")
class TwilioWebhookController(
    private val smsLogService: SmsLogService,
    private val springClientRepository: SpringClientRepository,
    private val smsLogRepository: SmsLogRepository,
    private val appointmentRepository: AppointmentRepository,
    private val eventPublisher: ApplicationEventPublisher,
    @Value("\${twilio.auth-token:}") private val twilioAuthToken: String,
) {
    private val logger = LoggerFactory.getLogger(TwilioWebhookController::class.java)

    @GetMapping("/test")
    @Operation(summary = "Test Twilio webhook endpoint")
    fun testWebhook(): ResponseEntity<Map<String, String>> {
        logger.info("Twilio webhook test endpoint called")
        return ResponseEntity.ok(mapOf(
            "status" to "success",
            "message" to "Twilio webhook endpoint is working",
            "timestamp" to java.time.LocalDateTime.now().toString()
        ))
    }

    /**
     * Handle incoming SMS messages from Twilio
     * Twilio sends POST requests with form-encoded data
     */
    @PostMapping("/sms", consumes = [MediaType.APPLICATION_FORM_URLENCODED_VALUE])
    @Operation(summary = "Handle incoming SMS messages from Twilio")
    fun handleIncomingSms(
        @RequestParam("MessageSid") messageSid: String,
        @RequestParam("From") from: String,
        @RequestParam("To") to: String,
        @RequestParam("Body") body: String,
        @RequestParam("NumMedia", required = false, defaultValue = "0") numMedia: Int,
        @RequestParam(required = false) accountSid: String?,
        @RequestParam(required = false) messagingServiceSid: String?,
        request: HttpServletRequest
    ): ResponseEntity<String> {
        return try {
            logger.info("📱 Received incoming SMS from Twilio: MessageSid=$messageSid, From=$from, To=$to")
            logger.debug("SMS Body: $body")

            // Verify webhook authenticity using Twilio SDK
            if (twilioAuthToken.isNotBlank() && !verifyTwilioSignature(request)) {
                logger.warn("⚠️ Invalid Twilio webhook signature")
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body("<?xml version=\"1.0\" encoding=\"UTF-8\"?><Response></Response>")
            }

            // Process the incoming message
            processIncomingMessage(
                messageSid = messageSid,
                from = from,
                to = to,
                body = body,
                isWhatsApp = false,
                numMedia = numMedia
            )

            // Respond with TwiML (Twilio Markup Language)
            // Empty response means no auto-reply
            val twimlResponse = """
                <?xml version="1.0" encoding="UTF-8"?>
                <Response></Response>
            """.trimIndent()

            ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_XML)
                .body(twimlResponse)
        } catch (e: Exception) {
            logger.error("❌ Error processing incoming SMS from Twilio", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("<?xml version=\"1.0\" encoding=\"UTF-8\"?><Response></Response>")
        }
    }

    /**
     * Handle incoming WhatsApp messages from Twilio
     * WhatsApp messages from Twilio have "whatsapp:" prefix in phone numbers
     */
    @PostMapping("/whatsapp", consumes = [MediaType.APPLICATION_FORM_URLENCODED_VALUE])
    @Operation(summary = "Handle incoming WhatsApp messages from Twilio")
    fun handleIncomingWhatsApp(
        @RequestParam("MessageSid") messageSid: String,
        @RequestParam("From") from: String,
        @RequestParam("To") to: String,
        @RequestParam("Body") body: String,
        @RequestParam("NumMedia", required = false, defaultValue = "0") numMedia: Int,
        @RequestParam(required = false) accountSid: String?,
        @RequestParam(required = false) messagingServiceSid: String?,
        request: HttpServletRequest
    ): ResponseEntity<String> {
        logger.info("💬 Received incoming WhatsApp from Twilio: MessageSid=$messageSid, From=$from, To=$to")
        logger.debug("WhatsApp Body: $body")

        return try {
            // Verify webhook authenticity using Twilio SDK
            if (twilioAuthToken.isNotBlank() && !verifyTwilioSignature(request)) {
                logger.warn("⚠️ Invalid Twilio webhook signature for WhatsApp")
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body("<?xml version=\"1.0\" encoding=\"UTF-8\"?><Response></Response>")
            }

            // Process the incoming WhatsApp message
            processIncomingMessage(
                messageSid = messageSid,
                from = from,
                to = to,
                body = body,
                isWhatsApp = true,
                numMedia = numMedia
            )

            // Respond with TwiML
            val twimlResponse = """
                <?xml version="1.0" encoding="UTF-8"?>
                <Response></Response>
            """.trimIndent()

            logger.info("✅ Successfully processed WhatsApp message from Twilio")
            ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_XML)
                .body(twimlResponse)
        } catch (e: Exception) {
            logger.error("❌ CRITICAL ERROR processing incoming WhatsApp from Twilio", e)
            logger.error("❌ Exception type: ${e.javaClass.name}")
            logger.error("❌ Exception message: ${e.message}")
            logger.error("❌ Stack trace:", e)

            // Still return 200 to Twilio to avoid retries
            ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_XML)
                .body("<?xml version=\"1.0\" encoding=\"UTF-8\"?><Response></Response>")
        }
    }

    /**
     * Handle message status callbacks from Twilio (delivery status updates)
     */
    @PostMapping("/status", consumes = [MediaType.APPLICATION_FORM_URLENCODED_VALUE])
    @Operation(summary = "Handle message status callbacks from Twilio")
    fun handleStatusCallback(
        @RequestParam("MessageSid") messageSid: String,
        @RequestParam("MessageStatus") messageStatus: String,
        @RequestParam(required = false) errorCode: String?,
        @RequestParam(required = false) errorMessage: String?,
        request: HttpServletRequest
    ): ResponseEntity<String> {
        return try {
            logger.info("📊 Received status callback from Twilio: MessageSid=$messageSid, Status=$messageStatus")

            if (errorCode != null) {
                logger.warn("⚠️ Message error: Code=$errorCode, Message=$errorMessage")
            }

            // Verify webhook authenticity
            if (twilioAuthToken.isNotBlank() && !verifyTwilioSignature(request)) {
                logger.warn("⚠️ Invalid Twilio signature for status callback")
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("UNAUTHORIZED")
            }

            // TODO: Update SMS log status based on callback
            // Status values: queued, sending, sent, failed, delivered, undelivered, receiving, received

            ResponseEntity.ok("OK")
        } catch (e: Exception) {
            logger.error("❌ Error processing status callback from Twilio", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("ERROR")
        }
    }

    /**
     * Process incoming message and save to database
     * Smart salon detection: Uses conversation context to determine the correct salon
     * when multiple salons have the same client phone number
     */
    private fun processIncomingMessage(
        messageSid: String,
        from: String,
        to: String,
        body: String,
        isWhatsApp: Boolean,
        numMedia: Int
    ) {
        try {
            // Remove "whatsapp:" prefix if present
            val cleanFrom = from.removePrefix("whatsapp:")
            val cleanTo = to.removePrefix("whatsapp:")

            logger.info("🔍 Processing incoming message from $cleanFrom to $cleanTo")

            // STEP 1: Find the most recent conversation with this phone number
            // This helps us determine which salon the client is responding to
            val lastSmsToClient = smsLogRepository.findFirstByPhoneNumberOrderBySentAtDesc(cleanFrom)

            var targetSalonId: String? = null
            var targetClientId: String? = null
            var targetClientName: String? = null
            var targetAppointmentId: String? = null
            var targetAppointmentDate: java.time.LocalDate? = null
            var targetPetName: String? = null

            if (lastSmsToClient != null) {
                // Use the salon from the most recent conversation
                targetSalonId = lastSmsToClient.salonId
                targetClientId = lastSmsToClient.clientId
                targetClientName = lastSmsToClient.clientName
                // Associate with the same appointment as the last message sent to this client
                targetAppointmentId = lastSmsToClient.appointmentId
                targetAppointmentDate = lastSmsToClient.appointmentDate
                targetPetName = lastSmsToClient.petName

                logger.info("✅ Found conversation context: Salon=${targetSalonId}, Client=${targetClientName}")
                logger.info("📅 Last message was sent at: ${lastSmsToClient.sentAt}")
                if (targetAppointmentId != null) {
                    logger.info("📍 Associating incoming message with appointment: ${targetAppointmentId}")
                }
            } else {
                // STEP 2: No previous conversation found, try to find client in database
                logger.warn("⚠️ No previous conversation found for $cleanFrom, searching for client...")

                val client = springClientRepository.findFirstByPhone(cleanFrom)

                if (client != null) {
                    targetSalonId = client.salonId
                    targetClientId = client.id
                    targetClientName = client.name

                    logger.info("✅ Found client in database: ${client.name} (Salon: ${client.salonId})")
                } else {
                    logger.warn("⚠️ No client found with phone number: $cleanFrom")
                }
            }

            // STEP 3: Log the incoming message if we have salon context
            if (targetSalonId != null) {
                // Prepare message content with WhatsApp prefix if applicable
                val messageContent = if (isWhatsApp) {
                    "[WhatsApp] $body"
                } else {
                    body
                }

                // Add media indicator if present
                val finalMessageContent = if (numMedia > 0) {
                    "$messageContent [📎 $numMedia media file(s)]"
                } else {
                    messageContent
                }

                // Log the incoming message to database
                smsLogService.logSms(
                    salonId = targetSalonId,
                    phoneNumber = cleanFrom,
                    messageContent = finalMessageContent,
                    messageType = SmsMessageType.REPLY, // Incoming messages are custom type
                    appointmentId = targetAppointmentId, // Now associated with appointment from last message
                    clientId = targetClientId,
                    clientName = targetClientName,
                    petName = targetPetName, // Use pet name from last message
                    appointmentDate = targetAppointmentDate, // Use appointment date from last message
                    appointmentTime = null,
                    status = SmsStatus.SENT, // Incoming messages are already "sent" from client perspective
                    direction = SmsDirection.INCOMING // Mark as incoming message from client
                )

                logger.info("✅ Successfully logged incoming message from $targetClientName (Salon: $targetSalonId)")

                // STEP 4: Auto-confirm appointment if message contains confirmation keywords
                if (targetAppointmentId != null) {
                    handleAppointmentConfirmation(targetAppointmentId, body.trim())
                    handleAppointmentCancellation(targetAppointmentId, body.trim())
                }

                // TODO: You can add additional logic here:
                // - Send notifications to salon staff
                // - Auto-reply based on keywords
                // - Create appointments from specific keywords
                // - Integration with AI chatbot
            } else {
                logger.warn("⚠️ Cannot determine salon for message from $cleanFrom - message not logged")
                logger.info("📝 This is likely a message from an unknown sender or a client with no conversation history")
            }
        } catch (e: Exception) {
            logger.error("❌ Error processing incoming message: MessageSid=$messageSid", e)
            throw e
        }
    }

    /**
     * Handle appointment confirmation based on client's message
     * Detects confirmation keywords and updates appointment status to CONFIRMED
     */
    private fun handleAppointmentConfirmation(appointmentId: String, messageBody: String) {
        try {
            // Normalize message for comparison (lowercase, trim whitespace)
            val normalizedMessage = messageBody.lowercase().trim()

            // List of confirmation keywords (English and Romanian)
            val confirmationKeywords = listOf(
                "confirm",
                "confirmed",
                "yes",
                "ok",
                "okay",
                "da",
                "confirmare",
                "confirmat",
                "confirmă",
                "bine",
                "desigur",
                "sigur"
            )

            // Check if message contains any confirmation keyword
            val isConfirmation = confirmationKeywords.any { keyword ->
                normalizedMessage.contains(keyword)
            }

            if (isConfirmation) {
                logger.info("🎯 Detected confirmation keyword in message: '$messageBody'")

                // Load the appointment
                val appointment = appointmentRepository.findById(AppointmentId(appointmentId))

                if (appointment != null) {
                    // Check if appointment can be confirmed
                    // Do not allow confirmation if already CONFIRMED, CANCELLED, COMPLETED, or IN_PROGRESS
                    if (appointment.status != AppointmentStatus.CONFIRMED &&
                        appointment.status != AppointmentStatus.CANCELLED &&
                        appointment.status != AppointmentStatus.COMPLETED &&
                        appointment.status != AppointmentStatus.IN_PROGRESS) {

                        val updatedAppointment = appointment.copy(
                            status = AppointmentStatus.CONFIRMED,
                            updatedAt = java.time.LocalDateTime.now()
                        )

                        appointmentRepository.save(updatedAppointment)

                        logger.info("✅ Auto-confirmed appointment ${appointmentId} - Status changed to CONFIRMED")
                        logger.info("   Previous status: ${appointment.status}")
                        logger.info("   New status: ${updatedAppointment.status}")

                        // Publish AppointmentConfirmedEvent
                        try {
                            val event = AppointmentConfirmedEvent(
                                eventId = UUID.randomUUID().toString(),
                                occurredAt = java.time.LocalDateTime.now(),
                                aggregateId = appointmentId,
                                appointmentId = appointment.id,
                                clientId = appointment.clientId,
                                petId = appointment.petId,
                                appointmentDate = appointment.appointmentDate,
                                startTime = appointment.startTime,
                                salonId = appointment.salonId,
                                confirmedBy = "SMS"
                            )

                            eventPublisher.publishEvent(event)
                            logger.info("📢 Published AppointmentConfirmedEvent for appointment ${appointmentId}")
                        } catch (e: Exception) {
                            logger.error("❌ Failed to publish AppointmentConfirmedEvent", e)
                            // Don't throw - confirmation was successful, event publishing is secondary
                        }
                    } else {
                        logger.info("ℹ️ Appointment ${appointmentId} already has final status: ${appointment.status} - Cannot confirm via SMS")
                    }
                } else {
                    logger.warn("⚠️ Appointment ${appointmentId} not found - Cannot auto-confirm")
                }
            } else {
                logger.debug("ℹ️ Message does not contain confirmation keywords: '$messageBody'")
            }
        } catch (e: Exception) {
            logger.error("❌ Error handling appointment confirmation for ${appointmentId}", e)
            logger.error("   Message was: '$messageBody'")
            // Don't throw - we don't want confirmation failure to break message processing
        }
    }

    /**
     * Handle appointment cancellation based on client's message
     * Detects cancellation keywords and updates appointment status to CANCELLED
     */
    private fun handleAppointmentCancellation(appointmentId: String, messageBody: String) {
        try {
            // Normalize message for comparison (lowercase, trim whitespace)
            val normalizedMessage = messageBody.lowercase().trim()

            // List of cancellation keywords (English and Romanian)
            val cancellationKeywords = listOf(
                "cancel",
                "cancelled",
                "anulare",
                "anuleaza",
                "anulează",
                "anulat",
                "nu pot",
                "nu mai pot",
                "renunt",
                "renunț",
                "renunțare"
            )

            // Check if message contains any cancellation keyword
            val isCancellation = cancellationKeywords.any { keyword ->
                normalizedMessage.contains(keyword)
            }

            if (isCancellation) {
                logger.info("🚫 Detected cancellation keyword in message: '$messageBody'")

                // Load the appointment
                val appointment = appointmentRepository.findById(AppointmentId(appointmentId))

                if (appointment != null) {
                    // Check if appointment can be cancelled
                    // Do not allow cancellation if already CANCELLED, CONFIRMED, COMPLETED, or IN_PROGRESS
                    if (appointment.status != AppointmentStatus.CANCELLED &&
                        appointment.status != AppointmentStatus.CONFIRMED &&
                        appointment.status != AppointmentStatus.COMPLETED &&
                        appointment.status != AppointmentStatus.IN_PROGRESS) {

                        val updatedAppointment = appointment.copy(
                            status = AppointmentStatus.CANCELLED,
                            updatedAt = java.time.LocalDateTime.now()
                        )

                        appointmentRepository.save(updatedAppointment)

                        logger.info("✅ Auto-cancelled appointment ${appointmentId} - Status changed to CANCELLED")
                        logger.info("   Previous status: ${appointment.status}")
                        logger.info("   New status: ${updatedAppointment.status}")

                        // Publish AppointmentCancelledEvent
                        try {
                            val event = AppointmentCancelledEvent(
                                eventId = UUID.randomUUID().toString(),
                                occurredAt = java.time.LocalDateTime.now(),
                                aggregateId = appointmentId,
                                appointmentId = appointment.id,
                                clientId = appointment.clientId,
                                userId = UserId(appointment.staffId.value),
                                appointmentDate = appointment.appointmentDate,
                                reason = "Anulat de client prin SMS: $messageBody",
                                salonId = appointment.salonId
                            )

                            eventPublisher.publishEvent(event)
                            logger.info("📢 Published AppointmentCancelledEvent for appointment ${appointmentId}")
                        } catch (e: Exception) {
                            logger.error("❌ Failed to publish AppointmentCancelledEvent", e)
                            // Don't throw - cancellation was successful, event publishing is secondary
                        }
                    } else {
                        logger.info("ℹ️ Appointment ${appointmentId} already has final status: ${appointment.status} - Cannot cancel via SMS")
                    }
                } else {
                    logger.warn("⚠️ Appointment ${appointmentId} not found - Cannot auto-cancel")
                }
            } else {
                logger.debug("ℹ️ Message does not contain cancellation keywords: '$messageBody'")
            }
        } catch (e: Exception) {
            logger.error("❌ Error handling appointment cancellation for ${appointmentId}", e)
            logger.error("   Message was: '$messageBody'")
            // Don't throw - we don't want cancellation failure to break message processing
        }
    }

    /**
     * Verify Twilio webhook signature using the official Twilio SDK
     * The RequestValidator handles all the complexity of parameter formatting and HMAC computation
     * https://www.twilio.com/docs/usage/security#validating-requests
     */
    private fun verifyTwilioSignature(request: HttpServletRequest): Boolean {
        if (twilioAuthToken.isBlank()) {
            logger.warn("⚠️ Twilio auth token not configured, skipping signature verification")
            return true // Skip verification if not configured
        }

        try {
            val signature = request.getHeader("X-Twilio-Signature")
            if (signature == null) {
                logger.warn("⚠️ Missing X-Twilio-Signature header")
                return false
            }

            // Log all headers for debugging
            logger.info("📋 All request headers:")
            request.headerNames.toList().forEach { headerName ->
                logger.info("   {}: {}", headerName, request.getHeader(headerName))
            }

            // Get the request URL - handle ngrok and proxy scenarios
            val xForwardedProto = request.getHeader("X-Forwarded-Proto")
            val xForwardedHost = request.getHeader("X-Forwarded-Host")
            val xOriginalUrl = request.getHeader("X-Original-URL")

            logger.info("🔍 URL Detection:")
            logger.info("   X-Forwarded-Proto: {}", xForwardedProto)
            logger.info("   X-Forwarded-Host: {}", xForwardedHost)
            logger.info("   X-Original-URL: {}", xOriginalUrl)
            logger.info("   request.scheme: {}", request.scheme)
            logger.info("   request.serverName: {}", request.serverName)
            logger.info("   request.serverPort: {}", request.serverPort)
            logger.info("   request.requestURI: {}", request.requestURI)
            logger.info("   request.requestURL: {}", request.requestURL)

            // Priority: Use X-Original-URL if available (ngrok can set this), then X-Forwarded headers, then request properties
            val protocol: String
            val host: String
            val port: Int
            val requestURI: String

            if (!xOriginalUrl.isNullOrBlank()) {
                // X-Original-URL contains the full URL from ngrok
                logger.debug("📍 Using X-Original-URL for signature validation")
                val url = java.net.URI(xOriginalUrl).toURL()
                protocol = url.protocol
                host = url.host
                port = if (url.port == -1) {
                    if (protocol == "https") 443 else 80
                } else {
                    url.port
                }
                requestURI = url.path
            } else {
                // Fallback to X-Forwarded headers or request properties
                protocol = xForwardedProto ?: request.scheme
                host = xForwardedHost ?: request.serverName
                port = request.serverPort
                requestURI = request.requestURI
            }

            // Build the URL as Twilio would have used it
            val url = if ((protocol == "https" && port == 443) || (protocol == "http" && port == 80)) {
                "$protocol://$host$requestURI"
            } else {
                "$protocol://$host:$port$requestURI"
            }

            // Add query string if present
            val fullUrl = if (request.queryString != null) {
                "$url?${request.queryString}"
            } else {
                url
            }

            // Build the parameter map - RequestValidator REQUIRES a TreeMap (sorted) for correct HMAC validation
            val params = java.util.TreeMap<String, String>()
            request.parameterMap.forEach { (key, values) ->
                // Join multiple values with comma (standard for form-encoded data)
                params[key] = values.joinToString(",")
            }

            // Use Twilio SDK's RequestValidator
            val validator = RequestValidator(twilioAuthToken)
            val isValid = validator.validate(fullUrl, params, signature)

            logger.info("🔐 Twilio signature validation:")
            logger.info("   Final URL for validation: {}", fullUrl)
            logger.info("   Signature from header: {}", signature)
            logger.info("   Auth Token Length: {}", twilioAuthToken.length)
            logger.info("   Parameters count: {}", params.size)
            logger.info("   Validation result: {}", if (isValid) "✅ VALID" else "❌ INVALID")

            if (!isValid) {
                logger.warn("⚠️ Invalid Twilio signature.")
                logger.warn("   URL used: {}", fullUrl)
                logger.warn("   Expected signature from header: {}", signature)
                if (logger.isDebugEnabled) {
                    logger.debug("   Parameters: {}", params.toSortedMap())
                }
            } else {
                logger.info("✅ Valid Twilio webhook signature verified")
            }

            return isValid
        } catch (e: Exception) {
            logger.error("❌ Error verifying Twilio signature", e)
            logger.error("   Exception: {}", e.message)
            return false
        }
    }
}
