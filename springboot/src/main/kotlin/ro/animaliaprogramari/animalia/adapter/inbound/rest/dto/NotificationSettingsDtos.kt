package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.time.LocalDateTime

/**
 * Response DTO for notification settings
 */
@Schema(description = "Notification settings response")
data class NotificationSettingsResponse(
    @Schema(description = "Salon ID")
    @JsonProperty("salonId")
    val salonId: String,
    @Schema(description = "Whether push notifications are enabled")
    @JsonProperty("pushNotificationsEnabled")
    val pushNotificationsEnabled: Boolean,
    @Schema(description = "Sound preference")
    @JsonProperty("soundPreference")
    val soundPreference: String,
    @Schema(description = "Whether vibration is enabled")
    @JsonProperty("vibrationEnabled")
    val vibrationEnabled: <PERSON>olean,
    @Schema(description = "Do Not Disturb settings")
    @JsonProperty("doNotDisturb")
    val doNotDisturb: DoNotDisturbResponse,
    @Schema(description = "Notification rules")
    @JsonProperty("notificationRules")
    val notificationRules: NotificationRulesResponse,
    @Schema(description = "Last updated timestamp")
    @JsonProperty("updatedAt")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]")
    val updatedAt: LocalDateTime,
)

/**
 * Do Not Disturb settings response DTO
 */
@Schema(description = "Do Not Disturb settings")
data class DoNotDisturbResponse(
    @JsonProperty("enabled")
    val enabled: Boolean,
    @JsonProperty("startTime")
    val startTime: String, // HH:MM format
    @JsonProperty("endTime")
    val endTime: String, // HH:MM format
    @JsonProperty("allowCritical")
    val allowCritical: Boolean,
)

/**
 * Notification rules response DTO
 */
@Schema(description = "Notification rules details")
data class NotificationRulesResponse(
    @JsonProperty("newAppointments")
    val newAppointments: Boolean,
    @JsonProperty("appointmentCancellations")
    val appointmentCancellations: Boolean,
    @JsonProperty("appointmentRescheduled")
    val appointmentRescheduled: Boolean,
    @JsonProperty("defaultPriority")
    val defaultPriority: String,
)

/**
 * Request DTO for updating notification settings
 */
@Schema(description = "Update notification settings request")
data class UpdateNotificationSettingsRequest(
    @field:NotNull(message = "Push notifications enabled flag is required")
    @JsonProperty("pushNotificationsEnabled")
    val pushNotificationsEnabled: Boolean,
    @field:NotBlank(message = "Sound preference is required")
    @JsonProperty("soundPreference")
    val soundPreference: String,
    @field:NotNull(message = "Vibration enabled flag is required")
    @JsonProperty("vibrationEnabled")
    val vibrationEnabled: Boolean,
    @field:Valid
    @field:NotNull(message = "Do not disturb settings are required")
    @JsonProperty("doNotDisturb")
    val doNotDisturb: DoNotDisturbRequest,
    @field:Valid
    @field:NotNull(message = "Notification rules are required")
    @JsonProperty("notificationRules")
    val notificationRules: NotificationRulesRequest,
)

/**
 * Do Not Disturb settings request DTO
 */
@Schema(description = "Do Not Disturb request")
data class DoNotDisturbRequest(
    @field:NotNull(message = "Do not disturb enabled flag is required")
    @JsonProperty("enabled")
    val enabled: Boolean,
    @field:NotBlank(message = "Start time is required")
    @JsonProperty("startTime")
    val startTime: String, // HH:MM format
    @field:NotBlank(message = "End time is required")
    @JsonProperty("endTime")
    val endTime: String, // HH:MM format
    @field:NotNull(message = "Allow critical flag is required")
    @JsonProperty("allowCritical")
    val allowCritical: Boolean,
)

/**
 * Notification rules request DTO
 */
@Schema(description = "Notification rules request")
data class NotificationRulesRequest(
    @field:NotNull(message = "New appointments flag is required")
    @JsonProperty("newAppointments")
    val newAppointments: Boolean,
    @field:NotNull(message = "Appointment cancellations flag is required")
    @JsonProperty("appointmentCancellations")
    val appointmentCancellations: Boolean,
    @field:NotNull(message = "Appointment rescheduled flag is required")
    @JsonProperty("appointmentRescheduled")
    val appointmentRescheduled: Boolean,
    @field:NotBlank(message = "Default priority is required")
    @JsonProperty("defaultPriority")
    val defaultPriority: String,
)
