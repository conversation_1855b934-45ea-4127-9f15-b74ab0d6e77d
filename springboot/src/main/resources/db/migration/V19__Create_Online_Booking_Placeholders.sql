-- Create online_booking_placeholders table
CREATE TABLE IF NOT EXISTS online_booking_placeholders (
    id VARCHAR(255) PRIMARY KEY,
    salon_id VARCHAR(255) NOT NULL,
    date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    service_ids TEXT NOT NULL,
    created_at TIMESTAMP NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    CONSTRAINT fk_placeholder_salon FOREIGN KEY (salon_id) REFERENCES salons(id) ON DELETE CASCADE
);

-- Create index for efficient cleanup of expired placeholders
CREATE INDEX idx_placeholder_expires_at ON online_booking_placeholders(expires_at);

-- Create index for salon lookups
CREATE INDEX idx_placeholder_salon_id ON online_booking_placeholders(salon_id);

