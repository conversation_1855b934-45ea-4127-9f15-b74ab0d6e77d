-- Add direction column to sms_logs table to differentiate between incoming and outgoing messages
-- OUTGOING: Messages sent from salon to client
-- INCOMING: Messages received from client

ALTER TABLE IF EXISTS sms_logs
  ADD COLUMN IF NOT EXISTS direction VARCHAR(20) NOT NULL DEFAULT 'OUTGOING';

-- <PERSON><PERSON> indexes only if the table exists to avoid migration failure when table is missing
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_name = 'sms_logs' AND table_schema = current_schema()
  ) THEN
    EXECUTE 'CREATE INDEX IF NOT EXISTS idx_sms_logs_direction ON sms_logs(direction)';
    EXECUTE 'CREATE INDEX IF NOT EXISTS idx_sms_logs_salon_direction_sent ON sms_logs(salon_id, direction, sent_at DESC)';
  END IF;
END
$$;
