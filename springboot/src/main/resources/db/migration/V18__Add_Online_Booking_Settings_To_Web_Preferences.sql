-- Add online booking settings to salon_web_preferences table
-- This migration adds the ability to enable/disable online booking
-- and select which services are available for online booking

-- Add online_booking_enabled column
ALTER TABLE salon_web_preferences
ADD COLUMN IF NOT EXISTS online_booking_enabled BOOLEAN NOT NULL DEFAULT TRUE;

-- Add available_service_ids column (JSON array of service IDs)
ALTER TABLE salon_web_preferences
ADD COLUMN IF NOT EXISTS available_service_ids TEXT;

-- Add index for better performance
CREATE INDEX IF NOT EXISTS idx_salon_web_preferences_online_booking_enabled
ON salon_web_preferences(online_booking_enabled);

-- Add comments for documentation
COMMENT ON COLUMN salon_web_preferences.online_booking_enabled IS 'Toggle to enable/disable online booking for this salon';
COMMENT ON COLUMN salon_web_preferences.available_service_ids IS 'JSON array of service IDs available for online booking. Empty means all active services are available';

-- End of migration

