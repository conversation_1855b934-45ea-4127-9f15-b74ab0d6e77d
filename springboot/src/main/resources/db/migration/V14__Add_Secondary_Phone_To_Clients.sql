-- Add secondary phone column to clients table
ALTER TABLE clients ADD COLUMN IF NOT EXISTS secondary_phone VARCHAR(50);

-- Migrate existing data: split phone numbers separated by comma
-- If a phone number contains a comma, split it and store the second part in secondary_phone
UPDATE clients
SET secondary_phone = TRIM(SPLIT_PART(phone, ',', 2)),
    phone = TRIM(SPLIT_PART(phone, ',', 1))
WHERE phone LIKE '%,%'
  AND secondary_phone IS NULL;

-- Add comment to columns for documentation
COMMENT ON COLUMN clients.phone IS 'Primary phone number of the client';
COMMENT ON COLUMN clients.secondary_phone IS 'Secondary phone number of the client (optional)';

