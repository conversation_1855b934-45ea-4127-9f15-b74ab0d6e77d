package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

@DisplayName("CustomServiceData DTO")
class CustomServiceDataTest {

    companion object {
        // Test constants to avoid magic values
        private const val ORIGINAL_SERVICE_NAME = "Basic Grooming"
        private const val CUSTOM_SERVICE_NAME = "Premium Grooming"
        private const val CUSTOM_SERVICE_PRICE = 75.0
        private const val CUSTOM_SERVICE_DESCRIPTION = "Premium grooming with special care"
    }

    @Nested
    @DisplayName("DTO Creation")
    inner class DtoCreationTests {

        @Test
        @DisplayName("Should create DTO with all fields")
        fun shouldCreateDtoWithAllFields() {
            // Act
            val dto = CustomServiceData(
                originalServiceName = ORIGINAL_SERVICE_NAME,
                customName = CUSTOM_SERVICE_NAME,
                customPrice = CUSTOM_SERVICE_PRICE,
                customDescription = CUSTOM_SERVICE_DESCRIPTION
            )

            // Assert
            assertNotNull(dto)
            assertEquals(ORIGINAL_SERVICE_NAME, dto.originalServiceName)
            assertEquals(CUSTOM_SERVICE_NAME, dto.customName)
            assertEquals(CUSTOM_SERVICE_PRICE, dto.customPrice)
            assertEquals(CUSTOM_SERVICE_DESCRIPTION, dto.customDescription)
        }

        @Test
        @DisplayName("Should create DTO with null description")
        fun shouldCreateDtoWithNullDescription() {
            // Act
            val dto = CustomServiceData(
                originalServiceName = ORIGINAL_SERVICE_NAME,
                customName = CUSTOM_SERVICE_NAME,
                customPrice = CUSTOM_SERVICE_PRICE,
                customDescription = null
            )

            // Assert
            assertNotNull(dto)
            assertEquals(ORIGINAL_SERVICE_NAME, dto.originalServiceName)
            assertEquals(CUSTOM_SERVICE_NAME, dto.customName)
            assertEquals(CUSTOM_SERVICE_PRICE, dto.customPrice)
            assertNull(dto.customDescription)
        }

        @Test
        @DisplayName("Should create DTO with default null description")
        fun shouldCreateDtoWithDefaultNullDescription() {
            // Act
            val dto = CustomServiceData(
                originalServiceName = ORIGINAL_SERVICE_NAME,
                customName = CUSTOM_SERVICE_NAME,
                customPrice = CUSTOM_SERVICE_PRICE
            )

            // Assert
            assertNotNull(dto)
            assertEquals(ORIGINAL_SERVICE_NAME, dto.originalServiceName)
            assertEquals(CUSTOM_SERVICE_NAME, dto.customName)
            assertEquals(CUSTOM_SERVICE_PRICE, dto.customPrice)
            assertNull(dto.customDescription)
        }
    }

    @Nested
    @DisplayName("Field Validation")
    inner class FieldValidationTests {

        @Test
        @DisplayName("Should handle empty original service name")
        fun shouldHandleEmptyOriginalServiceName() {
            // Act
            val dto = CustomServiceData(
                originalServiceName = "",
                customName = CUSTOM_SERVICE_NAME,
                customPrice = CUSTOM_SERVICE_PRICE,
                customDescription = CUSTOM_SERVICE_DESCRIPTION
            )

            // Assert
            assertEquals("", dto.originalServiceName)
        }

        @Test
        @DisplayName("Should handle empty custom service name")
        fun shouldHandleEmptyCustomServiceName() {
            // Act
            val dto = CustomServiceData(
                originalServiceName = ORIGINAL_SERVICE_NAME,
                customName = "",
                customPrice = CUSTOM_SERVICE_PRICE,
                customDescription = CUSTOM_SERVICE_DESCRIPTION
            )

            // Assert
            assertEquals("", dto.customName)
        }

        @ParameterizedTest
        @ValueSource(doubles = [0.0, 0.01, 25.50, 100.0, 999.99, 9999.99])
        @DisplayName("Should handle various custom prices")
        fun shouldHandleVariousCustomPrices(price: Double) {
            // Act
            val dto = CustomServiceData(
                originalServiceName = ORIGINAL_SERVICE_NAME,
                customName = CUSTOM_SERVICE_NAME,
                customPrice = price,
                customDescription = CUSTOM_SERVICE_DESCRIPTION
            )

            // Assert
            assertEquals(price, dto.customPrice)
        }

        @Test
        @DisplayName("Should handle negative custom price")
        fun shouldHandleNegativeCustomPrice() {
            // Act
            val dto = CustomServiceData(
                originalServiceName = ORIGINAL_SERVICE_NAME,
                customName = CUSTOM_SERVICE_NAME,
                customPrice = -10.0,
                customDescription = CUSTOM_SERVICE_DESCRIPTION
            )

            // Assert
            assertEquals(-10.0, dto.customPrice)
        }

        @Test
        @DisplayName("Should handle empty custom description")
        fun shouldHandleEmptyCustomDescription() {
            // Act
            val dto = CustomServiceData(
                originalServiceName = ORIGINAL_SERVICE_NAME,
                customName = CUSTOM_SERVICE_NAME,
                customPrice = CUSTOM_SERVICE_PRICE,
                customDescription = ""
            )

            // Assert
            assertEquals("", dto.customDescription)
        }

        @Test
        @DisplayName("Should handle very long custom description")
        fun shouldHandleVeryLongCustomDescription() {
            // Arrange
            val longDescription = "A".repeat(1000)

            // Act
            val dto = CustomServiceData(
                originalServiceName = ORIGINAL_SERVICE_NAME,
                customName = CUSTOM_SERVICE_NAME,
                customPrice = CUSTOM_SERVICE_PRICE,
                customDescription = longDescription
            )

            // Assert
            assertEquals(longDescription, dto.customDescription)
        }
    }

    @Nested
    @DisplayName("Data Class Properties")
    inner class DataClassPropertiesTests {

        @Test
        @DisplayName("Should support equality comparison")
        fun shouldSupportEqualityComparison() {
            // Arrange
            val dto1 = CustomServiceData(
                originalServiceName = ORIGINAL_SERVICE_NAME,
                customName = CUSTOM_SERVICE_NAME,
                customPrice = CUSTOM_SERVICE_PRICE,
                customDescription = CUSTOM_SERVICE_DESCRIPTION
            )
            val dto2 = CustomServiceData(
                originalServiceName = ORIGINAL_SERVICE_NAME,
                customName = CUSTOM_SERVICE_NAME,
                customPrice = CUSTOM_SERVICE_PRICE,
                customDescription = CUSTOM_SERVICE_DESCRIPTION
            )

            // Assert
            assertEquals(dto1, dto2)
            assertEquals(dto1.hashCode(), dto2.hashCode())
        }

        @Test
        @DisplayName("Should support copy with modifications")
        fun shouldSupportCopyWithModifications() {
            // Arrange
            val originalDto = CustomServiceData(
                originalServiceName = ORIGINAL_SERVICE_NAME,
                customName = CUSTOM_SERVICE_NAME,
                customPrice = CUSTOM_SERVICE_PRICE,
                customDescription = CUSTOM_SERVICE_DESCRIPTION
            )

            // Act
            val modifiedDto = originalDto.copy(
                customName = "Modified Custom Name",
                customPrice = 100.0
            )

            // Assert
            assertEquals(ORIGINAL_SERVICE_NAME, modifiedDto.originalServiceName)
            assertEquals("Modified Custom Name", modifiedDto.customName)
            assertEquals(100.0, modifiedDto.customPrice)
            assertEquals(CUSTOM_SERVICE_DESCRIPTION, modifiedDto.customDescription)
        }

        @Test
        @DisplayName("Should have meaningful toString representation")
        fun shouldHaveMeaningfulToStringRepresentation() {
            // Arrange
            val dto = CustomServiceData(
                originalServiceName = ORIGINAL_SERVICE_NAME,
                customName = CUSTOM_SERVICE_NAME,
                customPrice = CUSTOM_SERVICE_PRICE,
                customDescription = CUSTOM_SERVICE_DESCRIPTION
            )

            // Act
            val stringRepresentation = dto.toString()

            // Assert
            assertNotNull(stringRepresentation)
            // Should contain all field values
            assertEquals(true, stringRepresentation.contains(ORIGINAL_SERVICE_NAME))
            assertEquals(true, stringRepresentation.contains(CUSTOM_SERVICE_NAME))
            assertEquals(true, stringRepresentation.contains(CUSTOM_SERVICE_PRICE.toString()))
            assertEquals(true, stringRepresentation.contains(CUSTOM_SERVICE_DESCRIPTION))
        }
    }

    @Nested
    @DisplayName("Special Characters and Unicode")
    inner class SpecialCharactersTests {

        @Test
        @DisplayName("Should handle special characters in service names")
        fun shouldHandleSpecialCharactersInServiceNames() {
            // Arrange
            val specialOriginalName = "Grooming & Spa Service (Premium)"
            val specialCustomName = "Custom Grooming & Spa Service (VIP) - 50% Off!"

            // Act
            val dto = CustomServiceData(
                originalServiceName = specialOriginalName,
                customName = specialCustomName,
                customPrice = CUSTOM_SERVICE_PRICE,
                customDescription = CUSTOM_SERVICE_DESCRIPTION
            )

            // Assert
            assertEquals(specialOriginalName, dto.originalServiceName)
            assertEquals(specialCustomName, dto.customName)
        }

        @Test
        @DisplayName("Should handle Unicode characters")
        fun shouldHandleUnicodeCharacters() {
            // Arrange
            val unicodeOriginalName = "Îngrijire Câini 🐕"
            val unicodeCustomName = "Îngrijire Personalizată Câini 🐕✨"
            val unicodeDescription = "Serviciu premium pentru câini cu îngrijire specială 🌟"

            // Act
            val dto = CustomServiceData(
                originalServiceName = unicodeOriginalName,
                customName = unicodeCustomName,
                customPrice = CUSTOM_SERVICE_PRICE,
                customDescription = unicodeDescription
            )

            // Assert
            assertEquals(unicodeOriginalName, dto.originalServiceName)
            assertEquals(unicodeCustomName, dto.customName)
            assertEquals(unicodeDescription, dto.customDescription)
        }
    }
}
