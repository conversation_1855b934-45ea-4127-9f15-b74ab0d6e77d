package ro.animaliaprogramari.animalia.adapter.inbound.rest

import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.*
import org.springframework.test.web.servlet.setup.MockMvcBuilders
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.notification.*
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.port.inbound.NotificationManagementUseCase
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDateTime

@DisplayName("Notifications Controller Tests")
class NotificationsControllerTest {

    private lateinit var mockMvc: MockMvc
    private lateinit var objectMapper: ObjectMapper
    private lateinit var notificationManagementUseCase: NotificationManagementUseCase
    private lateinit var notificationsController: NotificationsController

    companion object {
        private const val BASE_URL = "/notifications"
        private const val TEST_USER_ID = "user-123"
        private const val TEST_SALON_ID = "salon-456"
        private const val TEST_NOTIFICATION_ID = "notification-789"
        private const val TEST_FCM_TOKEN = "fcm-token-abc123"
        private const val TEST_DEVICE_ID = "device-xyz789"
        private const val TEST_DEVICE_TYPE = "MOBILE"
        private const val TEST_TITLE = "Test Notification"
        private const val TEST_MESSAGE = "This is a test notification"
        private const val TEST_TYPE = "PUSH"
        private const val DEFAULT_PAGE = 0
        private const val DEFAULT_PAGE_SIZE = 20
        private const val TEST_COUNT = 10
    }

    @BeforeEach
    fun setUp() {
        notificationManagementUseCase = mockk()
        notificationsController = NotificationsController(notificationManagementUseCase)
        objectMapper = ObjectMapper()
        mockMvc = MockMvcBuilders.standaloneSetup(notificationsController).build()

        mockkObject(SecurityUtils)
    }

    private fun createMockAuthenticatedUser(): AuthenticatedUser {
        return AuthenticatedUser(
            userId = UserId.of(TEST_USER_ID),
            firebaseUid = "firebase-uid-123",
            email = Email.of("<EMAIL>"),
            phoneNumber = "+40731446896",
            name = "Test User",
            role = UserRole.STAFF,
            currentSalonId = SalonId.of(TEST_SALON_ID),
            isActive = true,
            staffAssociations = listOf(
                Staff(
                    id = StaffId.generate(),
                    userId = UserId.of(TEST_USER_ID),
                    salonId = SalonId.of(TEST_SALON_ID),
                    role = StaffRole.GROOMER,
                    permissions = StaffPermissions.fullAccess(),
                    isActive = true,
                    nickname = "Test Staff"
                )
            )
        )
    }

    @Nested
    @DisplayName("POST /notifications/generate-test-data")
    inner class GenerateTestData {

        @Test
        @DisplayName("should generate test data successfully when authenticated")
        fun shouldGenerateTestDataSuccessfullyWhenAuthenticated() {
            // Given
            val mockUser = createMockAuthenticatedUser()
            val expectedResponse = GenerateTestDataResponse(
                notificationsCreated = TEST_COUNT,
                fcmTokensCreated = 2,
                message = "Test data generated successfully",
                notificationIds = listOf("notif-1", "notif-2")
            )

            every { SecurityUtils.getCurrentUser() } returns mockUser
            every { notificationManagementUseCase.generateTestData(any()) } returns expectedResponse

            // When & Then
            mockMvc.perform(
                post("$BASE_URL/generate-test-data")
                    .param("count", TEST_COUNT.toString())
            )
                .andExpect(status().isOk)
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.notificationsCreated").value(TEST_COUNT))
                .andExpect(jsonPath("$.data.fcmTokensCreated").value(2))
                .andExpect(jsonPath("$.data.message").value("Test data generated successfully"))

            verify { notificationManagementUseCase.generateTestData(any()) }
        }

        @Test
        @DisplayName("should return 401 when not authenticated")
        fun shouldReturn401WhenNotAuthenticated() {
            // Given
            every { SecurityUtils.getCurrentUser() } returns null

            // When & Then
            mockMvc.perform(post("$BASE_URL/generate-test-data"))
                .andExpect(status().isUnauthorized)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("User not authenticated"))

            verify(exactly = 0) { notificationManagementUseCase.generateTestData(any()) }
        }

        @Test
        @DisplayName("should return 400 when user has no salon association")
        fun shouldReturn400WhenUserHasNoSalonAssociation() {
            // Given
            val userWithoutSalon = AuthenticatedUser(
                userId = UserId.of(TEST_USER_ID),
                firebaseUid = "firebase-uid-123",
                email = Email.of("<EMAIL>"),
                phoneNumber = "+40731446896",
                name = "Test User",
                role = UserRole.STAFF,
                currentSalonId = null,
                isActive = true,
                staffAssociations = emptyList()
            )

            every { SecurityUtils.getCurrentUser() } returns userWithoutSalon

            // When & Then
            mockMvc.perform(post("$BASE_URL/generate-test-data"))
                .andExpect(status().isBadRequest)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("No salon associated with user"))

            verify(exactly = 0) { notificationManagementUseCase.generateTestData(any()) }
        }

        @Test
        @DisplayName("should handle service exception")
        fun shouldHandleServiceException() {
            // Given
            val mockUser = createMockAuthenticatedUser()
            every { SecurityUtils.getCurrentUser() } returns mockUser
            every { notificationManagementUseCase.generateTestData(any()) } throws RuntimeException("Database error")

            // When & Then
            mockMvc.perform(post("$BASE_URL/generate-test-data"))
                .andExpect(status().isInternalServerError)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("Failed to generate test data: Database error"))

            verify { notificationManagementUseCase.generateTestData(any()) }
        }
    }

    @Nested
    @DisplayName("GET /notifications/stats")
    inner class GetNotificationStats {

        @Test
        @DisplayName("should return notification stats successfully when authenticated")
        fun shouldReturnNotificationStatsSuccessfullyWhenAuthenticated() {
            // Given
            val mockUser = createMockAuthenticatedUser()
            val expectedStats = NotificationStatsResponse(
                totalNotifications = 100L,
                unreadCount = 15L,
                readCount = 85L,
                todayCount = 5L,
                weekCount = 25L,
                monthCount = 60L,
                byType = mapOf("PUSH" to 80L, "SMS" to 20L),
                additionalStats = mapOf("avgPerDay" to 3.2)
            )

            every { SecurityUtils.getCurrentUser() } returns mockUser
            every { notificationManagementUseCase.getNotificationStats(any()) } returns expectedStats

            // When & Then
            mockMvc.perform(get("$BASE_URL/stats"))
                .andExpect(status().isOk)
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.totalNotifications").value(100))
                .andExpect(jsonPath("$.data.unreadCount").value(15))
                .andExpect(jsonPath("$.data.readCount").value(85))
                .andExpect(jsonPath("$.data.todayCount").value(5))
                .andExpect(jsonPath("$.data.byType.PUSH").value(80))
                .andExpect(jsonPath("$.data.byType.SMS").value(20))

            verify { notificationManagementUseCase.getNotificationStats(any()) }
        }

        @Test
        @DisplayName("should return 401 when not authenticated")
        fun shouldReturn401WhenNotAuthenticated() {
            // Given
            every { SecurityUtils.getCurrentUser() } returns null

            // When & Then
            mockMvc.perform(get("$BASE_URL/stats"))
                .andExpect(status().isUnauthorized)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("User not authenticated"))

            verify(exactly = 0) { notificationManagementUseCase.getNotificationStats(any()) }
        }

        @Test
        @DisplayName("should handle service exception")
        fun shouldHandleServiceException() {
            // Given
            val mockUser = createMockAuthenticatedUser()
            every { SecurityUtils.getCurrentUser() } returns mockUser
            every { notificationManagementUseCase.getNotificationStats(any()) } throws RuntimeException("Stats service error")

            // When & Then
            mockMvc.perform(get("$BASE_URL/stats"))
                .andExpect(status().isInternalServerError)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("Failed to get notification stats: Stats service error"))

            verify { notificationManagementUseCase.getNotificationStats(any()) }
        }
    }

    @Nested
    @DisplayName("POST /notifications/mark-read")
    inner class MarkAllAsRead {

        @Test
        @DisplayName("should mark all notifications as read successfully")
        fun shouldMarkAllNotificationsAsReadSuccessfully() {
            // Given
            val mockUser = createMockAuthenticatedUser()
            val expectedResponse = BulkMarkAsReadResponse(
                notificationsMarked = 15,
                message = "All notifications marked as read"
            )

            every { SecurityUtils.getCurrentUser() } returns mockUser
            every { notificationManagementUseCase.markAllAsRead(any()) } returns expectedResponse

            // When & Then
            mockMvc.perform(post("$BASE_URL/mark-read"))
                .andExpect(status().isOk)
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.notificationsMarked").value(15))
                .andExpect(jsonPath("$.data.message").value("All notifications marked as read"))

            verify { notificationManagementUseCase.markAllAsRead(any()) }
        }

        @Test
        @DisplayName("should return 401 when not authenticated")
        fun shouldReturn401WhenNotAuthenticated() {
            // Given
            every { SecurityUtils.getCurrentUser() } returns null

            // When & Then
            mockMvc.perform(post("$BASE_URL/mark-read"))
                .andExpect(status().isUnauthorized)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("User not authenticated"))

            verify(exactly = 0) { notificationManagementUseCase.markAllAsRead(any()) }
        }

        @Test
        @DisplayName("should handle service exception")
        fun shouldHandleServiceException() {
            // Given
            val mockUser = createMockAuthenticatedUser()
            every { SecurityUtils.getCurrentUser() } returns mockUser
            every { notificationManagementUseCase.markAllAsRead(any()) } throws RuntimeException("Mark read error")

            // When & Then
            mockMvc.perform(post("$BASE_URL/mark-read"))
                .andExpect(status().isInternalServerError)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("Failed to mark notifications as read: Mark read error"))

            verify { notificationManagementUseCase.markAllAsRead(any()) }
        }
    }

    @Nested
    @DisplayName("POST /notifications/test")
    inner class SendTestNotification {

        @Test
        @DisplayName("should send test notification successfully")
        fun shouldSendTestNotificationSuccessfully() {
            // Given
            val mockUser = createMockAuthenticatedUser()
            val request = SendTestNotificationRequest(
                title = TEST_TITLE,
                message = TEST_MESSAGE,
                type = TEST_TYPE
            )
            val expectedResponse = SendTestNotificationResponse(
                success = true,
                notificationId = TEST_NOTIFICATION_ID,
                devicesNotified = 2,
                message = "Test notification sent successfully"
            )

            every { SecurityUtils.getCurrentUser() } returns mockUser
            every { notificationManagementUseCase.sendTestNotification(any()) } returns expectedResponse

            // When & Then
            mockMvc.perform(
                post("$BASE_URL/test")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isOk)
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.success").value(true))
                .andExpect(jsonPath("$.data.notificationId").value(TEST_NOTIFICATION_ID))
                .andExpect(jsonPath("$.data.devicesNotified").value(2))
                .andExpect(jsonPath("$.data.message").value("Test notification sent successfully"))

            verify { notificationManagementUseCase.sendTestNotification(any()) }
        }

        @Test
        @DisplayName("should return 401 when not authenticated")
        fun shouldReturn401WhenNotAuthenticated() {
            // Given
            val request = SendTestNotificationRequest(
                title = TEST_TITLE,
                message = TEST_MESSAGE,
                type = TEST_TYPE
            )
            every { SecurityUtils.getCurrentUser() } returns null

            // When & Then
            mockMvc.perform(
                post("$BASE_URL/test")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isUnauthorized)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("User not authenticated"))

            verify(exactly = 0) { notificationManagementUseCase.sendTestNotification(any()) }
        }

        @Test
        @DisplayName("should handle service exception")
        fun shouldHandleServiceException() {
            // Given
            val mockUser = createMockAuthenticatedUser()
            val request = SendTestNotificationRequest(
                title = TEST_TITLE,
                message = TEST_MESSAGE,
                type = TEST_TYPE
            )
            every { SecurityUtils.getCurrentUser() } returns mockUser
            every { notificationManagementUseCase.sendTestNotification(any()) } throws RuntimeException("FCM error")

            // When & Then
            mockMvc.perform(
                post("$BASE_URL/test")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isInternalServerError)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("Failed to send test notification: FCM error"))

            verify { notificationManagementUseCase.sendTestNotification(any()) }
        }
    }

    @Nested
    @DisplayName("GET /notifications")
    inner class GetAllNotifications {

        @Test
        @DisplayName("should return paginated notifications successfully")
        fun shouldReturnPaginatedNotificationsSuccessfully() {
            // Given
            val mockUser = createMockAuthenticatedUser()
            val notificationResponse = NotificationResponse(
                id = TEST_NOTIFICATION_ID,
                title = TEST_TITLE,
                message = TEST_MESSAGE,
                type = TEST_TYPE,
                read = false,
                timestamp = LocalDateTime.now(),
                metadata = mapOf("salonId" to TEST_SALON_ID)
            )
            val expectedResponse = PaginatedNotificationsResponse(
                notifications = listOf(notificationResponse),
                totalCount = 1L,
                unreadCount = 1L,
                page = DEFAULT_PAGE,
                pageSize = DEFAULT_PAGE_SIZE,
                hasMore = false
            )

            every { SecurityUtils.getCurrentUser() } returns mockUser
            every { notificationManagementUseCase.getUserNotifications(any()) } returns expectedResponse

            // When & Then
            mockMvc.perform(
                get(BASE_URL)
                    .param("page", DEFAULT_PAGE.toString())
                    .param("pageSize", DEFAULT_PAGE_SIZE.toString())
                    .param("unreadOnly", "false")
            )
                .andExpect(status().isOk)
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.notifications").isArray)
                .andExpect(jsonPath("$.data.notifications[0].id").value(TEST_NOTIFICATION_ID))
                .andExpect(jsonPath("$.data.notifications[0].title").value(TEST_TITLE))
                .andExpect(jsonPath("$.data.notifications[0].read").value(false))
                .andExpect(jsonPath("$.data.totalCount").value(1))
                .andExpect(jsonPath("$.data.unreadCount").value(1))
                .andExpect(jsonPath("$.data.page").value(DEFAULT_PAGE))
                .andExpect(jsonPath("$.data.pageSize").value(DEFAULT_PAGE_SIZE))
                .andExpect(jsonPath("$.data.hasMore").value(false))

            verify { notificationManagementUseCase.getUserNotifications(any()) }
        }

        @Test
        @DisplayName("should filter by unread only")
        fun shouldFilterByUnreadOnly() {
            // Given
            val mockUser = createMockAuthenticatedUser()
            val expectedResponse = PaginatedNotificationsResponse(
                notifications = emptyList(),
                totalCount = 0L,
                unreadCount = 0L,
                page = DEFAULT_PAGE,
                pageSize = DEFAULT_PAGE_SIZE,
                hasMore = false
            )

            every { SecurityUtils.getCurrentUser() } returns mockUser
            every { notificationManagementUseCase.getUserNotifications(any()) } returns expectedResponse

            // When & Then
            mockMvc.perform(
                get(BASE_URL)
                    .param("unreadOnly", "true")
                    .param("type", "PUSH")
            )
                .andExpect(status().isOk)
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.notifications").isEmpty)
                .andExpect(jsonPath("$.data.totalCount").value(0))

            verify { notificationManagementUseCase.getUserNotifications(any()) }
        }

        @Test
        @DisplayName("should return 401 when not authenticated")
        fun shouldReturn401WhenNotAuthenticated() {
            // Given
            every { SecurityUtils.getCurrentUser() } returns null

            // When & Then
            mockMvc.perform(get(BASE_URL))
                .andExpect(status().isUnauthorized)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("User not authenticated"))

            verify(exactly = 0) { notificationManagementUseCase.getUserNotifications(any()) }
        }
    }

    @Nested
    @DisplayName("PUT /notifications/{id}/read")
    inner class MarkNotificationAsRead {

        @Test
        @DisplayName("should mark specific notification as read successfully")
        fun shouldMarkSpecificNotificationAsReadSuccessfully() {
            // Given
            val mockUser = createMockAuthenticatedUser()
            val expectedResponse = NotificationResponse(
                id = TEST_NOTIFICATION_ID,
                title = TEST_TITLE,
                message = TEST_MESSAGE,
                type = TEST_TYPE,
                read = true,
                timestamp = LocalDateTime.now(),
                metadata = mapOf("salonId" to TEST_SALON_ID)
            )

            every { SecurityUtils.getCurrentUser() } returns mockUser
            every { notificationManagementUseCase.markNotificationAsRead(any()) } returns expectedResponse

            // When & Then
            mockMvc.perform(put("$BASE_URL/$TEST_NOTIFICATION_ID/read"))
                .andExpect(status().isOk)
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value(TEST_NOTIFICATION_ID))
                .andExpect(jsonPath("$.data.read").value(true))

            verify { notificationManagementUseCase.markNotificationAsRead(any()) }
        }

        @Test
        @DisplayName("should return 404 when notification not found")
        fun shouldReturn404WhenNotificationNotFound() {
            // Given
            val mockUser = createMockAuthenticatedUser()
            every { SecurityUtils.getCurrentUser() } returns mockUser
            every { notificationManagementUseCase.markNotificationAsRead(any()) } throws
                EntityNotFoundException("Notification", "nonexistent-123")

            // When & Then
            mockMvc.perform(put("$BASE_URL/nonexistent-123/read"))
                .andExpect(status().isNotFound)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("Notification not found"))

            verify { notificationManagementUseCase.markNotificationAsRead(any()) }
        }

        @Test
        @DisplayName("should return 401 when not authenticated")
        fun shouldReturn401WhenNotAuthenticated() {
            // Given
            every { SecurityUtils.getCurrentUser() } returns null

            // When & Then
            mockMvc.perform(put("$BASE_URL/$TEST_NOTIFICATION_ID/read"))
                .andExpect(status().isUnauthorized)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("User not authenticated"))

            verify(exactly = 0) { notificationManagementUseCase.markNotificationAsRead(any()) }
        }
    }

    @Nested
    @DisplayName("POST /notifications/fcm-token")
    inner class RegisterFcmToken {

        @Test
        @DisplayName("should register FCM token successfully")
        fun shouldRegisterFcmTokenSuccessfully() {
            // Given
            val mockUser = createMockAuthenticatedUser()
            val request = RegisterFcmTokenRequest(
                salonId = TEST_SALON_ID,
                token = TEST_FCM_TOKEN,
                deviceId = TEST_DEVICE_ID,
                deviceType = TEST_DEVICE_TYPE
            )
            val expectedResponse = FcmTokenResponse(
                id = "token-id-123",
                userId = TEST_USER_ID,
                salonId = TEST_SALON_ID,
                token = TEST_FCM_TOKEN,
                deviceId = TEST_DEVICE_ID,
                deviceType = TEST_DEVICE_TYPE,
                isActive = true,
                lastUsed = LocalDateTime.now(),
                createdAt = LocalDateTime.now()
            )

            every { SecurityUtils.getCurrentUser() } returns mockUser
            every { notificationManagementUseCase.registerFcmToken(any()) } returns expectedResponse

            // When & Then
            mockMvc.perform(
                post("$BASE_URL/fcm-token")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isOk)
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.userId").value(TEST_USER_ID))
                .andExpect(jsonPath("$.data.salonId").value(TEST_SALON_ID))
                .andExpect(jsonPath("$.data.token").value(TEST_FCM_TOKEN))
                .andExpect(jsonPath("$.data.deviceType").value(TEST_DEVICE_TYPE))
                .andExpect(jsonPath("$.data.isActive").value(true))

            verify { notificationManagementUseCase.registerFcmToken(any()) }
        }

        @Test
        @DisplayName("should return 401 when not authenticated")
        fun shouldReturn401WhenNotAuthenticated() {
            // Given
            val request = RegisterFcmTokenRequest(
                salonId = TEST_SALON_ID,
                token = TEST_FCM_TOKEN,
                deviceId = TEST_DEVICE_ID,
                deviceType = TEST_DEVICE_TYPE
            )
            every { SecurityUtils.getCurrentUser() } returns null

            // When & Then
            mockMvc.perform(
                post("$BASE_URL/fcm-token")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isUnauthorized)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("User not authenticated"))

            verify(exactly = 0) { notificationManagementUseCase.registerFcmToken(any()) }
        }

        @Test
        @DisplayName("should handle service exception")
        fun shouldHandleServiceException() {
            // Given
            val mockUser = createMockAuthenticatedUser()
            val request = RegisterFcmTokenRequest(
                salonId = TEST_SALON_ID,
                token = TEST_FCM_TOKEN,
                deviceId = TEST_DEVICE_ID,
                deviceType = TEST_DEVICE_TYPE
            )
            every { SecurityUtils.getCurrentUser() } returns mockUser
            every { notificationManagementUseCase.registerFcmToken(any()) } throws RuntimeException("Token registration failed")

            // When & Then
            mockMvc.perform(
                post("$BASE_URL/fcm-token")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isInternalServerError)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("Failed to register FCM token: Token registration failed"))

            verify { notificationManagementUseCase.registerFcmToken(any()) }
        }
    }

    @Nested
    @DisplayName("DELETE /notifications/fcm-token")
    inner class DeactivateFcmToken {

        @Test
        @DisplayName("should deactivate FCM token successfully")
        fun shouldDeactivateFcmTokenSuccessfully() {
            // Given
            val mockUser = createMockAuthenticatedUser()
            val request = DeactivateFcmTokenRequest(token = TEST_FCM_TOKEN)

            every { SecurityUtils.getCurrentUser() } returns mockUser
            every { notificationManagementUseCase.deactivateFcmToken(any()) } returns true

            // When & Then
            mockMvc.perform(
                delete("$BASE_URL/fcm-token")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isOk)
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value(true))

            verify { notificationManagementUseCase.deactivateFcmToken(any()) }
        }

        @Test
        @DisplayName("should return 401 when not authenticated")
        fun shouldReturn401WhenNotAuthenticated() {
            // Given
            val request = DeactivateFcmTokenRequest(token = TEST_FCM_TOKEN)
            every { SecurityUtils.getCurrentUser() } returns null

            // When & Then
            mockMvc.perform(
                delete("$BASE_URL/fcm-token")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isUnauthorized)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("User not authenticated"))

            verify(exactly = 0) { notificationManagementUseCase.deactivateFcmToken(any()) }
        }

        @Test
        @DisplayName("should handle service exception")
        fun shouldHandleServiceException() {
            // Given
            val mockUser = createMockAuthenticatedUser()
            val request = DeactivateFcmTokenRequest(token = TEST_FCM_TOKEN)
            every { SecurityUtils.getCurrentUser() } returns mockUser
            every { notificationManagementUseCase.deactivateFcmToken(any()) } throws RuntimeException("Token deactivation failed")

            // When & Then
            mockMvc.perform(
                delete("$BASE_URL/fcm-token")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isInternalServerError)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("Failed to deactivate FCM token: Token deactivation failed"))

            verify { notificationManagementUseCase.deactivateFcmToken(any()) }
        }

        @Test
        @DisplayName("should handle token not found gracefully")
        fun shouldHandleTokenNotFoundGracefully() {
            // Given
            val mockUser = createMockAuthenticatedUser()
            val request = DeactivateFcmTokenRequest(token = "nonexistent-token")

            every { SecurityUtils.getCurrentUser() } returns mockUser
            every { notificationManagementUseCase.deactivateFcmToken(any()) } returns false

            // When & Then
            mockMvc.perform(
                delete("$BASE_URL/fcm-token")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isOk)
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value(false))

            verify { notificationManagementUseCase.deactivateFcmToken(any()) }
        }
    }
}
