package ro.animaliaprogramari.animalia.application.usecase

import io.mockk.*
import org.junit.jupiter.api.*
import ro.animaliaprogramari.animalia.application.port.outbound.AppointmentRepository
import ro.animaliaprogramari.animalia.application.port.outbound.BlockTimeRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffWorkingHoursRepository
import ro.animaliaprogramari.animalia.application.port.outbound.WorkingHoursRepository
import ro.animaliaprogramari.animalia.application.query.GetAvailableTimeSlotsQuery
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.service.SchedulingConflictService
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalTime
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * Unit tests for OnlineBookingQueryUseCaseImpl
 */
@DisplayName("OnlineBookingQueryUseCase")
class OnlineBookingQueryUseCaseTest {

    private val staffRepository = mockk<StaffRepository>()
    private val workingHoursRepository = mockk<WorkingHoursRepository>()
    private val staffWorkingHoursRepository = mockk<StaffWorkingHoursRepository>()
    private val appointmentRepository = mockk<AppointmentRepository>()
    private val blockTimeRepository = mockk<BlockTimeRepository>()
    private val conflictService = mockk<SchedulingConflictService>()

    private lateinit var useCase: OnlineBookingQueryUseCaseImpl

    companion object {
        private val TEST_SALON_ID = SalonId.generate()
        private val TEST_STAFF_ID_1 = StaffId.generate()
        private val TEST_STAFF_ID_2 = StaffId.generate()
        private val TEST_SERVICE_ID = ServiceId.generate()
        private val TEST_DATE = LocalDate.now().plusDays(1)
    }

    @BeforeEach
    fun setUp() {
        clearAllMocks()
        useCase = OnlineBookingQueryUseCaseImpl(
            staffRepository = staffRepository,
            workingHoursRepository = workingHoursRepository,
            staffWorkingHoursRepository = staffWorkingHoursRepository,
            appointmentRepository = appointmentRepository,
            blockTimeRepository = blockTimeRepository,
            conflictService = conflictService,
        )
    }

    @Nested
    @DisplayName("Get Available Time Slots")
    inner class GetAvailableTimeSlotsTests {

        @Test
        fun `should return empty list when salon is closed`() {
            // Given
            val query = GetAvailableTimeSlotsQuery(
                salonId = TEST_SALON_ID,
                date = TEST_DATE,
                serviceIds = listOf(TEST_SERVICE_ID),
            )

            val salonHours = createClosedSalonHours()
            every { workingHoursRepository.findBySalonId(TEST_SALON_ID) } returns salonHours

            // When
            val result = useCase.getAvailableTimeSlots(query)

            // Then
            assertNotNull(result)
            assertEquals(TEST_DATE, result.date)
            assertTrue(result.slots.isEmpty())
        }

        @Test
        fun `should return empty list when no staff available`() {
            // Given
            val query = GetAvailableTimeSlotsQuery(
                salonId = TEST_SALON_ID,
                date = TEST_DATE,
                serviceIds = listOf(TEST_SERVICE_ID),
            )

            val salonHours = createOpenSalonHours()
            every { workingHoursRepository.findBySalonId(TEST_SALON_ID) } returns salonHours
            every { staffRepository.findBySalonId(TEST_SALON_ID) } returns emptyList()

            // When
            val result = useCase.getAvailableTimeSlots(query)

            // Then
            assertNotNull(result)
            assertEquals(TEST_DATE, result.date)
            assertTrue(result.slots.isEmpty())
        }

        @Test
        fun `should return available slots when staff is available`() {
            // Given
            val query = GetAvailableTimeSlotsQuery(
                salonId = TEST_SALON_ID,
                date = TEST_DATE,
                serviceIds = listOf(TEST_SERVICE_ID),
            )

            val salonHours = createOpenSalonHours()
            val staff1 = createTestStaff(TEST_STAFF_ID_1)
            val staff2 = createTestStaff(TEST_STAFF_ID_2)

            every { workingHoursRepository.findBySalonId(TEST_SALON_ID) } returns salonHours
            every { staffRepository.findBySalonId(TEST_SALON_ID) } returns listOf(staff1, staff2)
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(any(), any()) } returns null
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { conflictService.isSlotAvailable(any(), any(), any(), any(), any(), any(), any(), any(), any()) } returns true

            // When
            val result = useCase.getAvailableTimeSlots(query)

            // Then
            assertNotNull(result)
            assertEquals(TEST_DATE, result.date)
            assertTrue(result.slots.isNotEmpty())

            // Verify slots have 30-minute granularity
            result.slots.forEach { slot ->
                val startTime = LocalTime.parse(slot.startTime)
                val endTime = LocalTime.parse(slot.endTime)
                assertEquals(30, java.time.Duration.between(startTime, endTime).toMinutes())
            }
        }

        @Test
        fun `should return slots with available staff IDs`() {
            // Given
            val query = GetAvailableTimeSlotsQuery(
                salonId = TEST_SALON_ID,
                date = TEST_DATE,
                serviceIds = listOf(TEST_SERVICE_ID),
            )

            val salonHours = createOpenSalonHours()
            val staff1 = createTestStaff(TEST_STAFF_ID_1)
            val staff2 = createTestStaff(TEST_STAFF_ID_2)

            every { workingHoursRepository.findBySalonId(TEST_SALON_ID) } returns salonHours
            every { staffRepository.findBySalonId(TEST_SALON_ID) } returns listOf(staff1, staff2)
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(any(), any()) } returns null
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()

            // Staff 1 is available, Staff 2 is not
            every {
                conflictService.isSlotAvailable(any(), any(), any(), TEST_STAFF_ID_1, any(), any(), any(), any(), any())
            } returns true
            every {
                conflictService.isSlotAvailable(any(), any(), any(), TEST_STAFF_ID_2, any(), any(), any(), any(), any())
            } returns false

            // When
            val result = useCase.getAvailableTimeSlots(query)

            // Then
            assertNotNull(result)
            assertTrue(result.slots.isNotEmpty())

            // Each slot should only have staff1 available
            result.slots.forEach { slot ->
                assertEquals(1, slot.availableStaffIds.size)
                assertTrue(slot.availableStaffIds.contains(TEST_STAFF_ID_1.value))
            }
        }

        @Test
        fun `should filter out slots with no available staff`() {
            // Given
            val query = GetAvailableTimeSlotsQuery(
                salonId = TEST_SALON_ID,
                date = TEST_DATE,
                serviceIds = listOf(TEST_SERVICE_ID),
            )

            val salonHours = createOpenSalonHours()
            val staff1 = createTestStaff(TEST_STAFF_ID_1)

            every { workingHoursRepository.findBySalonId(TEST_SALON_ID) } returns salonHours
            every { staffRepository.findBySalonId(TEST_SALON_ID) } returns listOf(staff1)
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(any(), any()) } returns null
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()

            // No staff available for any slot
            every { conflictService.isSlotAvailable(any(), any(), any(), any(), any(), any(), any(), any(), any()) } returns false

            // When
            val result = useCase.getAvailableTimeSlots(query)

            // Then
            assertNotNull(result)
            assertEquals(TEST_DATE, result.date)
            assertTrue(result.slots.isEmpty())
        }
    }

    // Helper methods
    private fun createOpenSalonHours(): WorkingHoursSettings {
        val workingDay = DaySchedule.workingDay(
            startTime = LocalTime.of(9, 0),
            endTime = LocalTime.of(17, 0),
        )

        val weeklySchedule = DayOfWeek.entries.associateWith { workingDay }

        return WorkingHoursSettings(
            salonId = TEST_SALON_ID,
            weeklySchedule = weeklySchedule,
            holidays = emptyList(),
            customClosures = emptyList(),
        )
    }

    private fun createClosedSalonHours(): WorkingHoursSettings {
        val closedDay = DaySchedule.dayOff()
        val weeklySchedule = DayOfWeek.entries.associateWith { closedDay }

        return WorkingHoursSettings(
            salonId = TEST_SALON_ID,
            weeklySchedule = weeklySchedule,
            holidays = emptyList(),
            customClosures = emptyList(),
        )
    }

    private fun createTestStaff(staffId: StaffId): Staff {
        return Staff(
            id = staffId,
            userId = UserId.generate(),
            salonId = TEST_SALON_ID,
            nickname = "Test Staff",
            role = StaffRole.GROOMER,
            permissions = StaffPermissions.defaultGroomerAccess(),
            isActive = true,
        )
    }

    @Nested
    @DisplayName("Time Slot Generation")
    inner class TimeSlotGeneration {

        @Test
        @DisplayName("Should generate correct number of 30-minute slots for a full day")
        fun shouldGenerateCorrectNumberOfSlots() {
            // Given: A full day from 00:00 to 23:00 (last slot)
            // Expected: 47 slots (we exclude 23:30-00:00 because it crosses midnight)
            // 00:00-00:30, 00:30-01:00, ..., 22:30-23:00, 23:00-23:30

            // When: Getting available time slots (which internally uses pre-calculated slots)
            every { staffRepository.findBySalonId(TEST_SALON_ID) } returns listOf(createTestStaff(TEST_STAFF_ID_1))
            every { workingHoursRepository.findBySalonId(TEST_SALON_ID) } returns listOf(
                WorkingHours(
                    id = WorkingHoursId.generate(),
                    salonId = TEST_SALON_ID,
                    dayOfWeek = TEST_DATE.dayOfWeek,
                    startTime = LocalTime.of(0, 0),
                    endTime = LocalTime.of(23, 59),
                    isActive = true
                )
            )
            every { staffWorkingHoursRepository.findByStaffId(TEST_STAFF_ID_1) } returns emptyList()
            every { appointmentRepository.findBySalonIdAndDateRange(TEST_SALON_ID, TEST_DATE, TEST_DATE) } returns emptyList()
            every { blockTimeRepository.findBySalonIdAndDateRange(TEST_SALON_ID, TEST_DATE, TEST_DATE) } returns emptyList()

            val query = GetAvailableTimeSlotsQuery(
                salonId = TEST_SALON_ID,
                date = TEST_DATE,
                serviceIds = listOf(TEST_SERVICE_ID)
            )

            // Then: Should complete without infinite loop and return slots
            val result = useCase.getAvailableTimeSlots(query)

            assertNotNull(result)
            // Should have 47 slots (00:00-00:30, 00:30-01:00, ..., 22:30-23:00, 23:00-23:30)
            // Note: 23:30-00:00 is excluded because it crosses midnight
            assertEquals(47, result.availableSlots.size, "Should generate exactly 47 30-minute slots for a full day")

            // Verify first slot
            val firstSlot = result.availableSlots.first()
            assertEquals("00:00", firstSlot.startTime)
            assertEquals("00:30", firstSlot.endTime)

            // Verify last slot
            val lastSlot = result.availableSlots.last()
            assertEquals("23:00", lastSlot.startTime)
            assertEquals("23:30", lastSlot.endTime)
        }

        @Test
        @DisplayName("Should not cause infinite loop with edge case times")
        fun shouldNotCauseInfiniteLoop() {
            // Given: Setup that would previously cause infinite loop
            every { staffRepository.findBySalonId(TEST_SALON_ID) } returns listOf(createTestStaff(TEST_STAFF_ID_1))
            every { workingHoursRepository.findBySalonId(TEST_SALON_ID) } returns listOf(
                WorkingHours(
                    id = WorkingHoursId.generate(),
                    salonId = TEST_SALON_ID,
                    dayOfWeek = TEST_DATE.dayOfWeek,
                    startTime = LocalTime.of(23, 0),
                    endTime = LocalTime.of(23, 59),
                    isActive = true
                )
            )
            every { staffWorkingHoursRepository.findByStaffId(TEST_STAFF_ID_1) } returns emptyList()
            every { appointmentRepository.findBySalonIdAndDateRange(TEST_SALON_ID, TEST_DATE, TEST_DATE) } returns emptyList()
            every { blockTimeRepository.findBySalonIdAndDateRange(TEST_SALON_ID, TEST_DATE, TEST_DATE) } returns emptyList()

            val query = GetAvailableTimeSlotsQuery(
                salonId = TEST_SALON_ID,
                date = TEST_DATE,
                serviceIds = listOf(TEST_SERVICE_ID)
            )

            // When/Then: Should complete without hanging
            val result = useCase.getAvailableTimeSlots(query)

            // Should complete successfully
            assertNotNull(result)
            assertTrue(result.availableSlots.isNotEmpty(), "Should have at least one available slot")
        }
    }
}

