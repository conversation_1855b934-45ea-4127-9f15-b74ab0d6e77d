//package ro.animaliaprogramari.animalia.suite
//
//import org.junit.jupiter.api.DisplayName
//import org.junit.platform.suite.api.IncludeClassNamePatterns
//import org.junit.platform.suite.api.SelectPackages
//import org.junit.platform.suite.api.Suite
//import org.junit.platform.suite.api.SuiteDisplayName
//
///**
// * Test suite for all user-specific notification settings tests
// *
// * This suite includes:
// * - Domain model tests
// * - Repository tests
// * - Use case tests
// * - Controller tests
// * - Integration tests
// * - Edge case tests
// *
// * Run this suite to execute all tests related to the user-specific notification settings feature.
// */
//@Suite
//@SuiteDisplayName("User-Specific Notification Settings Test Suite")
//@SelectPackages(
//    "ro.animaliaprogramari.animalia.domain.model",
//    "ro.animaliaprogramari.animalia.adapter.outbound.persistence",
//    "ro.animaliaprogramari.animalia.application.usecase",
//    "ro.animaliaprogramari.animalia.adapter.inbound.rest",
//    "ro.animaliaprogramari.animalia.integration"
//)
//@IncludeClassNamePatterns(".*UserSpecific.*NotificationSettings.*Test.*")
//@DisplayName("User-Specific Notification Settings Complete Test Suite")
//class UserSpecificNotificationSettingsTestSuite
//
///**
// * Domain-only test suite for faster feedback during development
// */
//@Suite
//@SuiteDisplayName("User-Specific Notification Settings Domain Test Suite")
//@SelectPackages("ro.animaliaprogramari.animalia.domain.model")
//@IncludeClassNamePatterns(".*UserSpecific.*NotificationSettings.*Test.*")
//@DisplayName("User-Specific Notification Settings Domain Tests")
//class UserSpecificNotificationSettingsDomainTestSuite
//
///**
// * Repository-only test suite for persistence layer testing
// */
//@Suite
//@SuiteDisplayName("User-Specific Notification Settings Repository Test Suite")
//@SelectPackages("ro.animaliaprogramari.animalia.adapter.outbound.persistence")
//@IncludeClassNamePatterns(".*UserSpecific.*NotificationSettings.*Test.*")
//@DisplayName("User-Specific Notification Settings Repository Tests")
//class UserSpecificNotificationSettingsRepositoryTestSuite
//
///**
// * Use case-only test suite for business logic testing
// */
//@Suite
//@SuiteDisplayName("User-Specific Notification Settings Use Case Test Suite")
//@SelectPackages("ro.animaliaprogramari.animalia.application.usecase")
//@IncludeClassNamePatterns(".*UserSpecific.*NotificationSettings.*Test.*")
//@DisplayName("User-Specific Notification Settings Use Case Tests")
//class UserSpecificNotificationSettingsUseCaseTestSuite
//
///**
// * Controller-only test suite for API layer testing
// */
//@Suite
//@SuiteDisplayName("User-Specific Notification Settings Controller Test Suite")
//@SelectPackages("ro.animaliaprogramari.animalia.adapter.inbound.rest")
//@IncludeClassNamePatterns(".*UserSpecific.*NotificationSettings.*Test.*")
//@DisplayName("User-Specific Notification Settings Controller Tests")
//class UserSpecificNotificationSettingsControllerTestSuite
//
///**
// * Integration-only test suite for end-to-end testing
// */
//@Suite
//@SuiteDisplayName("User-Specific Notification Settings Integration Test Suite")
//@SelectPackages("ro.animaliaprogramari.animalia.integration")
//@IncludeClassNamePatterns(".*UserSpecific.*NotificationSettings.*Test.*")
//@DisplayName("User-Specific Notification Settings Integration Tests")
//class UserSpecificNotificationSettingsIntegrationTestSuite
