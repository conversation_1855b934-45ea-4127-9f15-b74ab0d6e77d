package ro.animaliaprogramari.animalia.domain.service

import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalTime
import kotlin.test.*

@DisplayName("OptimizedConflictDetectionService - Data Classes and Enums")
class OptimizedConflictDetectionServiceTest {

    @Nested
    @DisplayName("TimeSlot")
    inner class TimeSlotTests {

        @Test
        fun `should create TimeSlot with valid times`() {
            // Given
            val startTime = LocalTime.of(9, 0)
            val endTime = LocalTime.of(10, 0)

            // When
            val timeSlot = TimeSlot.of(startTime, endTime)

            // Then
            assertEquals(startTime, timeSlot.startTime)
            assertEquals(endTime, timeSlot.endTime)
        }

        @Test
        fun `should throw exception when end time is before start time`() {
            // Given
            val startTime = LocalTime.of(10, 0)
            val endTime = LocalTime.of(9, 0)

            // When/Then
            assertFailsWith<IllegalArgumentException> {
                TimeSlot.of(startTime, endTime)
            }
        }

        @Test
        fun `should throw exception when start and end times are equal`() {
            // Given
            val time = LocalTime.of(10, 0)

            // When/Then
            assertFailsWith<IllegalArgumentException> {
                TimeSlot.of(time, time)
            }
        }

        @Test
        fun `should detect overlap when slots partially overlap`() {
            // Given
            val slot1 = TimeSlot.of(LocalTime.of(9, 0), LocalTime.of(10, 0))
            val slot2 = TimeSlot.of(LocalTime.of(9, 30), LocalTime.of(10, 30))

            // When/Then
            assertTrue(slot1.overlaps(slot2))
            assertTrue(slot2.overlaps(slot1))
        }

        @Test
        fun `should detect overlap when one slot contains another`() {
            // Given
            val outerSlot = TimeSlot.of(LocalTime.of(9, 0), LocalTime.of(11, 0))
            val innerSlot = TimeSlot.of(LocalTime.of(9, 30), LocalTime.of(10, 30))

            // When/Then
            assertTrue(outerSlot.overlaps(innerSlot))
            assertTrue(innerSlot.overlaps(outerSlot))
        }

        @Test
        fun `should not detect overlap when slots are adjacent`() {
            // Given
            val slot1 = TimeSlot.of(LocalTime.of(9, 0), LocalTime.of(10, 0))
            val slot2 = TimeSlot.of(LocalTime.of(10, 0), LocalTime.of(11, 0))

            // When/Then
            assertFalse(slot1.overlaps(slot2))
            assertFalse(slot2.overlaps(slot1))
        }

        @Test
        fun `should not detect overlap when slots are separate`() {
            // Given
            val slot1 = TimeSlot.of(LocalTime.of(9, 0), LocalTime.of(10, 0))
            val slot2 = TimeSlot.of(LocalTime.of(11, 0), LocalTime.of(12, 0))

            // When/Then
            assertFalse(slot1.overlaps(slot2))
            assertFalse(slot2.overlaps(slot1))
        }

        @Test
        fun `should detect overlap when slots are identical`() {
            // Given
            val slot1 = TimeSlot.of(LocalTime.of(9, 0), LocalTime.of(10, 0))
            val slot2 = TimeSlot.of(LocalTime.of(9, 0), LocalTime.of(10, 0))

            // When/Then
            assertTrue(slot1.overlaps(slot2))
            assertTrue(slot2.overlaps(slot1))
        }

        @Test
        fun `should handle edge case with minute precision`() {
            // Given
            val slot1 = TimeSlot.of(LocalTime.of(9, 59), LocalTime.of(10, 1))
            val slot2 = TimeSlot.of(LocalTime.of(10, 0), LocalTime.of(10, 2))

            // When/Then
            assertTrue(slot1.overlaps(slot2))
            assertTrue(slot2.overlaps(slot1))
        }

        @Test
        fun `should handle very short time slots`() {
            // Given
            val slot1 = TimeSlot.of(LocalTime.of(10, 0), LocalTime.of(10, 1))
            val slot2 = TimeSlot.of(LocalTime.of(10, 0, 30), LocalTime.of(10, 1, 30))

            // When/Then
            assertTrue(slot1.overlaps(slot2))
            assertTrue(slot2.overlaps(slot1))
        }

        @Test
        fun `should handle midnight boundary correctly`() {
            // Given
            val slot1 = TimeSlot.of(LocalTime.of(23, 30), LocalTime.of(23, 59))
            val slot2 = TimeSlot.of(LocalTime.of(0, 0), LocalTime.of(0, 30))

            // When/Then - These don't overlap as they're on different days
            assertFalse(slot1.overlaps(slot2))
            assertFalse(slot2.overlaps(slot1))
        }

        @Test
        fun `should handle second precision correctly`() {
            // Given
            val slot1 = TimeSlot.of(LocalTime.of(10, 0, 0), LocalTime.of(10, 0, 30))
            val slot2 = TimeSlot.of(LocalTime.of(10, 0, 15), LocalTime.of(10, 0, 45))

            // When/Then
            assertTrue(slot1.overlaps(slot2))
            assertTrue(slot2.overlaps(slot1))
        }

        @Test
        fun `should handle nanosecond precision correctly`() {
            // Given
            val slot1 = TimeSlot.of(LocalTime.of(10, 0, 0, 500_000_000), LocalTime.of(10, 0, 1, 0))
            val slot2 = TimeSlot.of(LocalTime.of(10, 0, 0, 750_000_000), LocalTime.of(10, 0, 1, 250_000_000))

            // When/Then
            assertTrue(slot1.overlaps(slot2))
            assertTrue(slot2.overlaps(slot1))
        }

        @Test
        fun `should not overlap with exact boundary times at nanosecond level`() {
            // Given
            val slot1 = TimeSlot.of(LocalTime.of(10, 0, 0, 0), LocalTime.of(10, 0, 0, 500_000_000))
            val slot2 = TimeSlot.of(LocalTime.of(10, 0, 0, 500_000_000), LocalTime.of(10, 0, 1, 0))

            // When/Then
            assertFalse(slot1.overlaps(slot2))
            assertFalse(slot2.overlaps(slot1))
        }
    }

    @Nested
    @DisplayName("DetailedScheduleConflict")
    inner class DetailedScheduleConflictTests {

        @Test
        fun `should create DetailedScheduleConflict with all fields`() {
            // Given
            val type = ScheduleConflictType.APPOINTMENT
            val message = "Conflicts with existing appointment"
            val timeSlot = TimeSlot.of(LocalTime.of(10, 0), LocalTime.of(11, 0))
            val appointmentId = AppointmentId.of("appt-123")

            // When
            val conflict = DetailedScheduleConflict(type, message, timeSlot, appointmentId)

            // Then
            assertEquals(type, conflict.type)
            assertEquals(message, conflict.message)
            assertEquals(timeSlot, conflict.timeSlot)
            assertEquals(appointmentId, conflict.conflictingAppointmentId)
        }

        @Test
        fun `should create DetailedScheduleConflict without appointment ID`() {
            // Given
            val type = ScheduleConflictType.SALON_CLOSED
            val message = "Salon is closed"
            val timeSlot = TimeSlot.of(LocalTime.of(10, 0), LocalTime.of(11, 0))

            // When
            val conflict = DetailedScheduleConflict(type, message, timeSlot)

            // Then
            assertEquals(type, conflict.type)
            assertEquals(message, conflict.message)
            assertEquals(timeSlot, conflict.timeSlot)
            assertNull(conflict.conflictingAppointmentId)
        }

        @Test
        fun `should provide meaningful toString representation`() {
            // Given
            val type = ScheduleConflictType.BLOCK_TIME
            val message = "Overlaps with blocked time"
            val timeSlot = TimeSlot.of(LocalTime.of(14, 30), LocalTime.of(15, 30))
            val conflict = DetailedScheduleConflict(type, message, timeSlot)

            // When
            val stringRepresentation = conflict.toString()

            // Then
            assertEquals("Conflict: Overlaps with blocked time (14:30-15:30)", stringRepresentation)
        }

        @Test
        fun `should handle toString with appointment ID`() {
            // Given
            val type = ScheduleConflictType.APPOINTMENT
            val message = "Conflicts with existing appointment"
            val timeSlot = TimeSlot.of(LocalTime.of(9, 15), LocalTime.of(10, 45))
            val appointmentId = AppointmentId.of("appt-456")
            val conflict = DetailedScheduleConflict(type, message, timeSlot, appointmentId)

            // When
            val stringRepresentation = conflict.toString()

            // Then
            assertEquals("Conflict: Conflicts with existing appointment (09:15-10:45)", stringRepresentation)
        }

        @Test
        fun `should handle empty message in toString`() {
            // Given
            val type = ScheduleConflictType.STAFF_UNAVAILABLE
            val message = ""
            val timeSlot = TimeSlot.of(LocalTime.of(12, 0), LocalTime.of(13, 0))
            val conflict = DetailedScheduleConflict(type, message, timeSlot)

            // When
            val stringRepresentation = conflict.toString()

            // Then
            assertEquals("Conflict:  (12:00-13:00)", stringRepresentation)
        }

        @Test
        fun `should handle special characters in message`() {
            // Given
            val type = ScheduleConflictType.BLOCK_TIME
            val message = "Overlaps with blocked time: Pauză & Masă (12:00-13:00) - Ștefan"
            val timeSlot = TimeSlot.of(LocalTime.of(12, 30), LocalTime.of(13, 30))
            val conflict = DetailedScheduleConflict(type, message, timeSlot)

            // When
            val stringRepresentation = conflict.toString()

            // Then
            assertEquals("Conflict: Overlaps with blocked time: Pauză & Masă (12:00-13:00) - Ștefan (12:30-13:30)", stringRepresentation)
        }

        @Test
        fun `should handle very long message in toString`() {
            // Given
            val type = ScheduleConflictType.APPOINTMENT
            val longMessage = "This is a very long conflict message that contains a lot of details about the specific conflict that occurred during the scheduling process and includes multiple pieces of information"
            val timeSlot = TimeSlot.of(LocalTime.of(8, 0), LocalTime.of(9, 0))
            val conflict = DetailedScheduleConflict(type, longMessage, timeSlot)

            // When
            val stringRepresentation = conflict.toString()

            // Then
            assertTrue(stringRepresentation.startsWith("Conflict: This is a very long conflict message"))
            assertTrue(stringRepresentation.endsWith("(08:00-09:00)"))
        }

        @Test
        fun `should handle null appointment ID correctly`() {
            // Given
            val type = ScheduleConflictType.BLOCK_TIME
            val message = "Block time conflict"
            val timeSlot = TimeSlot.of(LocalTime.of(15, 0), LocalTime.of(16, 0))
            val conflict = DetailedScheduleConflict(type, message, timeSlot, null)

            // When/Then
            assertNull(conflict.conflictingAppointmentId)
            assertEquals("Conflict: Block time conflict (15:00-16:00)", conflict.toString())
        }

        @Test
        fun `should support data class equality`() {
            // Given
            val type = ScheduleConflictType.STAFF_UNAVAILABLE
            val message = "Staff not available"
            val timeSlot = TimeSlot.of(LocalTime.of(11, 0), LocalTime.of(12, 0))
            val appointmentId = AppointmentId.of("appt-789")

            val conflict1 = DetailedScheduleConflict(type, message, timeSlot, appointmentId)
            val conflict2 = DetailedScheduleConflict(type, message, timeSlot, appointmentId)
            val conflict3 = DetailedScheduleConflict(type, "Different message", timeSlot, appointmentId)

            // When/Then
            assertEquals(conflict1, conflict2)
            assertNotEquals(conflict1, conflict3)
            assertEquals(conflict1.hashCode(), conflict2.hashCode())
        }

        @Test
        fun `should support data class copy functionality`() {
            // Given
            val original = DetailedScheduleConflict(
                type = ScheduleConflictType.APPOINTMENT,
                message = "Original message",
                timeSlot = TimeSlot.of(LocalTime.of(10, 0), LocalTime.of(11, 0)),
                conflictingAppointmentId = AppointmentId.of("original-id")
            )

            // When
            val copied = original.copy(message = "Updated message")

            // Then
            assertEquals(ScheduleConflictType.APPOINTMENT, copied.type)
            assertEquals("Updated message", copied.message)
            assertEquals(original.timeSlot, copied.timeSlot)
            assertEquals(original.conflictingAppointmentId, copied.conflictingAppointmentId)
        }
    }

    @Nested
    @DisplayName("ScheduleConflictType")
    inner class ScheduleConflictTypeTests {

        @Test
        fun `should have all expected conflict types`() {
            // Given/When
            val types = ScheduleConflictType.values()

            // Then
            assertEquals(4, types.size)
            assertTrue(types.contains(ScheduleConflictType.SALON_CLOSED))
            assertTrue(types.contains(ScheduleConflictType.STAFF_UNAVAILABLE))
            assertTrue(types.contains(ScheduleConflictType.APPOINTMENT))
            assertTrue(types.contains(ScheduleConflictType.BLOCK_TIME))
        }

        @Test
        fun `should have consistent enum names`() {
            // Given/When/Then
            assertEquals("SALON_CLOSED", ScheduleConflictType.SALON_CLOSED.name)
            assertEquals("STAFF_UNAVAILABLE", ScheduleConflictType.STAFF_UNAVAILABLE.name)
            assertEquals("APPOINTMENT", ScheduleConflictType.APPOINTMENT.name)
            assertEquals("BLOCK_TIME", ScheduleConflictType.BLOCK_TIME.name)
        }

        @Test
        fun `should support valueOf operations`() {
            // Given/When/Then
            assertEquals(ScheduleConflictType.SALON_CLOSED, ScheduleConflictType.valueOf("SALON_CLOSED"))
            assertEquals(ScheduleConflictType.STAFF_UNAVAILABLE, ScheduleConflictType.valueOf("STAFF_UNAVAILABLE"))
            assertEquals(ScheduleConflictType.APPOINTMENT, ScheduleConflictType.valueOf("APPOINTMENT"))
            assertEquals(ScheduleConflictType.BLOCK_TIME, ScheduleConflictType.valueOf("BLOCK_TIME"))
        }

        @Test
        fun `should throw exception for invalid valueOf`() {
            // Given/When/Then
            assertFailsWith<IllegalArgumentException> {
                ScheduleConflictType.valueOf("INVALID_TYPE")
            }
        }

        @Test
        fun `should have proper ordinal values`() {
            // Given/When/Then
            assertEquals(0, ScheduleConflictType.SALON_CLOSED.ordinal)
            assertEquals(1, ScheduleConflictType.STAFF_UNAVAILABLE.ordinal)
            assertEquals(2, ScheduleConflictType.APPOINTMENT.ordinal)
            assertEquals(3, ScheduleConflictType.BLOCK_TIME.ordinal)
        }

        @Test
        fun `should support enum comparison`() {
            // Given/When/Then
            assertTrue(ScheduleConflictType.SALON_CLOSED < ScheduleConflictType.STAFF_UNAVAILABLE)
            assertTrue(ScheduleConflictType.STAFF_UNAVAILABLE < ScheduleConflictType.APPOINTMENT)
            assertTrue(ScheduleConflictType.APPOINTMENT < ScheduleConflictType.BLOCK_TIME)
        }

        @Test
        fun `should support enum iteration`() {
            // Given
            val expectedTypes = listOf(
                ScheduleConflictType.SALON_CLOSED,
                ScheduleConflictType.STAFF_UNAVAILABLE,
                ScheduleConflictType.APPOINTMENT,
                ScheduleConflictType.BLOCK_TIME
            )

            // When
            val actualTypes = ScheduleConflictType.values().toList()

            // Then
            assertEquals(expectedTypes, actualTypes)
        }

        @Test
        fun `should have stable enum order`() {
            // Given/When
            val types = ScheduleConflictType.values()

            // Then - Order should be consistent for business logic
            assertEquals(ScheduleConflictType.SALON_CLOSED, types[0])
            assertEquals(ScheduleConflictType.STAFF_UNAVAILABLE, types[1])
            assertEquals(ScheduleConflictType.APPOINTMENT, types[2])
            assertEquals(ScheduleConflictType.BLOCK_TIME, types[3])
        }
    }

    @Nested
    @DisplayName("Integration Tests")
    inner class IntegrationTests {

        @Test
        fun `should work together in realistic conflict scenario`() {
            // Given
            val conflictTimeSlot = TimeSlot.of(LocalTime.of(14, 0), LocalTime.of(15, 0))
            val appointmentId = AppointmentId.of("conflict-appt-123")

            val conflict = DetailedScheduleConflict(
                type = ScheduleConflictType.APPOINTMENT,
                message = "Overlaps with existing appointment 14:00-15:00: CONFIRMED",
                timeSlot = conflictTimeSlot,
                conflictingAppointmentId = appointmentId
            )

            // When
            val requestedSlot = TimeSlot.of(LocalTime.of(14, 30), LocalTime.of(15, 30))
            val hasOverlap = conflictTimeSlot.overlaps(requestedSlot)
            val conflictDescription = conflict.toString()

            // Then
            assertTrue(hasOverlap)
            assertEquals(ScheduleConflictType.APPOINTMENT, conflict.type)
            assertEquals(appointmentId, conflict.conflictingAppointmentId)
            assertTrue(conflictDescription.contains("14:00-15:00"))
            assertTrue(conflictDescription.contains("CONFIRMED"))
        }

        @Test
        fun `should handle multiple conflict types in collection`() {
            // Given
            val timeSlot = TimeSlot.of(LocalTime.of(10, 0), LocalTime.of(11, 0))
            val conflicts = listOf(
                DetailedScheduleConflict(ScheduleConflictType.SALON_CLOSED, "Salon closed on Sunday", timeSlot),
                DetailedScheduleConflict(ScheduleConflictType.STAFF_UNAVAILABLE, "Staff not working", timeSlot),
                DetailedScheduleConflict(ScheduleConflictType.APPOINTMENT, "Appointment conflict", timeSlot, AppointmentId.of("appt-1")),
                DetailedScheduleConflict(ScheduleConflictType.BLOCK_TIME, "Block time conflict", timeSlot)
            )

            // When
            val conflictTypes = conflicts.map { it.type }.toSet()
            val hasAppointmentConflict = conflicts.any { it.conflictingAppointmentId != null }

            // Then
            assertEquals(4, conflictTypes.size)
            assertTrue(hasAppointmentConflict)
            assertTrue(conflictTypes.containsAll(ScheduleConflictType.values().toList()))
        }
    }
}
