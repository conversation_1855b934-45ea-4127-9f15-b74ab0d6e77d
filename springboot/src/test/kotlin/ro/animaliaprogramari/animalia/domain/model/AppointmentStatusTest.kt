package ro.animaliaprogramari.animalia.domain.model

import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * Comprehensive unit tests for AppointmentStatus enum
 * Tests all enum values and their behavior
 */
@DisplayName("AppointmentStatus Enum")
class AppointmentStatusTest {

    companion object {
        private val ALL_STATUSES = AppointmentStatus.values()
        private const val EXPECTED_STATUS_COUNT = 9
    }

    @Nested
    @DisplayName("Enum Values")
    inner class EnumValues {

        @Test
        @DisplayName("Should have all expected appointment statuses")
        fun shouldHaveAllExpectedAppointmentStatuses() {
            // Given & When
            val statuses = ALL_STATUSES

            // Then
            assertEquals(EXPECTED_STATUS_COUNT, statuses.size)
            assertTrue(statuses.contains(AppointmentStatus.SCHEDULED))
            assertTrue(statuses.contains(AppointmentStatus.CONFIRMED))
            assertTrue(statuses.contains(AppointmentStatus.IN_PROGRESS))
            assertTrue(statuses.contains(AppointmentStatus.COMPLETED))
            assertTrue(statuses.contains(AppointmentStatus.CANCELLED))
            assertTrue(statuses.contains(AppointmentStatus.NO_SHOW))
            assertTrue(statuses.contains(AppointmentStatus.RESCHEDULED))
            assertTrue(statuses.contains(AppointmentStatus.REQUESTED))
            assertTrue(statuses.contains(AppointmentStatus.PLACEHOLDER))
        }

        @Test
        @DisplayName("Should have correct enum names")
        fun shouldHaveCorrectEnumNames() {
            // Given & When & Then
            assertEquals("SCHEDULED", AppointmentStatus.SCHEDULED.name)
            assertEquals("CONFIRMED", AppointmentStatus.CONFIRMED.name)
            assertEquals("IN_PROGRESS", AppointmentStatus.IN_PROGRESS.name)
            assertEquals("COMPLETED", AppointmentStatus.COMPLETED.name)
            assertEquals("CANCELLED", AppointmentStatus.CANCELLED.name)
            assertEquals("NO_SHOW", AppointmentStatus.NO_SHOW.name)
            assertEquals("RESCHEDULED", AppointmentStatus.RESCHEDULED.name)
        }

        @Test
        @DisplayName("Should have correct ordinal values")
        fun shouldHaveCorrectOrdinalValues() {
            // Given & When & Then
            assertEquals(0, AppointmentStatus.SCHEDULED.ordinal)
            assertEquals(1, AppointmentStatus.CONFIRMED.ordinal)
            assertEquals(2, AppointmentStatus.IN_PROGRESS.ordinal)
            assertEquals(3, AppointmentStatus.COMPLETED.ordinal)
            assertEquals(4, AppointmentStatus.CANCELLED.ordinal)
            assertEquals(5, AppointmentStatus.NO_SHOW.ordinal)
            assertEquals(6, AppointmentStatus.RESCHEDULED.ordinal)
            assertEquals(7, AppointmentStatus.REQUESTED.ordinal)
            assertEquals(8, AppointmentStatus.PLACEHOLDER.ordinal)
        }
    }

    @Nested
    @DisplayName("Enum Conversion")
    inner class EnumConversion {

        @Test
        @DisplayName("Should convert from string using valueOf")
        fun shouldConvertFromStringUsingValueOf() {
            // Given & When & Then
            assertEquals(AppointmentStatus.SCHEDULED, AppointmentStatus.valueOf("SCHEDULED"))
            assertEquals(AppointmentStatus.CONFIRMED, AppointmentStatus.valueOf("CONFIRMED"))
            assertEquals(AppointmentStatus.IN_PROGRESS, AppointmentStatus.valueOf("IN_PROGRESS"))
            assertEquals(AppointmentStatus.COMPLETED, AppointmentStatus.valueOf("COMPLETED"))
            assertEquals(AppointmentStatus.CANCELLED, AppointmentStatus.valueOf("CANCELLED"))
            assertEquals(AppointmentStatus.NO_SHOW, AppointmentStatus.valueOf("NO_SHOW"))
            assertEquals(AppointmentStatus.RESCHEDULED, AppointmentStatus.valueOf("RESCHEDULED"))
        }

        @Test
        @DisplayName("Should convert to string using toString")
        fun shouldConvertToStringUsingToString() {
            // Given & When & Then
            assertEquals("SCHEDULED", AppointmentStatus.SCHEDULED.toString())
            assertEquals("CONFIRMED", AppointmentStatus.CONFIRMED.toString())
            assertEquals("IN_PROGRESS", AppointmentStatus.IN_PROGRESS.toString())
            assertEquals("COMPLETED", AppointmentStatus.COMPLETED.toString())
            assertEquals("CANCELLED", AppointmentStatus.CANCELLED.toString())
            assertEquals("NO_SHOW", AppointmentStatus.NO_SHOW.toString())
            assertEquals("RESCHEDULED", AppointmentStatus.RESCHEDULED.toString())
        }
    }

    @Nested
    @DisplayName("Enum Iteration")
    inner class EnumIteration {

        @Test
        @DisplayName("Should iterate through all values")
        fun shouldIterateThroughAllValues() {
            // Given
            val expectedStatuses = listOf(
                AppointmentStatus.SCHEDULED,
                AppointmentStatus.CONFIRMED,
                AppointmentStatus.IN_PROGRESS,
                AppointmentStatus.COMPLETED,
                AppointmentStatus.CANCELLED,
                AppointmentStatus.NO_SHOW,
                AppointmentStatus.RESCHEDULED,
                AppointmentStatus.REQUESTED,
                AppointmentStatus.PLACEHOLDER
            )

            // When
            val actualStatuses = ALL_STATUSES.toList()

            // Then
            assertEquals(expectedStatuses, actualStatuses)
        }

        @Test
        @DisplayName("Should maintain consistent ordering")
        fun shouldMaintainConsistentOrdering() {
            // Given & When
            val firstIteration = ALL_STATUSES.toList()
            val secondIteration = ALL_STATUSES.toList()

            // Then
            assertEquals(firstIteration, secondIteration)
        }
    }

    @Nested
    @DisplayName("Enum Equality")
    inner class EnumEquality {

        @Test
        @DisplayName("Should be equal to itself")
        fun shouldBeEqualToItself() {
            // Given & When & Then
            ALL_STATUSES.forEach { status ->
                assertEquals(status, status)
            }
        }

        @Test
        @DisplayName("Should have consistent hashCode")
        fun shouldHaveConsistentHashCode() {
            // Given & When & Then
            ALL_STATUSES.forEach { status ->
                assertEquals(status.hashCode(), status.hashCode())
            }
        }

        @Test
        @DisplayName("Should be comparable")
        fun shouldBeComparable() {
            // Given & When & Then
            assertTrue(AppointmentStatus.SCHEDULED < AppointmentStatus.CONFIRMED)
            assertTrue(AppointmentStatus.CONFIRMED < AppointmentStatus.IN_PROGRESS)
            assertTrue(AppointmentStatus.IN_PROGRESS < AppointmentStatus.COMPLETED)
            assertTrue(AppointmentStatus.COMPLETED < AppointmentStatus.CANCELLED)
            assertTrue(AppointmentStatus.CANCELLED < AppointmentStatus.NO_SHOW)
            assertTrue(AppointmentStatus.NO_SHOW < AppointmentStatus.RESCHEDULED)
        }
    }

    @Nested
    @DisplayName("Business Logic Validation")
    inner class BusinessLogicValidation {

        @Test
        @DisplayName("Should have appropriate statuses for appointment lifecycle")
        fun shouldHaveAppropriateStatusesForAppointmentLifecycle() {
            // Given & When & Then
            // Initial states
            assertTrue(ALL_STATUSES.contains(AppointmentStatus.SCHEDULED))

            // Confirmation states
            assertTrue(ALL_STATUSES.contains(AppointmentStatus.CONFIRMED))

            // Active states
            assertTrue(ALL_STATUSES.contains(AppointmentStatus.IN_PROGRESS))

            // Final states
            assertTrue(ALL_STATUSES.contains(AppointmentStatus.COMPLETED))
            assertTrue(ALL_STATUSES.contains(AppointmentStatus.CANCELLED))
            assertTrue(ALL_STATUSES.contains(AppointmentStatus.NO_SHOW))
            assertTrue(ALL_STATUSES.contains(AppointmentStatus.RESCHEDULED))
        }

        @Test
        @DisplayName("Should have distinct statuses for different outcomes")
        fun shouldHaveDistinctStatusesForDifferentOutcomes() {
            // Given
            val finalStatuses = setOf(
                AppointmentStatus.COMPLETED,
                AppointmentStatus.CANCELLED,
                AppointmentStatus.NO_SHOW,
                AppointmentStatus.RESCHEDULED
            )

            // When & Then
            assertEquals(4, finalStatuses.size) // All should be distinct
        }
    }

    @Nested
    @DisplayName("Edge Cases")
    inner class EdgeCases {

        @Test
        @DisplayName("Should handle enum comparison correctly")
        fun shouldHandleEnumComparisonCorrectly() {
            // Given
            val status1 = AppointmentStatus.SCHEDULED
            val status2 = AppointmentStatus.SCHEDULED
            val status3 = AppointmentStatus.CONFIRMED

            // When & Then
            assertTrue(status1 == status2)
            assertTrue(status1 === status2) // Same enum instance
            assertTrue(status1 != status3)
        }

        @Test
        @DisplayName("Should work correctly in collections")
        fun shouldWorkCorrectlyInCollections() {
            // Given
            val statusSet = setOf(
                AppointmentStatus.SCHEDULED,
                AppointmentStatus.CONFIRMED,
                AppointmentStatus.SCHEDULED // Duplicate
            )

            // When & Then
            assertEquals(2, statusSet.size) // Duplicates removed
            assertTrue(statusSet.contains(AppointmentStatus.SCHEDULED))
            assertTrue(statusSet.contains(AppointmentStatus.CONFIRMED))
        }

        @Test
        @DisplayName("Should work correctly as map keys")
        fun shouldWorkCorrectlyAsMapKeys() {
            // Given
            val statusMap = mapOf(
                AppointmentStatus.SCHEDULED to "Initial",
                AppointmentStatus.CONFIRMED to "Confirmed",
                AppointmentStatus.COMPLETED to "Done"
            )

            // When & Then
            assertEquals("Initial", statusMap[AppointmentStatus.SCHEDULED])
            assertEquals("Confirmed", statusMap[AppointmentStatus.CONFIRMED])
            assertEquals("Done", statusMap[AppointmentStatus.COMPLETED])
            assertEquals(null, statusMap[AppointmentStatus.CANCELLED])
        }
    }
}
