import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:animaliaproject/providers/side_menu_provider.dart';

/// Test widget that simulates the side navigation with toggle functionality
class TestSideNavigationWithToggle extends StatelessWidget {
  const TestSideNavigationWithToggle({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<SideMenuProvider>(
      builder: (context, sideMenuProvider, child) {
        return Scaffold(
          body: Stack(
            children: [
              Row(
                children: [
                  // Side navigation area
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                    width: sideMenuProvider.isVisible ? 240.0 : 0.0,
                    child: sideMenuProvider.isVisible
                        ? Container(
                            color: Theme.of(context).colorScheme.surface,
                            child: Column(
                              children: [
                                // Header with toggle button
                                Container(
                                  height: 64.0,
                                  padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
                                  child: Row(
                                    children: [
                                      Container(
                                        width: 28.0,
                                        height: 28.0,
                                        color: Colors.blue, // Mock logo for testing
                                      ),
                                      const SizedBox(width: 8.0),
                                      const Expanded(child: Text('Animalia')),
                                      IconButton(
                                        icon: const Icon(Icons.menu_open),
                                        onPressed: () async {
                                          await sideMenuProvider.toggleVisibility();
                                        },
                                        tooltip: 'Ascunde meniul lateral',
                                      ),
                                    ],
                                  ),
                                ),
                                const Expanded(child: Text('Navigation Items')),
                              ],
                            ),
                          )
                        : null,
                  ),
                  // Main content
                  const Expanded(
                    child: Center(child: Text('Main Content')),
                  ),
                ],
              ),
              // Toggle button when menu is hidden
              if (!sideMenuProvider.isVisible)
                Positioned(
                  top: 16.0,
                  left: 8.0,
                  child: Material(
                    elevation: 4,
                    borderRadius: BorderRadius.circular(20),
                    child: IconButton(
                      icon: const Icon(Icons.menu),
                      onPressed: () async {
                        await sideMenuProvider.toggleVisibility();
                      },
                      tooltip: 'Afișează meniul lateral',
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}

void main() {
  group('Side Navigation Toggle Button', () {
    late SideMenuProvider sideMenuProvider;

    setUp(() {
      SharedPreferences.setMockInitialValues({});
      sideMenuProvider = SideMenuProvider();
    });

    Widget createTestWidget({
      Size screenSize = const Size(1024, 768), // Default landscape tablet
    }) {
      return MaterialApp(
        home: ChangeNotifierProvider<SideMenuProvider>.value(
          value: sideMenuProvider,
          child: MediaQuery(
            data: MediaQueryData(size: screenSize),
            child: const TestSideNavigationWithToggle(),
          ),
        ),
      );
    }

    group('Toggle Button Visibility', () {
      testWidgets('should show toggle button inside menu when visible', (WidgetTester tester) async {
        await sideMenuProvider.initialize();

        await tester.pumpWidget(createTestWidget(
          screenSize: const Size(1024, 768), // Landscape tablet
        ));
        await tester.pumpAndSettle();

        // Should find the toggle button inside the menu header
        expect(find.byIcon(Icons.menu_open), findsOneWidget);
        expect(find.byTooltip('Ascunde meniul lateral'), findsOneWidget);
        expect(find.text('Animalia'), findsOneWidget); // Menu should be visible
      });

      testWidgets('should show floating toggle button when menu is hidden', (WidgetTester tester) async {
        await sideMenuProvider.initialize();
        await sideMenuProvider.hide(); // Start with hidden menu

        await tester.pumpWidget(createTestWidget(
          screenSize: const Size(1024, 768), // Landscape tablet
        ));
        await tester.pumpAndSettle();

        // Should find the floating toggle button
        expect(find.byIcon(Icons.menu), findsOneWidget);
        expect(find.byTooltip('Afișează meniul lateral'), findsOneWidget);
        expect(find.text('Animalia'), findsNothing); // Menu should be hidden
      });

      testWidgets('should not show toggle button in small landscape mode', (WidgetTester tester) async {
        await sideMenuProvider.initialize();
        
        await tester.pumpWidget(createTestWidget(
          screenSize: const Size(667, 375), // Small landscape (iPhone)
        ));
        await tester.pumpAndSettle();

        // Should not find the toggle button (screen width < 768)
        expect(find.byIcon(Icons.menu_open), findsNothing);
        expect(find.byIcon(Icons.menu), findsNothing);
      });

      testWidgets('should show toggle button in landscape desktop mode', (WidgetTester tester) async {
        await sideMenuProvider.initialize();
        
        await tester.pumpWidget(createTestWidget(
          screenSize: const Size(1440, 900), // Desktop landscape
        ));
        await tester.pumpAndSettle();

        // Should find the toggle button
        expect(find.byIcon(Icons.menu_open), findsOneWidget);
      });
    });

    group('Toggle Button Functionality', () {
      testWidgets('should toggle icon when menu visibility changes', (WidgetTester tester) async {
        await sideMenuProvider.initialize();
        
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Initially should show menu_open icon (menu is visible)
        expect(find.byIcon(Icons.menu_open), findsOneWidget);
        expect(find.byIcon(Icons.menu), findsNothing);

        // Tap the toggle button
        await tester.tap(find.byIcon(Icons.menu_open));
        await tester.pumpAndSettle();

        // Should now show menu icon (menu is hidden)
        expect(find.byIcon(Icons.menu), findsOneWidget);
        expect(find.byIcon(Icons.menu_open), findsNothing);
      });

      testWidgets('should update tooltip when menu visibility changes', (WidgetTester tester) async {
        await sideMenuProvider.initialize();
        
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Initially should show "hide" tooltip
        expect(find.byTooltip('Ascunde meniul lateral'), findsOneWidget);

        // Tap the toggle button
        await tester.tap(find.byIcon(Icons.menu_open));
        await tester.pumpAndSettle();

        // Should now show "show" tooltip
        expect(find.byTooltip('Afișează meniul lateral'), findsOneWidget);
        expect(find.byTooltip('Ascunde meniul lateral'), findsNothing);
      });

      testWidgets('should animate icon changes', (WidgetTester tester) async {
        await sideMenuProvider.initialize();
        
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Find the AnimatedSwitcher
        expect(find.byType(AnimatedSwitcher), findsOneWidget);

        // Tap the toggle button
        await tester.tap(find.byIcon(Icons.menu_open));
        await tester.pump(); // Start animation

        // AnimatedSwitcher should still be present during animation
        expect(find.byType(AnimatedSwitcher), findsOneWidget);

        await tester.pumpAndSettle();

        // Animation should complete with new icon
        expect(find.byIcon(Icons.menu), findsOneWidget);
      });

      testWidgets('should call provider toggle method on tap', (WidgetTester tester) async {
        await sideMenuProvider.initialize();
        bool toggleCalled = false;
        
        // Override the toggle method to track calls
        final originalToggle = sideMenuProvider.toggleVisibility;
        sideMenuProvider.toggleVisibility = () async {
          toggleCalled = true;
          await originalToggle();
        };
        
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Tap the toggle button
        await tester.tap(find.byIcon(Icons.menu_open));
        await tester.pumpAndSettle();

        expect(toggleCalled, true);
      });
    });

    group('Responsive Behavior', () {
      testWidgets('should hide toggle button when switching to portrait', (WidgetTester tester) async {
        await sideMenuProvider.initialize();
        
        // Start in landscape
        await tester.pumpWidget(createTestWidget(
          screenSize: const Size(1024, 768),
        ));
        await tester.pumpAndSettle();

        expect(find.byIcon(Icons.menu_open), findsOneWidget);

        // Switch to portrait
        await tester.pumpWidget(createTestWidget(
          screenSize: const Size(375, 812),
        ));
        await tester.pumpAndSettle();

        expect(find.byIcon(Icons.menu_open), findsNothing);
        expect(find.byIcon(Icons.menu), findsNothing);
      });

      testWidgets('should show toggle button when switching to landscape', (WidgetTester tester) async {
        await sideMenuProvider.initialize();
        
        // Start in portrait
        await tester.pumpWidget(createTestWidget(
          screenSize: const Size(375, 812),
        ));
        await tester.pumpAndSettle();

        expect(find.byIcon(Icons.menu_open), findsNothing);

        // Switch to landscape
        await tester.pumpWidget(createTestWidget(
          screenSize: const Size(1024, 768),
        ));
        await tester.pumpAndSettle();

        expect(find.byIcon(Icons.menu_open), findsOneWidget);
      });

      testWidgets('should maintain menu state across orientation changes', (WidgetTester tester) async {
        await sideMenuProvider.initialize();
        
        // Start in landscape and hide menu
        await tester.pumpWidget(createTestWidget(
          screenSize: const Size(1024, 768),
        ));
        await tester.pumpAndSettle();

        await tester.tap(find.byIcon(Icons.menu_open));
        await tester.pumpAndSettle();

        expect(find.byIcon(Icons.menu), findsOneWidget);

        // Switch to portrait (button should disappear)
        await tester.pumpWidget(createTestWidget(
          screenSize: const Size(375, 812),
        ));
        await tester.pumpAndSettle();

        // Switch back to landscape (menu should still be hidden)
        await tester.pumpWidget(createTestWidget(
          screenSize: const Size(1024, 768),
        ));
        await tester.pumpAndSettle();

        expect(find.byIcon(Icons.menu), findsOneWidget);
        expect(find.byIcon(Icons.menu_open), findsNothing);
      });
    });
  });
}
