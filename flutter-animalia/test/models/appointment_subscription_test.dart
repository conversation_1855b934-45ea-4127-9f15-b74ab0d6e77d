import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_animalia/models/appointment.dart';

void main() {
  group('Appointment Subscription Features', () {
    test('should correctly identify appointment with subscription', () {
      // Arrange
      final appointment = Appointment(
        id: '1',
        clientId: 'client1',
        clientName: '<PERSON>',
        clientPhone: '123456789',
        petId: 'pet1',
        petName: 'Buddy',
        petSpecies: 'Dog',
        service: 'Grooming',
        startTime: DateTime.now(),
        endTime: DateTime.now().add(const Duration(hours: 1)),
        status: 'SCHEDULED',
        isPaid: false,
        subscriptionId: 'sub123',
        sequenceNumber: 2,
        isRecurring: true,
      );

      // Act & Assert
      expect(appointment.hasSubscription, isTrue);
      expect(appointment.subscriptionDisplayInfo, equals('Abonament #2'));
    });

    test('should correctly identify appointment without subscription', () {
      // Arrange
      final appointment = Appointment(
        id: '1',
        clientId: 'client1',
        clientName: '<PERSON>',
        clientPhone: '123456789',
        petId: 'pet1',
        petName: 'Buddy',
        petSpecies: 'Dog',
        service: 'Grooming',
        startTime: DateTime.now(),
        endTime: DateTime.now().add(const Duration(hours: 1)),
        status: 'SCHEDULED',
        isPaid: false,
      );

      // Act & Assert
      expect(appointment.hasSubscription, isFalse);
      expect(appointment.subscriptionDisplayInfo, equals(''));
    });

    test('should handle subscription without sequence number', () {
      // Arrange
      final appointment = Appointment(
        id: '1',
        clientId: 'client1',
        clientName: 'John Doe',
        clientPhone: '123456789',
        petId: 'pet1',
        petName: 'Buddy',
        petSpecies: 'Dog',
        service: 'Grooming',
        startTime: DateTime.now(),
        endTime: DateTime.now().add(const Duration(hours: 1)),
        status: 'SCHEDULED',
        isPaid: false,
        subscriptionId: 'sub123',
        isRecurring: true,
      );

      // Act & Assert
      expect(appointment.hasSubscription, isTrue);
      expect(appointment.subscriptionDisplayInfo, equals('Abonament'));
    });

    test('should serialize and deserialize subscription fields correctly', () {
      // Arrange
      final originalAppointment = Appointment(
        id: '1',
        clientId: 'client1',
        clientName: 'John Doe',
        clientPhone: '123456789',
        petId: 'pet1',
        petName: 'Buddy',
        petSpecies: 'Dog',
        service: 'Grooming',
        startTime: DateTime.now(),
        endTime: DateTime.now().add(const Duration(hours: 1)),
        status: 'SCHEDULED',
        isPaid: false,
        subscriptionId: 'sub123',
        sequenceNumber: 3,
        isRecurring: true,
      );

      // Act
      final json = originalAppointment.toJson();
      final deserializedAppointment = Appointment.fromJson(json);

      // Assert
      expect(deserializedAppointment.subscriptionId, equals('sub123'));
      expect(deserializedAppointment.sequenceNumber, equals(3));
      expect(deserializedAppointment.isRecurring, isTrue);
      expect(deserializedAppointment.hasSubscription, isTrue);
    });

    test('should handle null subscription fields in JSON', () {
      // Arrange
      final json = {
        'id': '1',
        'clientId': 'client1',
        'clientName': 'John Doe',
        'clientPhone': '123456789',
        'petId': 'pet1',
        'petName': 'Buddy',
        'petSpecies': 'Dog',
        'service': 'Grooming',
        'startTime': DateTime.now().toIso8601String(),
        'endTime': DateTime.now().add(const Duration(hours: 1)).toIso8601String(),
        'status': 'SCHEDULED',
        'isPaid': false,
        // subscription fields are null/missing
      };

      // Act
      final appointment = Appointment.fromJson(json);

      // Assert
      expect(appointment.subscriptionId, isNull);
      expect(appointment.sequenceNumber, isNull);
      expect(appointment.isRecurring, isFalse);
      expect(appointment.hasSubscription, isFalse);
    });

    test('should copy appointment with subscription fields', () {
      // Arrange
      final originalAppointment = Appointment(
        id: '1',
        clientId: 'client1',
        clientName: 'John Doe',
        clientPhone: '123456789',
        petId: 'pet1',
        petName: 'Buddy',
        petSpecies: 'Dog',
        service: 'Grooming',
        startTime: DateTime.now(),
        endTime: DateTime.now().add(const Duration(hours: 1)),
        status: 'SCHEDULED',
        isPaid: false,
      );

      // Act
      final updatedAppointment = originalAppointment.copyWith(
        subscriptionId: 'sub456',
        sequenceNumber: 5,
        isRecurring: true,
      );

      // Assert
      expect(updatedAppointment.subscriptionId, equals('sub456'));
      expect(updatedAppointment.sequenceNumber, equals(5));
      expect(updatedAppointment.isRecurring, isTrue);
      expect(updatedAppointment.hasSubscription, isTrue);
      
      // Original should remain unchanged
      expect(originalAppointment.hasSubscription, isFalse);
    });
  });
}
