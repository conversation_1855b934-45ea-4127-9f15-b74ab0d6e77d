# frozen_string_literal: true

# Released under the MIT License.
# Copyright, 2006-2012, by <PERSON>.
# Copyright, 2008, by <PERSON><PERSON><PERSON><PERSON>.
# Copyright, 2019, by <PERSON><PERSON>.
# Copyright, 2019, by <PERSON>.
# Copyright, 2021-2024, by <PERSON>.

require 'net/http'

require_relative '../../../multipart/post'

module Net
  class HTTP
    class Put
      class Multipart < Put
        include ::Multipart::Post::Multipartable
      end
    end

    class Post
      class Multipart < Post
        include ::Multipart::Post::Multipartable
      end
    end
  end
end
