require 'jrjackson' unless defined?(::<PERSON><PERSON><PERSON><PERSON>)
require 'multi_json/adapter'

module MultiJson
  module Adapters
    # Use the jrjackson.rb library to dump/load.
    class JrJackson < Adapter
      ParseError = ::Jr<PERSON><PERSON><PERSON>::ParseError

      def load(string, options = {}) #:nodoc:
        ::Jr<PERSON><PERSON><PERSON>::Json.load(string, options)
      end

      if ::Jr<PERSON><PERSON>son::Json.method(:dump).arity == 1
        def dump(object, _)
          ::JrJackson::Json.dump(object)
        end
      else
        def dump(object, options = {})
          ::JrJ<PERSON>son::Json.dump(object, options)
        end
      end
    end
  end
end
