require 'fastlane_core/ui/ui'

module Fastlane
  UI = FastlaneCore::UI unless Fastlane.const_defined?(:UI)

  module Helper
    class <%= plugin_name.fastlane_class %>Helper
      # class methods that you define here become available in your action
      # as `Helper::<%= plugin_name.fastlane_class %>Helper.your_method`
      #
      def self.show_message
        UI.message("Hello from the <%= plugin_name %> plugin helper!")
      end
    end
  end
end
