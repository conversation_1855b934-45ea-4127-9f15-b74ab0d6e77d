# That looks wrong
Layout/AlignHash:
  Enabled: false

Layout/DotPosition:
  Enabled: false

# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
Layout/IndentHash:
  Enabled: false

Lint/HandleExceptions:
  Enabled: false

# the let(:key) { ... } should be allowed in tests
Lint/ParenthesesAsGroupedExpression:
  Exclude:
    - 'spec/**/*'

# Cop supports --auto-correct.
Lint/UnusedBlockArgument:
  Enabled: false

# Needed for $verbose
Style/GlobalVars:
  Enabled: false

Lint/UnusedMethodArgument:
  Enabled: false

# https://stackoverflow.com/a/13059657/865175
Lint/UriEscapeUnescape:
  Enabled: false

Lint/UselessAssignment:
  Exclude:
    - 'spec/**/*'

# Offense count: 20
Metrics/AbcSize:
  Max: 60

Metrics/BlockLength:
  Enabled: false

# Configuration parameters: CountComments.
Metrics/ClassLength:
  Max: 320

Metrics/CyclomaticComplexity:
  Max: 17

# Configuration parameters: AllowURI, URISchemes.
Metrics/LineLength:
  Max: 370

# options.rb might be large, we know that
Metrics/MethodLength:
  Max: 60
  Exclude:
    - 'lib/*/options.rb'

# Configuration parameters: CountKeywordArgs.
Metrics/ParameterLists:
  Max: 17

Metrics/PerceivedComplexity:
  Max: 18

Naming/FileName:
  Enabled: false

# and and or is okay
Style/AndOr:
  Enabled: false

# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
Style/BracesAroundHashParameters:
  Enabled: false

Style/ClassCheck:
  EnforcedStyle: kind_of?

Style/ClassVars:
  Enabled: false

Style/Documentation:
  Enabled: false

Style/DoubleNegation:
  Enabled: false

# Needed for $verbose
Style/GlobalVars:
  Enabled: false

Style/GuardClause:
  Enabled: false

# Having if in the same line might not always be good
Style/IfUnlessModifier:
  Enabled: false

Style/RaiseArgs:
  EnforcedStyle: exploded

# Better too much 'return' than one missing
Style/RedundantReturn:
  Enabled: false

# Not a good thing
Style/RedundantSelf:
  Enabled: false

# raise and fail are both okay
Style/SignalException:
  Enabled: false

# $? Exit
Style/SpecialGlobalVars:
  Enabled: false

# Both string notations are okay
Style/StringLiterals:
  Enabled: false

# The %w might be confusing for new users
Style/WordArray:
  MinSize: 19

Style/ZeroLengthPredicate:
  Enabled: false
