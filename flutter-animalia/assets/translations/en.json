{"add_client": {"title": "Add New Client", "save": "Save", "saving": "Saving...", "client_info": "Client Information", "name_label": "Full Name *", "name_hint": "Ex: <PERSON>", "name_required": "Name is required", "name_min_length": "Name must be at least 2 characters", "phone_label": "Phone Number *", "phone_hint": "+40 XXX XXX XXX", "phone_helper": "Format: +40 XXX XXX XXX", "phone_required": "Phone is required", "phone_invalid": "Phone number is not valid (ex: +40728626399)", "email_label": "Email (optional)", "email_hint": "ex: <EMAIL>", "address_label": "Address (optional)", "address_hint": "Select client address on map", "notes_label": "Notes (optional)", "notes_hint": "Additional information about client", "save_button": "Save Client", "info_text": "After saving the client, you will be able to add their pets.", "error_save": "Error saving client", "error_connection": "Connection error: {error}", "success_title": "Client created successfully!", "success_message": "Would you like to add a pet for this client now?", "later": "Later", "add_pet": "Add Pet"}, "add_staff_dialog": {"title": "Add Team Member", "cancel_button": "Cancel", "add_button": "Add", "create_directly_title": "Create directly", "create_directly_enabled_subtitle": "Team member will be created directly without invitation", "create_directly_disabled_subtitle": "An invitation will be sent to the phone number", "phone_section_title": "Phone number", "phone_placeholder": "+40 XXX XXX XXX", "phone_helper_text": "Accepts: +40 XXX XXX XXX, 0XXX XXX XXX, XXXXXXXXX", "phone_invitation_info": "The person with this phone number will receive an invitation to join the team.", "nickname_required_title": "Display name (required)", "nickname_optional_title": "Display name (optional)", "nickname_placeholder": "e.g: <PERSON>, <PERSON>, <PERSON><PERSON>", "nickname_helper_text": "The name that will appear in appointments and calendar", "nickname_required_error": "Display name is required for direct creation", "nickname_min_length_error": "Display name must be at least 2 characters", "nickname_max_length_error": "Display name cannot exceed 50 characters", "nickname_direct_info": "The name that will appear in appointments and calendar", "nickname_invitation_info": "If not filled, the full name from the user account will be used.", "role_section_title": "Team role", "permissions_section_title": "Client data access permissions", "permissions_chief_info": "Chief <PERSON><PERSON><PERSON> automatically has full access to client data.", "notes_section_title": "Notes (optional)", "notes_placeholder": "Notes about this team member...", "success_member_created_title": "Member created", "success_invitation_sent_title": "Invitation sent", "success_member_created_message": "Team member {name} has been created successfully", "success_invitation_sent_message": "Invitation has been sent successfully to {phone}", "error_title": "Error", "error_generic_message": "Could not send invitation", "error_unexpected_title": "Unexpected error", "error_unexpected_message": "A problem occurred while adding the team member"}, "address_action": {"open_address_in": "Open address in:", "google_maps": "Google Maps", "navigate_with_google_maps": "Navigate with Google Maps", "waze": "Waze", "navigate_with_waze": "Navigate with Waze", "apple_maps": "Apple Maps", "navigate_with_apple_maps": "Navigate with Apple Maps"}, "app": {"name": "Animalia Appointments", "tagline": "Grooming salon management"}, "appointments": {"appointments": "Appointments", "no_appointments": "No appointments", "upcoming_appointments": "Upcoming Appointments", "appointment_history": "Appointment History", "recurring_appointments": "Recurring Appointments", "appointment_details": "Appointment Details", "unknown_service": "Unknown service", "unknown_status": "Unknown"}, "appointments_report": {"title": "Appointments Report", "export_excel_tooltip": "Export Excel", "use_real_data_tooltip": "Use real data", "use_sample_data_tooltip": "Use sample data", "empty_state_title": "No appointments in selected period", "empty_state_subtitle": "Select another period or use sample data", "summary_title": "Appointments Summary", "summary_total": "Total", "summary_completed": "Completed", "summary_cancelled": "Cancelled", "summary_revenue": "Revenue", "details_title": "Appointment Details ({count})", "table_date_time": "Date/Time", "table_client": "Client", "table_pet": "Pet", "table_service": "Service", "table_staff": "Staff", "table_status": "Status", "table_price": "Price", "table_duration": "Duration", "status_completed": "Completed", "status_cancelled": "Cancelled", "status_confirmed": "Confirmed", "status_scheduled": "Scheduled", "status_in_progress": "In Progress", "status_requested": "Requested", "status_no_show": "No Show", "generating_report": "Generating report...", "report_downloaded_success": "Report downloaded successfully!", "error_loading_appointments": "Error loading appointments: {error}", "error_generating_report": "Error: {error}", "excel_sheet_title": "Appointments Report", "excel_period_label": "Period: {start} - {end}", "excel_generated_label": "Generated at: {date}", "excel_total_label": "Total appointments: {count}", "excel_headers": {"date": "Date", "start_time": "Start Time", "end_time": "End Time", "client": "Client", "phone": "Phone", "pet": "Pet", "species": "Species", "service": "Service", "staff": "Staff", "status": "Status", "price_ron": "Price (RON)", "duration_min": "Duration (min)", "notes": "Notes"}}, "business_reports": {"title": "Business Reports", "info_tooltip": "Information", "sample_data_banner_title": "Sample Data Available", "sample_data_banner_message": "Currently using test data for preview. Real reports will be available when sufficient data exists.", "available_analyses": "Available Analyses", "appointments_report_title": "Appointments Report", "appointments_report_subtitle": "Excel export with all appointments", "pets_by_size_breed_title": "Pets by <PERSON><PERSON>/Breed", "pets_by_size_breed_subtitle": "Distribution of pets by size and breed", "services_requested_title": "Services Requested", "services_requested_subtitle": "Service popularity and revenue", "top_clients_title": "Top Clients", "top_clients_subtitle": "Clients with highest spending", "staff_performance_title": "Staff Performance", "staff_performance_subtitle": "Team member performance analysis", "revenue_title": "Revenue", "revenue_subtitle": "Revenue evolution over time", "quick_actions": "Quick Actions", "sample_data_preview_title": "Sample Data Preview", "sample_data_preview_subtitle": "See how reports look with test data", "sample_data_dialog_title": "Sample Data", "sample_data_dialog_content": "All reports currently use generated data for demonstration. This includes:\n\n• Fictional pets with varied breeds and sizes\n• Services with simulated popularity\n• Clients with generated spending\n• Simulated staff performance\n• Revenue with realistic trends\n\nReal data will replace this when sufficient information is available.", "sample_data_dialog_understood": "Understood", "info_dialog_title": "About Reports", "info_dialog_content": "The reports section provides detailed analysis of salon activity:\n\n• Interactive charts for data visualization\n• Excel export for each report\n• Flexible time interval selection\n• Sample data for preview\n\nAll reports can be exported and shared.", "info_dialog_close": "Close"}, "calendar": {"title": "Calendar", "list_view": "List view", "calendar_view": "Calendar view", "calendar_settings": "Calendar settings", "calendar_settings_close": "Close", "calendar_settings_subtitle": "Configure calendar display options", "calendar_tour_title": "Calendar", "calendar_tour_description": "View all appointments for your team.", "settings_tour_title": "Calendar Settings", "settings_tour_description": "Filter staff, change view mode or view cancelled appointments.", "create_appointment_tour_title": "Create Appointment", "create_appointment_tour_description": "<PERSON>p on a free time slot to create a new appointment.", "quick_actions_tour_title": "Quick Actions", "quick_actions_tour_description": "Add new appointments or block time intervals.", "actions_tooltip": "Actions", "block_time": "Block time", "add_appointment": "Add appointment", "new_appointment": "New appointment", "add_new_appointment": "Add new appointment", "appointments_list": "Appointments - List", "export_excel": "Export Excel", "print": "Print", "share": "Share", "print_feature_coming": "Print functionality will be implemented", "share_feature_coming": "Share functionality will be implemented", "update_complete": "Update complete", "update_complete_message": "All data has been updated: team, schedule, appointments and blocks", "update_error": "Update error", "update_error_message": "Update error: {error}", "appointment_added": "Appointment added", "appointment_added_message": "The appointment was created successfully and is visible in the calendar", "appointments_conflict_message": "There are appointments in the selected interval. Blocking will continue, but appointments will need to be rescheduled:", "unknown_staff": "Unknown", "unknown_client": "Unknown client", "unknown_service": "Unknown service", "client_label": "Client", "service_label": "Service", "block_will_continue": "Blocking will continue automatically. You will be able to reschedule appointments from the calendar.", "cancel": "Cancel", "continue_blocking": "Continue blocking", "error_deleting_block": "Error deleting block. Block has been restored.", "block_time_title": "Block time", "close": "Close", "time_tab": "Time", "team_tab": "Team", "details_tab": "Details", "quick_actions": "Quick actions", "quick_action_30_min": "30 min", "quick_action_1_hour": "1 hour", "quick_action_2_hours": "2 hours", "quick_action_first_half": "First half", "quick_action_second_half": "Second half", "quick_action_full_day": "Full day", "multi_day_blocking": "Multi-day blocking", "period": "Period", "date": "Date", "from": "From", "until": "Until", "select": "Select", "duration_label": "Duration:", "interval_label": "Interval:", "team_label": "Team:", "cancel_button": "Cancel", "block_time_button": "Block time", "conflict_info_title": "Conflict information", "continue_button": "Continue", "blocking_will_continue": "Blocking will continue automatically. You will be able to reschedule appointments from the calendar.", "start_time": "Start time", "end_time": "End time", "duration": "Duration", "select_team_members": "Select team members", "time_will_be_blocked": "Time will be blocked for selected members", "no_team_members_available": "No team members available", "select_all": "Select all", "deselect": "Deselect", "blocking_reason": "Blocking reason", "select_blocking_reason": "Select the reason for blocking time", "custom_reason": "Custom reason", "enter_blocking_reason": "Enter the blocking reason...", "block_summary": "Block summary", "no_member_selected": "No member selected", "member": "member", "members": "members", "reason": "Reason", "reason_break": "Break", "reason_meeting": "Meeting", "reason_vacation": "Vacation", "reason_personal": "Personal", "reason_other": "Other", "conflict_information": "Conflict information", "continue": "Continue", "select_at_least_one_member": "Select at least one team member", "select_end_date": "Select end date for multi-day blocking", "end_date_after_start": "End date must be after start date", "end_time_after_start": "End time must be after start time", "duration_max_12_hours": "Duration cannot be more than 12 hours", "enter_custom_reason": "Enter a custom reason", "max_30_days": "Multi-day blocking cannot exceed 30 days", "view_mode_day": "Day", "view_mode_week": "Week", "view_mode_month": "Month", "view_mode_day_short": "Day", "view_mode_week_short": "Week", "view_mode_month_short": "Month", "blocked": "Blocked", "no_appointments": "No appointments", "no_appointments_today": "No appointments for today", "refresh": "Refresh", "filter_all": "All", "filter_today": "Today", "filter_upcoming": "Upcoming", "filter_this_week": "This week", "filter_this_month": "This month", "filter_completed": "Completed", "filter_pending": "Pending", "filter_cancelled": "Cancelled", "search_appointments": "Search appointments", "search_placeholder": "Search by client name, pet...", "clear_search": "Clear search", "no_results": "No results found", "no_results_message": "No appointments found matching your criteria", "time_slot": "Time slot", "time_slots": "Time slots", "available": "Available", "occupied": "Occupied", "free_time": "Free time", "busy_time": "Busy time", "working_hours": "Working hours", "minutes_short": "min", "hour_singular": "hour", "hours_plural": "hours", "break_time": "Break time", "lunch_break": "Lunch break", "before_hours": "Before hours", "after_hours": "After hours", "weekend": "Weekend", "weekday": "Weekday", "morning": "Morning", "afternoon": "Afternoon", "evening": "Evening", "night": "Night", "all_day": "All day", "recurring": "Recurring", "repeat": "Repeat", "repeat_daily": "Daily", "repeat_weekly": "Weekly", "repeat_monthly": "Monthly", "repeat_never": "Never", "navigation": "Navigation", "today": "Today", "tomorrow": "Tomorrow", "yesterday": "Yesterday", "this_week": "This week", "next_week": "Next week", "last_week": "Last week", "this_month": "This month", "next_month": "Next month", "last_month": "Last month", "go_to_date": "Go to date", "select_date": "Select date", "calendar_navigation": "Calendar navigation", "zoom_in": "Zoom in", "zoom_out": "Zoom out", "fit_to_screen": "Fit to screen", "full_screen": "Full screen", "exit_full_screen": "Exit full screen", "stats_today": "Today", "stats_upcoming": "Upcoming", "stats_completed": "Completed", "stats_revenue": "Revenue", "empty_today": "No appointments today", "empty_today_subtitle": "The day is free!", "empty_upcoming": "No upcoming appointments", "empty_upcoming_subtitle": "All appointments are in the past", "empty_completed": "No completed appointments", "empty_completed_subtitle": "No appointments have been completed yet", "empty_pending": "No pending appointments", "empty_pending_subtitle": "All appointments are confirmed", "empty_cancelled": "No cancelled appointments", "empty_cancelled_subtitle": "No appointments have been cancelled", "empty_default": "No appointments", "empty_search_subtitle": "Try modifying your search", "empty_filter_subtitle": "Select a different filter", "search_hint": "Search by client, pet, service", "hour_display": "Hour display", "business_hours": "Business hours", "full_day": "24 hours", "business_hours_desc": "Show only salon business hours", "full_day_desc": "Show all 24 hours of the day", "time_format": "Time format", "time_format_12h": "AM/PM", "time_format_24h": "24 hours", "time_format_12h_desc": "Display hours in AM/PM format.", "time_format_24h_desc": "Display hours in 24-hour format.", "google_calendar_sync": "Sync appointments with Google Calendar", "staff_filter": "Staff filter", "deselect_all": "Deselect all", "no_staff_available": "No staff available", "add_new_member": "Add new member", "display_options": "Display options", "show_cancelled": "Show cancelled appointments", "hide_revenue_summary": "Hide appointment summary", "show_revenue_summary": "Show appointment summary", "upgrade_for_team": "Upgrade for Team", "upgrade_freelancer_msg": "Free plan allows only 1 team member. Upgrade to Freelancer for up to 2 members and advanced features.", "extend_team": "Extend Your Team", "upgrade_team_msg": "Freelancer plan allows only 2 team members. Upgrade to Enterprise for unlimited members and all premium features.", "unlimited_team": "Unlimited Team", "upgrade_enterprise_msg": "Team plan allows maximum 10 team members. Upgrade to Enterprise for unlimited members and all premium features.", "limit_reached": "Limit Reached", "limit_reached_msg": "Cannot add more team members with current plan.", "zoom": "Zoom", "reset": "Reset", "unavailable_slot_opacity": "Unavailable slot color intensity", "slot_height": "Slot height", "unavailable_slot_opacity_desc": "Control transparency of unavailable time slots", "transparent": "Transparent", "intense": "Intense", "unavailable_slot_color": "Unavailable slot color", "unavailable_slot_color_desc": "Choose color for unavailable time slots", "new_slot": "New slot", "new_slot_button": "New Slot", "view_toggle": "View", "view_toggle_description": "Switch between day, week or month view.", "navigation_description": "Tap on date to go to today.", "new_slot_description": "Create a new slot for multiple simultaneous appointments.", "slot_added": "Slot added!", "slot_added_message": "The slot {staffName} - Slot {slotNumber} has been added successfully.", "slot_could_not_be_added": "Slot could not be added!", "slot_limit_reached": "Slot Limit Reached", "slot_limit_free_plan": "Free plan allows only limited slots. Upgrade to Freelancer for more time slots.", "slot_limit_freelancer_plan": "Freelancer plan has limited slots. Upgrade to Team for extended capacity.", "slot_limit_team_plan": "Team plan has limited slots. Upgrade to Enterprise for unlimited slots.", "slot_limit_enterprise_plan": "Cannot add more slots with current plan.", "slot_limit_no_subscription": "Cannot add more slots with current plan. Upgrade for more features.", "no_groomer_in_team": "No groomer in team", "no_groomer_selected": "No groomer selected", "no_groomers_in_team_description": "There are no groomers in the team. Add team members to see appointments and working hours.", "no_groomers_selected_description": "To see appointments and working hours, check at least one staff member from the settings menu.", "check_staff": "Check staff", "check_all_staff": "Check all staff ({count})", "manage_team": "Manage team", "block_details": "Block details", "average": "Average", "months": {"january_short": "Jan", "february_short": "Feb", "march_short": "Mar", "april_short": "Apr", "may_short": "May", "june_short": "Jun", "july_short": "Jul", "august_short": "Aug", "september_short": "Sep", "october_short": "Oct", "november_short": "Nov", "december_short": "Dec"}, "full_months": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}, "colors": {"gray": "<PERSON>", "red": "Red", "blue": "Blue", "purple": "Purple", "orange": "Orange", "brown": "<PERSON>"}, "weekdays": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "monday_short": "Mon", "tuesday_short": "<PERSON><PERSON>", "wednesday_short": "Wed", "thursday_short": "<PERSON>hu", "friday_short": "<PERSON><PERSON>", "saturday_short": "Sat", "sunday_short": "Sun", "monday_abbr": "M", "tuesday_abbr": "T", "wednesday_abbr": "W", "thursday_abbr": "T", "friday_abbr": "F", "saturday_abbr": "S", "sunday_abbr": "S"}, "appointments": {"unknown_pet": "Unknown pet", "pet": "Pet", "service": "Service", "general_service": "General service", "blocked": "Blocked", "new_appointment_action": "New appointment", "block_time_action": "Block time", "cancelled": "Cancelled"}}, "chart_widget": {"unsupported_chart_type": "Unsupported chart type", "no_data_to_display": "No data to display", "no_valid_values": "Data contains no valid values", "insufficient_data_variation": "Data has insufficient variation for chart"}, "client_search": {"title": "Select Client", "search_label": "Search client", "search_placeholder": "Name or phone...", "add_new_client": "Add new client", "clients_found": "Clients found ({count})", "no_clients_available": "No clients available", "no_results_for_query": "No clients found for \"{query}\"", "pet_match": "Pet match: {pets}"}, "client_settings": {"title": "Client Settings", "inactive_clients_title": "Inactive Clients", "inactive_clients_subtitle": "Configure when a client is considered inactive", "threshold_period_title": "Inactivity period", "threshold_description": "Clients are considered inactive if they haven't had appointments in the last {period}", "custom_days_label": "Custom days", "custom_days_hint": "Enter number of days", "custom_days_suffix": "days", "custom_days_error": "Please enter a valid number between 1 and 365 days", "notifications_title": "Notifications", "notifications_subtitle": "Configure notifications for inactive clients", "notifications_coming_soon": "Notifications for inactive clients will be available soon", "advanced_title": "Advanced Settings", "advanced_coming_soon": "Advanced priority settings will be available soon", "reset_title": "Reset", "reset_button": "Reset to default settings", "reset_dialog_title": "Reset settings", "reset_dialog_message": "Are you sure you want to reset all settings to default values?", "reset_success": "Settings have been reset", "critical_threshold_title": "Critical inactivity threshold", "critical_threshold_description": "Clients who haven't had appointments in this period will be marked with critical priority.", "critical_threshold_label": "Days", "cancel": "Cancel", "reset": "Reset", "period_1_day": "1 day", "period_days": "{days} days", "period_1_week": "1 week", "period_weeks": "{weeks} weeks", "period_1_month": "1 month", "period_months": "{months} months", "period_years": "{years} years"}, "clients": {"title": "Clients", "all_clients": "All Clients", "client_added_success": "New client added successfully", "view_details": "View Details", "inactive_clients_title": "{tabTitle} Clients", "view_active_clients": "Active", "view_inactive_clients": "Inactive", "search_clients": "Search clients...", "search_inactive_clients": "Search inactive clients...", "settings_tooltip": "Client Settings", "client_not_found": "Client not found", "navigation_error": "Navigation error", "no_clients_found": "No clients found", "no_clients_registered": "No clients registered yet", "pull_to_refresh_or_add": "Pull to refresh or add a new client", "clients_found_count": "{count} clients found", "client_updated_success": "Client {name} updated successfully", "confirm_delete_title": "Confirm Deletion", "confirm_delete_message": "Are you sure you want to delete client {name}?", "delete_will_remove": "This action will permanently remove:", "all_client_data": "All client information", "all_associated_pets": "All associated pets", "appointment_history": "Appointment history", "reviews_and_subscriptions": "Reviews and subscriptions", "action_cannot_be_undone": "This action cannot be undone!", "deleting_client": "Deleting client...", "client_deleted_success": "Client {name} deleted successfully", "delete_error_generic": "Failed to delete client", "client_actions": "Client Actions", "delete_client": "Delete Client", "add_pet": "Add Pet", "edit_client": "Edit Client", "client_details": "Client Details", "new_client_badge": "NEW", "new_client": "New client", "client_since": "Client since {date}", "no_visits": "No visits", "last_visit": "Last visit: {date}", "total_revenue": "Revenue", "total_appointments": "Appointments", "no_show_appointments": "No-Show", "cancelled_appointments": "Cancelled", "add_manual": "Add manually", "import_contacts": "Import contacts", "no_inactive_clients_title": "Excellent!", "no_inactive_clients_message": "No inactive clients in the last {days} days.", "refresh_inactive_clients": "Refresh", "loading_inactive_clients": "Loading inactive clients...", "error_loading_inactive_clients": "Error loading inactive clients", "pet_singular": "pet", "pet_plural": "pets"}, "clients_report": {"title": "Top Clients", "export_excel_tooltip": "Export Excel", "demo_button": "DEMO", "real_button": "REAL", "sample_data_indicator": "Showing sample data for demonstration", "total_clients": "Total Clients", "average_spending": "Average Spending", "new_clients": "New Clients", "top_clients_chart_title": "Top 8 Clients by Spending", "client_axis_label": "Client", "spending_axis_label": "Spending (RON)", "top_clients_section_title": "Top Clients", "export_chart_tooltip": "Export chart", "client_analysis_title": "Client Analysis", "returning_clients": "Returning Clients", "retention_excellent": "Excellent retention rate! Clients return frequently.", "retention_improvement": "Retention rate can be improved. Consider loyalty programs.", "detailed_clients_title": "Top Clients Details", "spending_label": "Spending: {amount} RON", "empty_state_title": "No data available", "empty_state_subtitle": "Select a different interval or enable sample data", "generating_report": "Generating report...", "excel_sheet_title": "Clients Report", "excel_total_clients": "Total clients: {count}", "excel_top_clients_header": "Top Clients", "excel_spending_header": "Spending (RON)", "excel_filename": "clients_report.xlsx", "excel_share_text": "Clients report generated from Animalia app", "report_downloaded_success": "Report downloaded successfully!", "error_loading_data": "Error loading data: {error}", "export_chart_coming_soon": "Export {type} chart - coming soon!"}, "common": {"ok": "OK", "cancel": "Cancel", "save": "Save", "saving": "Saving...", "delete": "Delete", "next": "Next", "previous": "Previous", "is_required": "is required", "edit": "Edit", "close": "Close", "back": "Back", "yes": "Yes", "no": "No", "loading": "Loading...", "error": "Error", "success": "Success", "maybe_later": "Maybe later", "help": "Help", "profil": "Settings", "options": "Options", "add": "Add", "today": "Today", "yesterday": "Yesterday", "days_ago": "{count} days ago", "weeks_ago": "{count} weeks ago", "no_data": "No data available", "try_again": "Try again", "used": "used"}, "create_salon": {"title": "Create Salon", "multiple_salons_title": "Multiple Salon Creation", "multiple_salons_description": "To create multiple salons, you need the Gold plan which offers multi-location functionality.", "upgrade_enterprise": "Upgrade to Enterprise", "new_salon_title": "Your New Salon", "new_salon_subtitle": "Fill in the information below to create your salon", "validation_errors": "Validation errors:", "salon_name_label": "Salon Name *", "salon_name_hint": "e.g. Animalia Salon", "salon_name_required": "Salon name is required", "salon_name_min_length": "Name must be at least 3 characters", "owner_name_label": "Your Name *", "owner_name_hint": "e.g. <PERSON>", "owner_name_required": "Your name is required", "owner_name_min_length": "Name must be at least 2 characters", "owner_name_helper": "This will appear in the calendar", "description_label": "Description (optional)", "description_hint": "Short description of the salon...", "location_label": "Salon Location", "location_hint": "Select the salon address on the map", "location_required": "Salon location is required", "phone_label": "Phone (optional)", "phone_hint": "+40 XXX XXX XXX", "email_label": "Email (optional)", "email_hint": "<EMAIL>", "create_button": "Create Salon", "name_missing_title": "Name Missing", "name_missing_message": "Please enter the salon name to continue.", "name_missing_action": "Fill in the name", "owner_name_missing_title": "Your Name Missing", "owner_name_missing_message": "Please enter your name to continue.", "owner_name_missing_action": "Fill in the name", "location_missing_title": "Location Missing", "location_missing_message": "Please select the salon location on the map to continue.", "location_missing_action": "Select location", "creation_error_title": "Error Creating Salon", "creation_error_message": "An unexpected problem occurred", "unexpected_error_title": "Unexpected Error", "unexpected_error_message": "A problem occurred while creating the salon. Please try again.", "edit_title": "Edit Salon", "save_button": "Save", "salon_info_title": "Salon Information", "phone_helper_text": "This number will be used for Chief <PERSON><PERSON> (salon owner)", "location_selecting": "Getting current location...", "location_disclaimer": "This location will be used in reminder messages sent to clients", "address_details_label": "Address Details (optional)", "address_details_hint": "e.g. Floor 2, right corner, apartment 15", "address_details_helper": "Add additional details to help clients find you easier", "referral_code_optional": "Referral Code (optional)", "referral_code_description": "Got a code from a friend? Enter it here to receive bonus SMS credits!", "referral_code_label": "Referral Code", "important_info_title": "Important Information", "important_info_description": "After creating the salon, you will become the owner and chief groomer. You will be able to invite other groomers to join your team.", "location_select": "Select location", "location_selected": "Location selected", "map_location_selected": "Selected location", "my_location": "My location", "confirm_location": "Confirm location"}, "date_range_selector": {"time_interval": "Time interval", "days_count": "{count} days", "last_week": "Last week", "last_month": "Last month", "last_3_months": "Last 3 months", "last_6_months": "Last 6 months", "last_year": "Last year", "custom": "Custom"}, "edit_client": {"title": "Edit {name}", "save": "Save", "saving": "Saving...", "edit_info_title": "Edit client information", "registered_on": "Client registered on {date}", "basic_info_title": "Basic information", "contact_info_title": "Contact information", "additional_info_title": "Additional information", "name_label": "Full Name *", "name_hint": "ex: <PERSON>", "name_required": "Name is required", "name_min_length": "Name must be at least 2 characters", "name_max_length": "Name cannot exceed 255 characters", "phone_label": "Phone Number", "phone_hint": "+40 XXX XXX XXX", "phone_helper": "Format: +40 XXX XXX XXX", "phone_max_length": "Phone number cannot exceed 50 characters", "phone_invalid": "Phone number is not valid (ex: +40728626399)", "email_label": "Email Address", "email_hint": "ex: <EMAIL>", "email_max_length": "Email cannot exceed 255 characters", "email_invalid": "Email is not valid", "address_label": "Address (optional)", "address_hint": "Select client address on map", "notes_label": "Notes", "notes_hint": "Additional information about client", "update_button": "Update Client", "info_text": "Fields marked with * are required. All changes will be saved automatically.", "error_update": "Error updating client", "error_connection": "Connection error: {error}", "success_message": "Client updated successfully!"}, "edit_staff_dialog": {"title": "Edit Team Member", "member_info_title": "Member Information", "name_label": "Name", "phone_label": "Phone", "email_label": "Email", "chief_groomer_restriction_title": "Chief <PERSON><PERSON><PERSON> - Restricted Editing", "chief_groomer_restriction_message": "Chief <PERSON><PERSON><PERSON> cannot be edited. Only nickname and notes can be modified.", "display_name_title": "Display Name", "display_name_placeholder": "e.g: <PERSON>, <PERSON>, <PERSON><PERSON>", "display_name_helper": "The name that will appear in appointments and calendar", "display_name_min_error": "Display name must be at least 2 characters", "display_name_max_error": "Display name cannot exceed 50 characters", "display_name_fallback_info": "If not filled, the full name ({name}) will be used.", "team_role_title": "Team Role", "client_permissions_title": "Client Data Access Permissions", "notes_title": "Notes (optional)", "notes_placeholder": "Notes about this team member...", "cancel_button": "Cancel", "save_button": "Save", "update_success": "Team member \"{name}\" has been updated successfully!", "update_error": "Error updating team member: {error}", "update_error_generic": "Error updating team member: {error}"}, "languages": {"ro": "Română", "en": "English"}, "login": {"welcome": "Welcome to Animalia!", "subtitle": "Managing your grooming salon\nhas never been easier", "google_signin": "Continue with Google", "apple_signin": "Continue with Apple", "phone_signin": "Continue with phone number", "or": "or", "terms_and_privacy": "By signing in, you agree to our", "terms_of_service": "Terms of Service", "and": "and", "privacy_policy": "Privacy Policy", "login_error": "Login error", "please_try_again": "Please try again"}, "mass_sms": {"title": "Mass SMS", "back_button": "Back", "step_message": "Message", "step_clients": "Clients", "step_send": "Send", "continue_button": "Continue", "select_clients_button": "Select Clients", "review_button": "Review", "send_button": "Send", "schedule_button": "Schedule", "sending_progress": "Sending...", "message_required_error": "Message and client list are required", "quota_exceeded_title": "SMS Quota Exceeded", "quota_exceeded_message": "You don't have enough SMS available to send this message.", "sms_required_label": "SMS required: {0}", "sms_available_label": "SMS available: {0} of {1}", "quota_exceeded_description": "To send this message, please upgrade your subscription or purchase additional SMS credits.", "cancel_button": "Cancel", "sms_settings_button": "SMS Settings", "success_title": "Success", "success_scheduled_message": "SMS messages have been scheduled successfully!", "success_sent_message": "SMS messages have been sent!", "total_recipients_label": "Total recipients: {0}", "sent_success_label": "Sent successfully: {0}", "failed_label": "Failed: {0}", "ok_button": "OK", "error_title": "Error", "salon_not_found_error": "Could not identify salon", "compose_message_title": "Compose Message", "compose_message_subtitle": "Write the message you want to send to clients or choose a template.", "templates_title": "Templates", "hide_button": "<PERSON>de", "show_button": "Show", "your_message_title": "Your message", "message_placeholder": "Write message here...", "no_templates_available": "No templates available", "phone_preview_title": "Phone preview", "characters_count": "Characters: {0}/1600", "sms_count": "SMS: {0}", "select_clients_title": "Select Clients", "select_clients_subtitle": "Choose the clients you want to send the message to.", "search_clients_placeholder": "Search clients...", "clients_found_label": "Clients found: {0}", "selected_label": "Selected: {0}", "select_all_button": "Select All", "clear_all_button": "Clear All", "loading_clients_error": "Error loading clients", "try_again_button": "Try again", "no_clients_found_title": "No clients found", "no_clients_found_subtitle": "Try modifying the search criteria.", "send_error_default": "Error sending SMS messages", "send_error_generic": "Error: {error}", "advanced_filters_title": "Advanced filters", "customer_loyalty_label": "Customer loyalty:", "appointments_label": "Appointments", "period_label": "Period", "clear_filters_button": "Clear filters", "clients_list_title": "Client list", "clients_found_count": "Clients found: {0}", "selected_count": "Selected: {0}", "clear_selection_button": "Clear All", "review_summary_title": "Mass SMS Summary", "recipients_label": "Recipients:", "message_length_label": "Message length:", "estimated_sms_label": "Estimated SMS:", "total_sms_label": "Total SMS:", "message_preview_title": "Message preview", "recipients_list_title": "Recipients ({0})", "loading_text": "Loading...", "characters_text": "{0} characters", "client_selection_title": "Select Clients", "client_selection_subtitle": "Choose the clients you want to send the message to."}, "messaging_template_management": {"screen_title": "Messaging Templates", "screen_subtitle": "Manage SMS and WhatsApp messages", "sms_tab": "SMS Templates", "whatsapp_tab": "WhatsApp Templates", "help_tooltip": "Help", "no_permission_title": "Restricted Access", "no_permission_message": "You need management access to edit messaging templates.", "error_loading_title": "Failed to load templates", "unknown_error": "Unknown error", "retry_button": "Try again", "fully_editable_title": "Fully Editable", "fully_editable_message": "SMS templates can be completely customized to match your salon's style.", "fixed_templates_title": "Fixed Templates", "fixed_templates_message": "WhatsApp requires pre-approved templates. Choose your preferred option for each message type.", "status_active": "Active", "status_inactive": "Inactive", "characters": "characters", "selected": "Selected", "no_templates_for_type": "No templates available for this message type", "whatsapp_updated_success": "WhatsApp template updated successfully!", "error_prefix": "Error:", "help_dialog_title": "Template Help", "help_understood": "Got it", "help_sms_title": "📱 SMS Templates", "help_sms_content": "Fully customizable messages sent via SMS. Edit the text freely to match your salon's voice and style.", "help_whatsapp_title": "💬 WhatsApp Templates", "help_whatsapp_content": "Pre-approved templates required by WhatsApp Business API. Choose from available options in Romanian or English.", "help_variables_title": "🔤 Variables", "help_variables_content": "Use {VARIABLE_NAME} placeholders that will be replaced with real data:\n• {OWNER_NAME} - Client name\n• {PET_NAME} - Pet name\n• {SALON_NAME} - Salon name\n• {APPOINTMENT_DATE} - Date\n• {APPOINTMENT_TIME} - Time"}, "navigation": {"calendar": "Appointments", "clients": "Clients", "sales": "Sales", "notifications": "Notifications", "salon": "Salon", "profil": "Profile", "exit_app_message": "Press back again to exit the app"}, "new_appointment": {"title": "New Appointment", "appointment_title": "Appointment", "creating_appointment": "Creating appointment...", "appointment_created_success": "Appointment created successfully!", "could_not_create_appointment": "Could not create appointment", "error_prefix": "Error: {error}", "close": "Close", "client_section": "Client", "form_progress": "Form progress", "additional_options": "Additional options", "ok": "OK", "loading_clients": "Loading clients...", "select_client": "Select a client", "pet_name_required": "Pet name *", "pet_name_placeholder": "e.g: <PERSON>, <PERSON>, <PERSON>", "pet_name_validation": "Pet name is required", "validation_client_phone_required": "Please enter the client's phone number", "validation_pet_name_required": "Please enter the pet's name", "validation_pet_breed_required": "Please select the pet's breed", "validation_client_required": "Please select a client", "validation_staff_required": "Please select a team member", "validation_service_required": "Please select at least one service", "validation_start_before_end": "Start time must be before end time", "validation_time_in_past": "You cannot schedule an appointment in the past or too close to the current time. Please select a time at least 15 minutes in the future.", "validation_incomplete_form": "Formular incomplet", "validation_invalid_date": "<PERSON><PERSON><PERSON>", "breed_required": "Breed *", "breed_loading": "Loading breeds...", "breed_placeholder": "Start typing for suggestions...", "breed_refresh_tooltip": "Refresh breed list", "breed_validation": "Breed is required", "client_name_optional": "Client name (optional)", "client_name_helper": "Client name can be added later", "phone_required": "Client phone *", "phone_placeholder": "07xxxxxxxx", "phone_helper": "Format: 07xxxxxxxx - Required field", "phone_validation_required": "Phone is required", "phone_validation_invalid": "Phone number is not valid", "waitlist_added_title": "Adă<PERSON><PERSON>", "waitlist_added_message": "Programarea a fost adăugată la lista de așteptare", "date_time_title": "Appointment date and time", "team_member_title": "Assigned team member", "select_team_member": "Select team member", "notes_label": "Notes", "notes_placeholder": "Add notes for this appointment...", "recurring_appointments": "Recurring appointments", "select_start_time": "Select start time", "select_end_time": "Select end time", "cancel": "Cancel", "confirm": "Confirm", "time_in_past_error": "You cannot schedule an appointment in the past. Please select a future time.", "end_time_before_start_error": "End time must be at least 15 minutes after start time.", "recurrence_title": "Recurring appointments", "every_label": "Every", "period_label": "Period", "period_days": "days", "period_weeks": "weeks", "period_months": "months", "total_appointments_label": "Total number of appointments", "appointment_singular": "appointment", "appointment_plural": "appointments", "preview_label": "Preview", "preview_text": "Will create {total} {appointments} every {frequency}{period}.", "period_day_singular": "day", "period_week_singular": "week", "period_month_singular": "month", "payment_model_title": "Payment model", "payment_per_appointment": "Payment per appointment", "payment_per_appointment_subtitle": "Pay for each appointment separately (standard behavior)", "payment_upfront_discount": "Upfront payment with discount", "payment_upfront_subtitle": "Pay for all appointments upfront with custom discount", "payment_upfront_subtitle_with_discount": "Pay for all appointments upfront with {discount}% discount", "discount_percentage_label": "Discount percentage (%)", "discount_validation_error": "Percentage must be between 0 and 100", "payment_summary_title": "Payment summary", "price_per_appointment": "Price per appointment:", "number_of_appointments": "Number of appointments:", "original_total": "Original total:", "discount_amount": "Discount ({discount}%):", "total_to_pay": "Total to pay:", "savings_message": "You save {amount} RON!", "appointment_details_title": "Appointment Details", "status_confirmed": "Confirmed", "status_scheduled": "Scheduled", "status_pending": "Pending", "status_cancelled": "Cancelled", "status_completed": "Completed", "client_info_title": "Client Information", "client_name_label": "Name:", "client_phone_label": "Phone:", "client_address_label": "Address:", "pet_info_title": "Pet Information", "pet_name_label": "Name:", "pet_breed_label": "Breed:", "pet_gender_label": "Gender:", "pet_age_label": "Age:", "pet_weight_label": "Weight:", "pet_color_label": "Color:", "pet_gender_male": "Male", "pet_gender_female": "Female", "pet_gender_unknown": "Unknown", "service_details_title": "Service Details", "service_label": "Service:", "services_label": "Services:", "total_duration_label": "Total Duration:", "start_time_label": "Start Time:", "end_time_label": "End Time:", "staff_info_title": "Assigned Staff", "staff_name_label": "Name:", "staff_full_name_label": "Full Name:", "staff_phone_label": "Phone:", "staff_specialties_label": "Specialties:", "staff_phone_not_available": "Not available", "notes_title": "Service Notes", "photos_title": "Photos", "add_photo_tooltip": "Add photo", "no_photos_message": "No photos for this appointment", "add_first_photo_hint": "Tap + to add the first photo", "photo_options_title": "Photo Options", "add_photo_title": "Add Photo", "camera_option": "Camera", "gallery_option": "Gallery", "view_photo_option": "View", "delete_photo_option": "Delete", "photo_uploaded_success": "Photo uploaded and saved successfully", "photo_upload_error": "Error saving photo: {error}", "photo_deleted_success": "Photo deleted and saved", "call_button": "Call", "sms_button": "SMS", "whatsapp_button": "WhatsApp", "tap_for_client_details": "Tap to view complete client details", "complete_button": "Complete", "modify_button": "Modify", "cancel_button": "Cancel", "cancel_appointment_title": "<PERSON>cel Appointment", "cancel_confirmation": "Are you sure you want to cancel this appointment?", "cancel_reason_label": "Cancellation reason (optional)", "cancel_yes_button": "Yes, cancel", "cancel_no_button": "No", "appointment_cancelled_success": "Appointment cancelled successfully", "cancel_error": "Error cancelling appointment: {error}", "reschedule_title": "Reschedule Appointment", "reschedule_button": "Reschedule", "reschedule_success": "Appointment rescheduled successfully", "reschedule_date_label": "Date", "reschedule_start_time_label": "Start Time", "reschedule_end_time_label": "End Time", "reschedule_reason_label": "Reschedule reason (optional)", "reschedule_time_error": "End time must be after start time", "reschedule_error": "Error rescheduling: {error}", "special_attention_needed": "Needs special attention", "groomer_notes_label": "<PERSON><PERSON><PERSON> notes", "pet_observations_label": "Observations", "price_info_title": "Price Information", "total_price_label": "Total Price:", "pet_size_label": "<PERSON>:", "completion_title": "Completion", "completion_time_label": "Completion time", "time_difference_label": "Difference:", "actual_duration_label": "Actual Duration:", "delete_photo_title": "Delete Photo", "delete_photo_confirmation": "Are you sure you want to delete this photo?", "pet_observations_standard": "Observations - Standard pet", "service_single_label": "Service", "total_duration_short": "Total Duration", "start_time_short": "Start Time", "end_time_short": "End Time", "price_info_short": "Price Information", "total_price_short": "Total Price", "pet_size_short": "<PERSON>", "create": "Create", "complete_appointment_button": "Complete", "modify_appointment_button": "Modify", "cancel_appointment_button": "Cancel", "complete_appointment_dialog_title": "Complete appointment", "reschedule_appointment_dialog_title": "Reschedule appointment", "cancel_appointment_dialog_title": "Cancel appointment", "complete_appointment_dialog_widget_title": "Complete appointment", "completion_notes_label": "Completion notes (optional)", "completion_notes_hint": "Add notes about appointment completion...", "completion_cancel_button": "Cancel", "completion_complete_button": "Complete", "reschedule_cancel_button": "Cancel", "reschedule_confirm_button": "Reschedule", "reschedule_time_validation_error": "End time must be after start time", "cancel_confirmation_message": "Are you sure you want to cancel this appointment?"}, "notification_messages": {"new_appointment_title": "New Appointment", "new_appointment_body": "New appointment for {petName} ({clientName}) on {date}", "appointment_cancelled_title": "Appointment Cancelled", "appointment_cancelled_body": "Appointment for {petName} ({clientName}) on {date} has been cancelled", "payment_confirmed_title": "Payment Confirmed", "payment_confirmed_body": "Payment of {amount} RON from {clientName} confirmed", "team_update_title": "Team Update", "team_member_joined": "{member<PERSON><PERSON>} has joined the team", "team_member_left": "{member<PERSON><PERSON>} has left the team", "team_member_role_changed": "{member<PERSON><PERSON>}'s role has been changed to {newRole}", "team_member_update_default": "Team update: {memberName}", "system_maintenance_title": "Scheduled System Maintenance", "system_maintenance_body": "{maintenanceType} maintenance scheduled on {date}"}, "notification_settings": {"title": "Notification Settings", "general_settings": "General Settings", "sound_vibration": "Sound & Vibration", "do_not_disturb": "Do Not Disturb", "notification_rules": "Notification Rules", "push_notifications": "Push Notifications", "push_notifications_subtitle": "Enable all push notifications", "vibration": "Vibration", "vibration_subtitle": "Vibrate for notifications", "do_not_disturb_title": "Do Not Disturb", "do_not_disturb_subtitle": "Enable Do Not Disturb mode", "start_time": "Start time:", "end_time": "End time:", "allow_critical": "Allow critical notifications", "allow_critical_subtitle": "Allow important notifications during Do Not Disturb", "new_appointments": "New appointments", "new_appointments_subtitle": "Notification for new appointments", "appointment_cancellations": "Appointment cancellations", "appointment_cancellations_subtitle": "Notification for appointment cancellations", "appointment_rescheduled": "Rescheduled appointments", "appointment_rescheduled_subtitle": "Notification for rescheduled events", "settings_updated_success": "Notification settings have been updated successfully!", "settings_update_error": "Error updating notification settings", "settings_update_generic_error": "Error updating settings: {error}", "failed_to_load_settings": "Failed to load notification settings", "error_loading_settings": "Error loading notification settings: {error}", "error_loading_title": "Error loading notification settings", "unknown_error": "Unknown error", "try_again": "Try again", "restricted_access_title": "Restricted access", "restricted_access_message": "Only head groomers can configure notification settings for the salon.", "back_button": "Back", "deleted_appointment": "Deleted Appointment", "deleted": "Deleted"}, "notifications": {"title": "Notifications", "settings_tooltip": "Notification settings", "help_tooltip": "Help", "settings_tour_title": "Notification Settings", "settings_tour_description": "Configure which types of notifications you receive and how they are displayed.", "invitations_tour_title": "Invitations", "invitations_tour_description": "Here you can see invitations from other salons to join their team.", "list_tour_title": "Notifications", "list_tour_description": "Here you will see all notifications about new appointments, cancellations, reminders and other important events in the salon. Tap on a notification to see details or take action.", "count": "{count} notifications", "unread_count": "{count} unread", "all_read": "All read", "mark_all_read": "Mark all as read", "read_all": "Read all", "no_notifications": "No notifications", "notifications_will_appear": "Notifications will appear here", "swipe_to_delete": "Delete", "mark_as_read": "<PERSON> as read", "now": "Now", "minutes_ago": "{minutes} min ago", "hours_ago": "{hours}h ago", "yesterday_at": "Yesterday at {time}", "days_ago": "{days} days ago", "appointment_not_found": "Appointment could not be found.", "error_opening_appointment": "Error opening appointment: {error}", "error_deleting_notification": "Error deleting notification: {error}", "invitations_pending": "Pending Invitations", "invitations_from_salons": "{count} invitations from salons", "invited_by": "Invited by {name}", "today": "Today!", "days_short": "{days}d", "decline": "Decline", "accept": "Accept", "joined_salon_success": "You have successfully joined {salonName}!", "error_accepting_invitation": "Error accepting invitation", "error_accepting_invitation_detail": "Error accepting invitation: {error}", "decline_invitation_title": "Decline invitation?", "decline_invitation_message": "Are you sure you want to decline the invitation from {<PERSON><PERSON>ame}?", "invitation_declined": "Invitation from {salonName} has been declined", "could_not_decline_invitation": "Could not decline invitation", "error_declining_invitation": "Error declining invitation: {error}"}, "pets": {"pets": "Pets", "no_pets_registered": "No pets registered", "add_first_pet": "Add first pet", "add_new_pet": "Add new pet", "pet_details": "Pet Details", "add_pet_title": "Add pet for {clientName}", "edit_pet_title": "Edit pet", "pet_name": "Pet name", "pet_name_required": "Pet name *", "pet_name_placeholder": "e.g: <PERSON>, <PERSON>, <PERSON>", "pet_name_required_error": "Name is required", "pet_name_min_length_error": "Name must be at least 2 characters", "species": "Species", "species_required": "Species *", "dog": "Dog", "cat": "Cat", "other": "Other", "rabbit": "Rabbit", "dog_with_icon": "Dog 🐕", "cat_with_icon": "Cat 🐱", "rabbit_with_icon": "Rabbit 🐰", "breed": "Breed", "breed_required": "Breed *", "breed_placeholder": "Start typing for suggestions...", "breed_loading": "Loading breeds...", "breed_refresh_tooltip": "Refresh breed list", "breed_list_updated": "Breed list has been updated", "breed_update_error": "Error updating breeds", "gender": "Gender", "gender_required": "Gender *", "male": "Male", "female": "Female", "male_with_icon": "Male ♂", "female_with_icon": "Female ♀", "birth_date": "Birth date", "age": "Age", "age_years_months": "{years} years, {months} months", "age_one_year": "1 year", "age_years": "{years} years", "age_one_month": "1 month", "age_months": "{months} months", "age_one_week": "1 week", "age_weeks": "{weeks} weeks", "age_one_day": "1 day", "age_days": "{days} days", "weight": "Weight (kg)", "weight_required": "Weight (kg) *", "weight_placeholder": "e.g: 25.5", "weight_validation_error": "Weight must be a valid number", "weight_positive_error": "Weight must be greater than 0", "weight_too_large_error": "Weight seems too large", "color": "Color", "color_placeholder": "e.g: <PERSON>, <PERSON>", "microchip": "Microchip", "microchip_number": "Microchip number", "microchip_placeholder": "e.g: 123456789012345", "notes": "Notes", "notes_placeholder": "Additional information about the pet", "photo": "Photo", "add_photo": "Add photo", "change_photo": "Tap to change photo", "select_image_source": "Select image source", "camera": "Camera", "gallery": "Gallery", "delete_image": "Delete image", "image_selection_error": "Error selecting image: {error}", "basic_info": "Basic information", "physical_characteristics": "Physical characteristics", "additional_info": "Additional information", "details": "Details", "not_specified": "Not specified", "save": "Save", "saving": "Saving...", "add_pet_button": "Add pet", "update_pet_button": "Update pet", "pet_added_success": "{petName} has been added successfully!", "pet_updated_success": "Pet has been updated successfully!", "save_error": "Error saving pet", "update_error": "Error updating pet", "connection_error": "Connection error: {error}", "required_fields_info": "Fields marked with * are required. Photos and additional information can be added later.", "metis": "Mixed breed", "unknown": "Unknown", "delete_pet_title": "Delete {petName}", "delete_pet_confirmation": "Are you sure you want to delete this pet? This action cannot be undone.", "delete_success": "{petName} has been deleted successfully!", "delete_error_generic": "Error deleting", "delete_error": "Error deleting pet: {error}", "deleting_pet": "Deleting pet...", "needs_special_attention": "Needs special attention", "needs_special_attention_description": "Pet requires special attention", "no_special_attention_needed": "No special attention needed", "health_status_updated": "Health status has been updated!", "health_status_update_error": "Error updating health status: {error}", "groomer_notes": "<PERSON><PERSON><PERSON> notes", "edit_notes_tooltip": "Edit notes", "add_groomer_notes_hint": "Add notes for groomer...", "add_groomer_notes_prompt": "Tap to add notes for groomer...", "notes_updated": "Groomer notes have been updated!", "notes_update_error": "Error updating notes: {error}"}, "pets_report": {"title": "Pets Report", "export_excel_tooltip": "Export Excel", "demo_button": "DEMO", "real_button": "REAL", "sample_data_indicator": "Showing sample data for demonstration", "total_pets": "Total Pets", "size_types": "Size Types", "different_breeds": "Different Breeds", "size_distribution_title": "Size Distribution", "breed_distribution_title": "Breed Distribution", "size_axis_label": "Size", "breed_axis_label": "Breed", "pets_count_axis_label": "Number of pets", "export_chart_tooltip": "Export chart", "size_breakdown_title": "Size Distribution", "top_breeds_title": "Top 5 Breeds", "category_column": "Category", "count_column": "Count", "percentage_column": "Percentage", "empty_state_title": "No data available", "empty_state_subtitle": "Select a different interval or enable sample data", "generating_report": "Generating report...", "excel_sheet_title": "Pets Report", "excel_report_title": "Pets Report by <PERSON><PERSON> and <PERSON>d", "excel_total_pets": "Total pets: {count}", "excel_size_header": "Size", "excel_count_header": "Count", "excel_filename": "pets_report.xlsx", "excel_share_text": "Pets report generated from Animalia app", "report_downloaded_success": "Report downloaded successfully!", "error_loading_data": "Error loading data: {error}", "export_chart_coming_soon": "Export {type} chart - coming soon!"}, "profile_screen": {"salon_title": "Salon", "profile_title": "Profile", "help_tooltip": "Help", "restricted_access_title": "Restricted Access", "restricted_access_message": "This section is available only for salon owners.", "loading_profile": "Loading profile...", "profile_load_error_title": "Error loading profile", "unknown_error": "Unknown error", "try_again": "Try again", "management_section": "Management", "business_section": "Business", "my_services_title": "My services", "my_services_subtitle": "Manage offered services", "my_services_subtitle_with_count": "Manage offered services ({count} services)", "no_services_warning": "You have no services configured!", "my_team_title": "My team", "my_team_subtitle": "Manage members", "programming_website_title": "Programming Website", "programming_website_subtitle": "Create and manage your salon's website", "programming_website_subtitle_configured": "View and manage your booking page", "no_website_warning": "No website is currently associated with your salon. Create one to enhance your online presence.", "website_updated_success": "Website updated successfully", "no_website_to_open": "No website URL to open", "invalid_website_format": "Please enter a valid website URL", "website_info_title": "Website Information", "website_info_description": "Add your salon's website URL to enhance your online presence. This will be displayed to clients and can help them find more information about your services.", "website_url_label": "Website URL", "website_url_hint": "Enter your website URL", "website_url_help": "You can enter with or without https:// - we'll add it automatically", "open_website": "Open Website", "basic_info_step": "Details", "customize_booking_step": "Customize", "business_hours_step": "Hours", "extra_info_step": "Policies", "booking_acceptance_step": "Acceptance", "basic_info_title": "Basic Information", "booking_website_help": "This link is automatically generated based on your business name. Spaces are replaced with underscores.", "business_name_label": "Business Name", "business_name_hint": "Enter your business name", "business_description_label": "Business Description", "business_description_hint": "Describe your business and services", "business_address_label": "Business Address", "select_business_address": "Select your business address", "contact_information_label": "Contact Information", "phone_label": "Phone Number", "phone_hint": "Enter your phone number", "email_label": "Email Address", "email_hint": "Enter your email address", "facebook_label": "Facebook Link", "instagram_label": "Instagram Link", "tiktok_label": "TikTok Link", "customize_booking_title": "Customize Your Booking Website", "work_in_progress": "Work in Progress", "customize_booking_description": "This feature is coming soon. You'll be able to customize the appearance and functionality of your booking website.", "business_hours_title": "Business Hours", "day_monday": "Monday", "day_tuesday": "Tuesday", "day_wednesday": "Wednesday", "day_thursday": "Thursday", "day_friday": "Friday", "day_saturday": "Saturday", "day_sunday": "Sunday", "closed": "Closed", "extra_info_title": "Extra Information", "cancellation_policy_label": "Cancellation and Rescheduling Policy", "cancellation_24_hours": "Clients can cancel or reschedule appointments up to 24 hours before start time", "cancellation_48_hours": "Clients can cancel or reschedule appointments up to 48 hours before start time", "cancellation_72_hours": "Clients can cancel or reschedule appointments up to 72 hours before start time", "cancellation_no_changes": "No cancellations or rescheduling allowed", "booking_acceptance_title": "Booking Acceptance", "booking_automatic": "Automatically accept all appointment requests", "booking_manual": "Manually accept or decline all appointment requests", "booking_automatic_description": "All appointment requests will be automatically confirmed", "booking_manual_description": "You will review and approve each appointment request", "field_required": "{field} is required", "invalid_email_format": "Please enter a valid email address", "website_photos_title": "Website Photos", "website_photos_description": "Upload photos to showcase your salon and services on your booking website. You can add up to 6 photos.", "no_photos_yet": "No photos added yet", "add_photo": "Add Photo", "choose_from_gallery": "Choose from Gallery", "take_photo": "Take Photo", "additional_customization_title": "Additional Customization", "additional_customization_description": "More customization options for your booking website will be available soon.", "coming_soon_features": "Color themes, layout options, and more customization features coming soon!", "photo_selection_error": "Error selecting photos. Please try again.", "photos_uploaded_success": "Photos uploaded successfully", "photo_upload_error": "Error uploading photos. Please try again.", "sms_reminders_title": "SMS Reminders", "sms_reminders_subtitle": "SMS notifications for clients", "work_schedule_title": "Work schedule", "work_schedule_subtitle": "View members' schedule", "performance_reports_title": "Performance reports", "performance_reports_subtitle": "Business analytics and statistics", "notifications_title": "Notifications", "notifications_subtitle": "Salon notification settings", "mass_sms_title": "Mass SMS", "mass_sms_subtitle": "Send messages to multiple clients", "referral_system_title": "SMS Referral System", "referral_system_subtitle": "Invite friends and earn credits", "quick_contact_title": "Quick contact", "quick_contact_subtitle": "Support and assistance", "contact_call": "Call", "contact_sms": "SMS", "contact_whatsapp": "WhatsApp", "subscription_title": "Subscription", "subscription_upgrade_label": "UPGRADE", "subscription_upgrade_message": "Activate a plan for advanced features", "subscription_plan_prefix": "Plan", "subscription_trial_status": "Trial period • {date}", "subscription_active_status": "Active • {date}", "subscription_expired": "Expired", "subscription_expires_today": "Expires today", "subscription_expires_tomorrow": "Expires tomorrow", "subscription_expires_days": "Expires in {days} days", "subscription_expires_months": "Expires in {months} month", "subscription_expires_months_plural": "Expires in {months} months", "tier_free": "Free", "tier_freelancer": "Freelancer", "tier_team": "Team", "tier_enterprise": "Enterprise", "salon_not_found_title": "Could not find current salon", "salon_not_found_message": "Please make sure you are connected to a valid salon.", "error_title": "Error", "error_salon_not_found": "Current salon could not be found.", "showcase_services_title": "Services", "showcase_services_description": "Add grooming services and set prices. You can configure fixed prices, based on animal size, or custom price ranges.", "showcase_team_title": "My Team", "showcase_team_description": "Manage salon members. They will be available in the calendar, can be added directly or invited by phone number. Everyone will see the salon's collective calendar.", "showcase_sms_title": "Automated messages", "showcase_sms_description": "Configure when clients receive messages and choose EXACTLY how you want the message to look.", "showcase_schedule_title": "Schedule", "showcase_schedule_description": "Set working hours and days off for the team. Use smart templates to quickly configure the weekly schedule.", "showcase_subscription_title": "Subscription", "showcase_subscription_description": "View current plan and upgrade to access more premium features for your salon.", "trial_period": "Trial period", "active": "Active", "manage_services": "Manage offered services", "manage_services_with_count": "Manage offered services ({count} services)", "expires_in_days": "Expires in {days} days", "expires_in_day": "Expires in {days} day", "expires_in_months": "Expires in {months} months", "expires_in_month": "Expires in {months} month", "error_phone_app": "phone app", "error_sms_app": "messaging app", "error_whatsapp_app": "WhatsApp"}, "referral_system": {"title": "SMS Referral System", "description": "Invite friends and earn benefits for each referral that pays for a subscription", "your_referral_code": "Your referral code", "loading_code": "Loading code...", "copy_code_tooltip": "Copy code", "share_code_tooltip": "Share code", "share_message": "Share this code with your friends to earn benefits!", "claim_code_button": "Claim Code", "code_claimed_button": "Code Claimed", "loading_button": "Loading...", "already_claimed_info": "You have already claimed a referral code. You can only claim one code per account.", "claim_dialog_title": "Claim Referral Code", "claim_dialog_description": "Enter the referral code received from a friend to get free SMS credits!", "referral_code_label": "Referral Code", "referral_code_hint": "Ex: ABC12345", "code_format_error": "Code must have 8 characters (letters and numbers)", "cancel_button": "Cancel", "claim_button": "<PERSON><PERSON><PERSON>", "code_copied": "Code copied: {0}", "share_error": "Error sharing code", "already_claimed_warning": "You have already claimed a referral code. You can only claim one code per account.", "invalid_code_title": "Invalid code", "invalid_code_message": "Please enter a valid referral code", "error_title": "Error", "salon_not_found": "Could not identify salon", "validation_error": "Error validating code", "claim_error": "Error claiming code", "unknown_error": "Unknown error", "success_title": "Congratulations!", "success_message": "You have successfully claimed the referral code!", "sms_credits_awarded": "+{0} SMS", "credits_added": "SMS credits added to your account!", "code_claimed_label": "Code claimed: {0}", "excellent_button": "Excellent!", "code_valid": "Valid code!", "code_invalid": "Invalid code", "validation_error_generic": "Error validating code", "salon_identification_error": "Error: Could not identify salon", "share_invitation_message": "Hello! 👋\n\nI invite you to try the Animalia app for managing salon appointments! 💅\n\nUse my referral code: {0}\n\nDownload the app and enjoy the benefits of the referral system! 🎁", "share_subject": "Animalia Invitation - Referral Code: {0}", "progress_title": "Referral Progress", "progress_description": "Invite friends and earn more and more SMS credits!", "referral_singular": "Referral", "messages_count": "messages", "cannot_claim_own_code": "You cannot claim your own referral code", "salon_id_error": "Could not get salon ID", "stats_load_error": "Error loading statistics", "try_again": "Try again", "referrals_label": "Referrals", "credits_earned_label": "Credits earned", "next_reward_label": "Next reward"}, "revenue_report": {"title": "Revenue Report", "export_excel": "Export Excel", "demo": "DEMO", "real": "REAL", "sample_data_message": "Showing sample data for demonstration", "revenue_summary": "Revenue Summary", "total_revenue": "Total Revenue", "daily_average": "Daily Average", "growth_rate": "Growth Rate", "active_days": "Active Days", "growth": "Growth", "decline": "Decline", "growth_comparison": "{growth} of {percentage}% compared to previous period", "daily_revenue_evolution": "Daily Revenue Evolution", "revenue_by_service": "Revenue by Service", "date_label": "Date", "revenue_label": "Revenue (RON)", "service_label": "Service", "export_chart": "Export chart", "analysis_trends": "Analysis and Trends", "best_day": "Best day", "worst_day": "Worst day", "top_service": "Top revenue service", "revenue_distribution": "Revenue Distribution by Service", "service_column": "Service", "revenue_column": "Revenue", "percent_column": "Percentage", "no_data_available": "No data available", "no_data_suggestion": "Select a different interval or enable sample data", "loading_error": "Error loading data: {error}", "generating_report": "Generating report...", "excel_total_revenue": "Total revenue: {amount} RON", "excel_filename": "revenue_report.xlsx", "excel_share_text": "Revenue report generated from Animalia app", "excel_success": "Report downloaded successfully!", "excel_error": "Error: {error}", "chart_export_coming_soon": "Export {type} chart - coming soon!"}, "salon_switcher": {"title": "Switch Salon", "switch_to_salon": "Switch to this salon", "switching_progress": "Switching...", "active_badge": "ACTIVE", "unknown_address": "Unknown address", "edit_action": "Edit", "delete_action": "Delete", "details_action": "Details", "invitations_count": "Invitations ({count})", "delete_salon_title": "Delete Salon", "delete_salon_message": "Are you sure you want to delete salon \"{name}\"?\n\nThis action cannot be undone.", "delete_salon_button": "Delete", "salon_deleted_success": "Salon \"{name}\" has been deleted successfully", "salon_delete_error": "Error deleting salon: {error}", "salon_info_title": "Salon information", "salon_info_address": "Address: {address}", "salon_info_role": "Role: {role}", "salon_info_permissions": "Permissions: {permissions}", "salon_info_phone": "Phone: {phone}", "salon_info_email": "Email: {email}", "salon_info_close": "Close"}, "schedule_management": {"weekly_schedule": "Weekly Schedule", "swipe_to_toggle": "Swipe to activate/deactivate days or tap to edit", "holidays_free_days": "Holidays and Days Off", "no_holidays_next_30_days": "No holidays in the next 30 days", "we_work": "We work", "working_day": "Working day", "working_hours": "Working hours", "lunch_break": "Lunch break", "start": "Start", "end": "End", "break_start": "Break start", "break_end": "Break end", "edit_day": "Edit {day}", "configure_schedule": "Configure schedule for this day", "cancel": "Cancel", "save": "Save", "saving": "Saving...", "save_changes": "Save changes", "custom_closures": "Custom Closures", "add": "Add", "no_custom_closures": "No custom closures scheduled", "delete_closure": "Delete closure", "confirm_delete": "Confirm deletion", "confirm_delete_closure": "Are you sure you want to delete the closure from {range}?", "delete": "Delete", "add_custom_closure": "Custom closure", "add_custom_closure_subtitle": "Add a special closure day", "closure_reason": "Closure reason", "closure_period": "Closure period", "closure_period_subtitle": "Selected period", "additional_description": "Additional description (optional)", "add_details_placeholder": "Add additional details...", "add_closure": "Add closure", "select_reason_error": "Please select a reason for closure", "weekdays": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "holidays": {"anul_nou": "New Year", "a_doua_zi_de_anul_nou": "Second Day of New Year", "unirea_principatelor_romane": "Union of Romanian Principalities", "ziua_muncii": "Labor Day", "ziua_copilului": "Children's Day", "adormirea_maicii_domnului": "Assumption of Mary", "sfantul_andrei": "<PERSON>", "ziua_nationala_a_romaniei": "National Day of Romania", "craciunul": "Christmas", "a_doua_zi_de_craciun": "Second Day of Christmas", "boboteaza": "Epiphany", "vinerea_mare": "Good Friday", "pastele": "Easter", "a_doua_zi_de_paste": "Easter Monday", "rusaliile": "Pentecost", "a_doua_zi_de_rusalii": "Whit Monday"}}, "schedule_templates": {"title": "Quick templates", "subtitle": "Select a template to quickly configure the schedule", "standard_schedule_name": "Standard schedule", "standard_schedule_desc": "Mon-Fri 9:00-17:00\nSat 10:00-15:00", "weekdays_only_name": "Weekdays only", "weekdays_only_desc": "Mon-Fri 8:00-18:00\nWeekend off", "extended_schedule_name": "Extended schedule", "extended_schedule_desc": "Mon-Sat 8:00-20:00\nLong hours", "weekend_focus_name": "Weekend focus", "weekend_focus_desc": "Thu-Sun\nFor weekend salons", "nonstop_schedule_name": "Non-stop schedule", "nonstop_schedule_desc": "Open 24/7 permanently", "day_monday_abbr": "M", "day_tuesday_abbr": "T", "day_wednesday_abbr": "W", "day_thursday_abbr": "T", "day_friday_abbr": "F", "day_saturday_abbr": "S", "day_sunday_abbr": "S"}, "service_form": {"edit_title": "Edit Service", "add_title": "Add New Service", "service_name_required": "Service name *", "service_name_placeholder": "e.g: Grooming, Wash and dry", "service_name_validation_required": "Service name is required", "service_name_validation_length": "Name cannot exceed 255 characters", "variable_pricing_title": "Variable price and duration", "variable_pricing_enabled": "Different prices and durations for S/M/L", "variable_pricing_disabled": "Fixed price and duration for all sizes", "price_range_title": "Price range", "price_range_enabled": "Minimum and maximum price for service", "price_range_disabled": "Fixed price for service", "advanced_options": "Advanced Options", "advanced_options_subtitle": "Additional configurations for service", "optional": "Optional", "price_min_label": "Minimum price (RON)", "price_max_label": "Maximum price (RON)", "price_label": "Price (RON)", "price_placeholder": "e.g: 50.00", "price_validation_required": "Price is required", "price_validation_min_value": "Price must be at least 0.01 RON", "price_validation_min_max": "Minimum price must be less than maximum", "price_validation_max_min": "Maximum price must be greater than minimum", "duration_label": "Duration (min)", "duration_placeholder": "e.g: 60", "duration_validation_required": "Duration is required", "duration_validation_range": "Duration must be between 1-480 min", "color_label": "Color", "size_small": "Small", "size_medium": "Medium", "size_large": "Large", "size_label": "Size {size}", "description_label": "Description", "description_placeholder": "Detailed description of the service...", "description_validation": "Description cannot exceed 1000 characters", "display_order_label": "Display order", "display_order_placeholder": "0 = first, 1 = second, etc.", "requirements_title": "Additional service requirements", "requirements_subtitle": "Select requirements needed for this service", "no_requirements_selected": "No requirements selected", "status_title": "Service status", "status_active": "Active - available for appointments", "status_inactive": "Inactive - hidden from list", "create_another": "Create Another Service", "close": "Close", "cancel": "Cancel", "create_service": "Create Service", "save_changes": "Save Changes", "invalid_data_title": "Invalid data", "success_title": "Success", "service_created_success": "Service \"{name}\" created successfully!", "service_updated_success": "Service \"{name}\" updated successfully!", "error_title": "Error", "save_error_generic": "Could not save service", "connection_error": "Connection error: {error}", "validation_required": "Required", "validation_min_value": ">={value}", "validation_min_max_short": "Min < Max", "validation_max_min_short": "Max > Min", "validation_duration_range_short": "1-480 min", "price_min_short": "Min (RON)", "price_max_short": "<PERSON> (RON)", "duration_short": "Duration (min)", "base_price_optional": "Base price (RON) - Optional"}, "service_requirements": {"vaccination_up_to_date": "Vaccination up to date", "recent_deworming": "Recent deworming", "no_contagious_diseases": "No contagious diseases", "sociable_animal": "Sociable animal", "advance_booking": "Advance booking", "owner_presence": "Owner presence", "veterinary_certificate": "Veterinary certificate", "medical_history": "Medical history"}, "service_selection": {"service_label": "Service", "add_another_service": "Add another service", "select_service_required": "Select a service *", "select_service_title": "Select service", "create_new_service": "Create new service", "size_label": "Size {size}", "cancel": "Cancel", "edit_service_title": "Edit service: {name}", "custom_service_name": "Custom service name", "custom_service_name_placeholder": "Enter custom name", "custom_price_label": "Price (RON)", "custom_price_placeholder": "Enter price in RON", "custom_description_label": "Description (optional)", "custom_description_placeholder": "Add custom description", "custom_service_info": "These changes apply only to this appointment.", "reset": "Reset", "save": "Save", "name_required_error": "Service name cannot be empty.", "price_invalid_error": "Price must be a valid number.", "service_customized_success": "Service has been customized for this appointment.", "add_service_error": "Error adding service: {error}", "error_title": "Error", "success_title": "Success"}, "services_management": {"title": "Services Management", "services_available_count": "{count} services available", "inactive_badge": "INACTIVE", "edit_action": "Edit", "activate_action": "Activate", "deactivate_action": "Deactivate", "duplicate_action": "Duplicate", "delete_permanent_action": "Delete permanently", "requirements_count": "{count} requirements", "error_loading_services_with_details": "Error loading services: {error}", "error_with_details": "Error: {error}", "error_changing_status": "Error changing status: {error}", "error_duplicating_service": "Error duplicating service: {error}", "error_deactivating_service": "Error deactivating service: {error}", "error_deleting_permanently": "Error deleting permanently: {error}", "service_activated_success": "Service \"{name}\" has been activated", "service_deactivated_success": "Service \"{name}\" has been deactivated", "service_duplicated_success": "Service \"{name}\" has been duplicated successfully", "service_deleted_permanently_success": "Service \"{name}\" has been deleted permanently", "confirm_deactivation_title": "Confirm deactivation", "confirm_deactivation_message": "Are you sure you want to deactivate service \"{name}\"?", "deactivation_explanation": "The service will be deactivated and will no longer be available for new appointments, but will remain in the system.", "deactivate_button": "Deactivate", "confirm_permanent_delete_title": "Confirm permanent deletion", "confirm_permanent_delete_message": "Are you sure you want to permanently delete service \"{name}\"?", "warning_attention": "WARNING!", "permanent_delete_warning": "This action will permanently delete the service from the system. All associated data will be lost permanently.", "delete_permanent_button": "Delete permanently"}, "services_report": {"title": "Services Report", "export_excel_tooltip": "Export Excel", "demo_button": "DEMO", "real_button": "REAL", "sample_data_indicator": "Showing sample data for demonstration", "total_services": "Total Services", "total_revenue": "Total Revenue", "popular_service": "Popular Service", "chart_type_label": "Chart type:", "requests_button": "Requests", "revenue_button": "Revenue", "revenue_chart_title": "Revenue by Service", "requests_chart_title": "Requests by Service", "service_axis_label": "Service", "revenue_axis_label": "Revenue (RON)", "requests_axis_label": "Number of requests", "export_chart_tooltip": "Export chart", "performance_table_title": "Service Performance", "service_column": "Service", "requests_column": "Requests", "revenue_column": "Revenue", "percentage_column": "Percentage", "empty_state_title": "No data available", "empty_state_subtitle": "Select a different interval or enable sample data", "generating_report": "Generating report...", "excel_sheet_title": "Services Report", "excel_total_revenue": "Total revenue: {amount} RON", "excel_service_header": "Service", "excel_requests_header": "Requests", "excel_revenue_header": "Revenue (RON)", "excel_filename": "services_report.xlsx", "excel_share_text": "Services report generated from Animalia app", "report_downloaded_success": "Report downloaded successfully!", "error_loading_data": "Error loading data: {error}", "export_chart_coming_soon": "Export {type} chart - coming soon!"}, "settings": {"title": "Settings", "user_settings": "User settings", "language": "Language", "language_selection": "Select language", "theme": "Theme", "dark_mode": "Dark mode", "light_mode": "Light mode", "notifications": "Notifications", "account": "Account", "logout": "Logout", "about": "About", "version": "Version", "language_changed": "Language changed", "restart_required": "You may need to restart the app to see all changes", "salon_management_description": "Tap here to switch active salon, edit salon data or create a new salon.", "help_tooltip": "Help", "tour_restart_success": "Tour restarted! Follow the guided steps.", "tour_restart_error": "Error: Could not start tour", "salon_section": "Salon", "user_section": "User", "application_section": "Application", "edit_name_title": "Edit Name", "edit_name_label": "Name", "edit_name_hint": "Enter your name", "edit_name_current": "Current name: {name}", "edit_name_success": "Name has been updated successfully!", "edit_name_updating": "Updating name...", "edit_name_error": "Error: {error}", "edit_phone_title": "Edit Phone", "edit_phone_label": "Phone number", "edit_phone_hint": "Enter phone number", "edit_phone_current": "Current phone: {phone}", "edit_phone_continue": "Continue", "edit_phone_success": "Phone number has been updated successfully!", "sms_verification_title": "SMS Verification", "sms_verification_message": "We sent a verification code to {phone}", "sms_verification_code_label": "Verification code", "sms_verification_code_hint": "Enter the code received via SMS", "sms_verification_code_required": "Code is required", "sms_verification_code_length": "Code must have 6 digits", "sms_verification_verify": "Verify", "sms_verification_sending": "Sending verification code...", "sms_verification_verifying": "Verifying code...", "sms_verification_error": "Error sending SMS: {error}", "sms_verification_code_error": "Error verifying code: {error}", "theme_dark_interface": "Dark interface for eyes", "theme_light_interface": "Bright and clear interface", "language_app_selection": "Select application language", "privacy_policy_title": "Privacy Policy", "privacy_policy_subtitle": "View privacy policy", "terms_conditions_title": "Terms and Conditions", "terms_conditions_subtitle": "View terms and conditions", "demo_video_title": "Demo Video", "demo_video_subtitle": "Watch the video guide for the application", "logout_button": "Logout", "delete_account_button": "Delete Account", "logout_confirmation_title": "Logout Confirmation", "logout_confirmation_message": "Are you sure you want to logout from your account?", "logout_confirmation_button": "Logout", "delete_account_phone_required": "To delete your account, you must have a phone number set in your profile.", "delete_account_success": "Account has been deleted successfully", "delete_account_error": "Error deleting account: {error}", "delete_account_db_error": "Database configuration error. Contact support.", "delete_account_jdbc_error": "Database error. Try again later or contact support.", "admin_section": "Administrator", "admin_user_management_title": "User Management", "admin_user_management_subtitle": "Manage users and impersonate accounts", "easter_egg_activated": "🎨 Easter egg activated! Color selection is now available.", "cancel": "Cancel", "save": "Save", "continue": "Continue", "verify": "Verify", "salon_details": {"groomer_chief": "Chief <PERSON><PERSON>er", "full_access": "Full access", "tap_for_salon_options": "Tap for salon options", "clients": "Clients", "appointments": "Appointments", "open": "Open", "status": "Status"}, "service_suggestions": {"full_grooming": "Full grooming", "hygienic_grooming": "Hygienic grooming", "simple_wash": "Simple wash", "wash_and_dry": "Wash and dry", "special_shampoo_wash": "Special shampoo wash", "complete_package": "Complete package", "puppy_package": "Puppy package", "senior_package": "Senior package", "nail_trimming": "Nail trimming", "fur_descaling": "Fur descaling", "intensive_brushing": "Intensive brushing", "ear_cleaning": "Ear cleaning", "eye_cleaning": "Eye cleaning", "white_fur_treatment": "White fur treatment", "carding": "Carding", "antiparasitic_treatment": "Antiparasitic treatment", "special_styling": "Special styling", "facial_hair_trimming": "Facial hair trimming", "dental_scaling": "Dental scaling", "special_perfume": "Special perfume"}}, "sms_followup_timing": {"title": "Follow-up <PERSON><PERSON>", "added_success": "Follow-up timing added successfully", "add_error": "Error adding timing", "update_error": "Error updating timing: {error}", "delete_confirmation_title": "Confirm deletion", "delete_confirmation_message": "Are you sure you want to delete timing \"{timing}\"?", "deleted_success": "Follow-up timing deleted successfully", "delete_error": "Error deleting timing: {error}"}, "sms_reminders_add_button": "Add", "sms_reminders_appointment_confirmations": "Appointment confirmations", "sms_reminders_appointment_confirmations_description": "Send confirmation SMS when booking", "sms_reminders_appointment_confirmations_subtitle": "Send SMS confirmation for booking, rescheduling and cancellation", "sms_reminders_appointment_confirmations_title": "Appointment confirmations", "sms_reminders_appointment_settings": "Appointment Settings", "sms_reminders_auto_finalize": "Auto-finalize appointments when time passes", "sms_reminders_auto_finalize_subtitle": "Appointments will be automatically marked as completed when end time passes", "sms_reminders_auto_sms": "Automatic SMS", "sms_reminders_auto_sms_description": "Enable automatic message sending", "sms_reminders_auto_sms_subtitle": "Enable automatic SMS sending", "sms_reminders_auto_sms_title": "Automatic SMS", "sms_reminders_available_soon": "Available soon", "sms_reminders_back_button": "Back", "sms_reminders_cancellation_notifications_description": "Send SMS when an appointment is cancelled", "sms_reminders_cancellation_notifications_title": "Cancellation notifications", "sms_reminders_channel_sms_description": "Only for romanian numbers", "sms_reminders_channel_update_error": "Error updating channel", "sms_reminders_channel_update_error_details": "Error updating channel: {error}", "sms_reminders_channel_updated": "Channel updated to {channel}", "sms_reminders_channel_whatsapp_description": "WhatsApp messages", "sms_reminders_close_button": "Close", "sms_reminders_coming_soon": "Coming soon", "sms_reminders_coming_soon_badge": "Soon", "sms_reminders_completion_messages": "Completion messages", "sms_reminders_completion_messages_subtitle": "Send SMS after service completion", "sms_reminders_delete_reminder_tooltip": "Delete reminder", "sms_reminders_description_12_hours_before": "12 hours before", "sms_reminders_description_1_day_before": "1 day before", "sms_reminders_description_1_hour_before": "1 hour before", "sms_reminders_description_2_days_before": "2 days before", "sms_reminders_description_2_hours_before": "2 hours before", "sms_reminders_description_3_days_before": "3 days before", "sms_reminders_description_6_hours_before": "6 hours before", "sms_reminders_description_hours_before": "{hours} hours before", "sms_reminders_edit_templates": "Edit", "sms_reminders_edit_templates_button": "Edit Templates", "sms_reminders_empty_state": "No reminders configured", "sms_reminders_empty_state_subtitle": "Add the first reminder to notify clients", "sms_reminders_error_loading_title": "Error loading SMS settings", "sms_reminders_error_unknown": "Unknown error", "sms_reminders_followup_messages": "Follow-up messages", "sms_reminders_followup_messages_subtitle": "Send SMS feedback", "sms_reminders_followup_timing_12_hours_after": "12 hours after completion", "sms_reminders_followup_timing_1_hour_after": "1 hour after completion", "sms_reminders_followup_timing_24_hours_after": "24 hours after completion", "sms_reminders_followup_timing_2_hours_after": "2 hours after completion", "sms_reminders_followup_timing_48_hours_after": "48 hours after completion", "sms_reminders_followup_timing_6_hours_after": "6 hours after completion", "sms_reminders_followup_timing_72_hours_after": "72 hours after completion", "sms_reminders_followup_timing_create_error": "Error creating timing", "sms_reminders_followup_timing_created": "Follow-up timing created at {timing}", "sms_reminders_followup_timing_hours_after": "{hours} hours after completion", "sms_reminders_followup_timing_immediately": "immediately after completion", "sms_reminders_followup_timing_title": "Follow-up Timing", "sms_reminders_followup_timing_update_error": "Error updating timing", "sms_reminders_followup_timing_update_error_details": "Error updating timing: {error}", "sms_reminders_followup_timing_updated": "Follow-up timing updated to {timing}", "sms_reminders_followup_timing_when": "When to send feedback message", "sms_reminders_messaging_channel_subtitle": "Choose how to send messages", "sms_reminders_messaging_channel_title": "Communication Channel", "sms_reminders_no_permission_message": "Only chief groomers can configure SMS settings for the salon.", "sms_reminders_no_permission_title": "Restricted access", "sms_reminders_option_12_hours_before": "12 hours before", "sms_reminders_option_1_day_before": "1 day before", "sms_reminders_option_1_hour_before": "1 hour before", "sms_reminders_option_2_days_before": "2 days before", "sms_reminders_option_2_hours_before": "2 hours before", "sms_reminders_option_3_days_before": "3 days before", "sms_reminders_option_6_hours_before": "6 hours before", "sms_reminders_overdue_disabled_reason": "Disabled because auto-finalization is enabled", "sms_reminders_overdue_notifications": "Notification for unfinalized appointment when time passes", "sms_reminders_overdue_notifications_subtitle": "Receive notification for appointments that haven't been manually finalized", "sms_reminders_refresh_data_error": "Error refreshing data", "sms_reminders_remaining": "SMS remaining: {count}", "sms_reminders_reminder_added": "<PERSON><PERSON><PERSON> added successfully", "sms_reminders_reminder_deleted": "<PERSON><PERSON><PERSON> deleted successfully", "sms_reminders_reminder_error": "Error updating reminder", "sms_reminders_reminder_restore_error": "Could not restore reminder", "sms_reminders_reminder_section_subtitle": "When to send reminders before appointments", "sms_reminders_reminder_section_title": "SMS Reminders", "sms_reminders_reminder_undo": "Undo", "sms_reminders_reminder_updated": "Reminder updated successfully", "sms_reminders_reschedule_notifications_description": "Send SMS when an appointment is rescheduled", "sms_reminders_reschedule_notifications_title": "Reschedule notifications", "sms_reminders_salon_not_found": "Could not find salon", "sms_reminders_salon_not_identified": "Could not identify salon", "sms_reminders_save_settings_button": "Save settings", "sms_reminders_screen_title": "SMS Reminders", "sms_reminders_see_details": "See details", "sms_reminders_send_after": "Send after", "sms_reminders_settings_auto_finalize_success": "Auto-finalization setting has been updated", "sms_reminders_settings_notification_success": "Notification setting has been updated", "sms_reminders_settings_title": "SMS Notification Settings", "sms_reminders_settings_update_error": "Error updating SMS settings", "sms_reminders_settings_update_error_details": "Error updating settings: {error}", "sms_reminders_settings_updated_success": "SMS settings have been updated successfully!", "sms_reminders_sms_history_empty_subtitle": "Sent SMS will appear here", "sms_reminders_sms_history_empty_title": "No SMS sent yet", "sms_reminders_sms_history_title": "SMS History", "sms_reminders_subscription_active": "SMS subscription active", "sms_reminders_subscription_auto_billing": "Automatic billing", "sms_reminders_subscription_cancel": "Cancel", "sms_reminders_subscription_cancel_anytime": "Cancel anytime", "sms_reminders_subscription_current_month": "SMS sent this month", "sms_reminders_subscription_details_usage": "Usage details", "sms_reminders_subscription_estimated_cost": "Estimated cost", "sms_reminders_subscription_more_details": "More details", "sms_reminders_subscription_no_monthly_fees": "No fixed monthly fees", "sms_reminders_subscription_pay_only": "Pay only for SMS sent", "sms_reminders_subscription_price_per_sms": "0.05€/SMS (VAT included)", "sms_reminders_subscription_redirect_success": "Redirected to payment page. Return to app after completion", "sms_reminders_subscription_setup": "Subscribe for unlimited SMS", "sms_reminders_subscription_stripe_redirect": "You will be redirected to <PERSON><PERSON>'s secure payment page", "sms_reminders_subscription_stripe_warning": "This action will take you outside the app to Strip<PERSON>'s website", "sms_reminders_subscription_subscribe_now": "Subscribe now", "sms_reminders_subscription_title": "SMS Subscription", "sms_reminders_subscription_upgrade_message": "Upgrade to access SMS subscription functionality", "sms_reminders_subscription_usage": "SMS Usage", "sms_reminders_success_title": "Success", "sms_reminders_timing_12_hours": "12 hours after completion", "sms_reminders_timing_1_hour": "1 hour after completion", "sms_reminders_timing_24_hours": "24 hours after completion", "sms_reminders_timing_2_hours": "2 hours after completion", "sms_reminders_timing_48_hours": "48 hours after completion", "sms_reminders_timing_6_hours": "6 hours after completion", "sms_reminders_timing_72_hours": "72 hours after completion", "sms_reminders_timing_immediate": "Immediately after completion", "sms_reminders_try_again_button": "Try again", "sms_template": {"sms_title": "Select SMS Template", "whatsapp_title": "Select WhatsApp Template", "load_templates_error": "Could not load templates", "load_error_with_details": "Error loading templates: {error}", "retry_button": "Retry", "no_templates_available": "No templates available", "preview_message": "Message preview:", "send_without_template": "Without template", "send_button": "Send", "default_message_greeting": "Hello {clientName}, ", "sms_app_name": "messaging app", "whatsapp_app_name": "WhatsApp", "default_salon_name": "Your Salon", "default_owner_name": "Owner", "default_salon_address": "Salon address", "default_salon_phone": "Salon phone", "default_service_name": "Service"}, "sms_templates": {"title": "SMS Templates", "appointment_confirmation": "Appointment Confirmation", "appointment_cancellation": "Appointment Cancellation", "appointment_reschedule": "Appointment Reschedule", "reminder": "Reminder", "appointment_completion": "Appointment Completion", "follow_up": "Follow-up", "friendly_tone": "Friendly tone", "friendly_tone_desc": "Friendly and relaxed tone", "professional_tone": "Professional tone", "professional_tone_desc": "Professional and formal tone", "with_emoji": "With emoji", "with_emoji_desc": "Cheerful message with emoji", "simple": "Simple", "simple_desc": "Short and direct message", "with_apology": "With apology", "with_apology_desc": "Empathetic tone with apology", "short_positive_desc": "Short and positive message", "short": "Short", "friendly_short_desc": "Friendly and short reminder", "urgent": "<PERSON><PERSON>", "urgent_desc": "Reminder with visual urgency", "formal": "Formal", "formal_reminder_desc": "Formal and professional reminder", "thank_you": "Thank you", "thank_you_desc": "Warm thank you message", "professional": "Professional", "formal_completion_desc": "Formal completion message", "with_review": "With review", "with_review_desc": "Message with feedback request", "care": "Care", "care_desc": "Follow-up with care for pet", "review": "Review", "review_request_desc": "Follow-up with review request", "simple_followup_desc": "Simple and positive follow-up", "confirmation_friendly_content": "{SALON_NAME}: Hi {OWNER_NAME}! The appointment for {PET_NAME} is confirmed on {APPOINTMENT_DATE} at {APPOINTMENT_TIME}. We're waiting for you! 🐾", "confirmation_professional_content": "{SALON_NAME}: Good day {OWNER_NAME}! The appointment for {PET_NAME} is confirmed on {APPOINTMENT_DATE} at {APPOINTMENT_TIME}. We're waiting for you!", "confirmation_emoji_content": "🐾 {SALON_NAME}: Hi {OWNER_NAME}! {PET_NAME} is expected on {APPOINTMENT_DATE} at {APPOINTMENT_TIME}. We're excited to see you! ✨", "cancellation_simple_content": "{SALON_NAME}: Hi {OWNER_NAME}! The appointment for {PET_NAME} on {APPOINTMENT_DATE} has been cancelled. For rescheduling: {SALON_PHONE}", "cancellation_apology_content": "{SALON_NAME}: We're sorry {OWNER_NAME}! The appointment for {PET_NAME} on {APPOINTMENT_DATE} has been cancelled. Shall we reschedule? {SALON_PHONE}", "reschedule_simple_content": "{SALON_NAME}: Hi {OWNER_NAME}! The appointment for {PET_NAME} has been moved to {APPOINTMENT_DATE} at {APPOINTMENT_TIME}. We're waiting for you! 🐾", "reschedule_formal_content": "{SALON_NAME}: Good day {OWNER_NAME}! The appointment for {PET_NAME} has been rescheduled to {APPOINTMENT_DATE} at {APPOINTMENT_TIME}.", "reminder_short_content": "{SALON_NAME}: Hi {OWNER_NAME}! We're expecting {PET_NAME} at {APPOINTMENT_TIME}. We're excited to see you! 🐾", "reminder_urgent_content": "⏰ {SALON_NAME}: Reminder {OWNER_NAME}! {PET_NAME} has an appointment at {APPOINTMENT_TIME}. We're waiting for you!", "reminder_formal_content": "{SALON_NAME}: Good day {OWNER_NAME}! We remind you of the appointment for {PET_NAME} at {APPOINTMENT_TIME}.", "completion_thank_you_content": "{SALON_NAME}: Thank you {OWNER_NAME}! {PET_NAME} was great today. We hope to see you again soon! 🐾", "completion_professional_content": "{SALON_NAME}: Good day {OWNER_NAME}! The appointment for {PET_NAME} has been completed successfully. Thank you!", "completion_review_content": "{SALON_NAME}: Thank you {OWNER_NAME}! If {PET_NAME} felt good, we would appreciate a review. Info: {SALON_PHONE}", "followup_care_content": "{SALON_NAME}: Hi {OWNER_NAME}! How is {PET_NAME} feeling after the visit? Thank you for choosing us! For questions: {SALON_PHONE}", "followup_review_content": "{SALON_NAME}: Thank you {OWNER_NAME}! If {PET_NAME} felt good, we would appreciate a review on Google. 🌟", "followup_simple_content": "{SALON_NAME}: Thank you for the visit {OWNER_NAME}! We hope {PET_NAME} is happy. See you soon! 🐾", "reset_all_templates": "Reset all templates", "help": "SMS Templates Help", "help_description": "Customize SMS messages sent automatically to clients.", "available_variables": "Available variables:", "close": "Close", "restricted_access": "Restricted Access", "no_permission_message": "You don't have permission to manage SMS templates. Only head groomers can modify these settings.", "error_loading": "Error loading templates", "unknown_error": "Unknown error", "try_again": "Try again", "reset_confirmation_title": "Reset templates", "reset_confirmation_message": "Are you sure you want to reset all templates to default values?", "cancel": "Cancel", "reset": "Reset", "reset_success": "Templates have been reset successfully!", "reset_error": "Error resetting templates", "active": "Active", "inactive": "Inactive", "characters": "characters", "sms_count": "SMS", "editor": {"edit_title": "Edit", "professional_templates": "Professional templates", "available_variables": "Available variables", "reset_to_default": "Reset to default", "content_title": "Template content", "usage_instructions": "Use variables to personalize messages. Drag variables into editor or tap them.", "frequent_variables": "Frequent variables (drag into editor):", "drag_hint": "Enter template content or drag variables here...", "drag_active_hint": "Release here to insert variable", "preview_title": "Preview", "preview_description": "How the message will look for client:", "preview_placeholder": "Enter content for preview...", "hide": "<PERSON>de", "show": "Show", "cancel": "Cancel", "save": "Save", "reset_confirmation_title": "Reset template", "reset_confirmation_message": "Are you sure you want to reset the template to default value?", "reset": "Reset", "template_saved_title": "Template saved", "template_saved_message": "Template has been saved successfully!", "save_error_title": "Save error", "save_error_message": "Error saving template", "professional_templates_description": "Choose a professional template organized by SMS cost:", "all_variables_description": "All available variables (drag into editor or tap):", "basic_info": "Basic information", "client_pet_info": "Client and pet", "appointment_info": "Appointment", "economical_templates": "💚 Economical (1 SMS)", "moderate_templates": "🧡 Moderate (2 SMS)", "complete_templates": "❤️ Complete"}}, "sms_usage": {"loading": "Loading SMS usage...", "free_trial": "SMS Free Trial", "title": "SMS Usage", "trial_badge": "TRIAL", "usage_count": "{used} / {total} SMS", "remaining": "{count} SMS remaining", "plan": "Plan: {tier}", "upgrade_button": "Upgrade to Continue Sending SMS", "quota": "cote", "used": "used", "instant_subscription": "Instant Subscription", "send_unlimited": "Send Unlimited", "pricing": "Pricing"}, "staff_detail_screen": {"info_tab": "Info", "schedule_tab": "Schedule", "contact_info_title": "Contact Information", "name_label": "Name", "nickname_label": "Nickname", "phone_label": "Phone", "email_label": "Email", "status_label": "Status", "status_active": "Active", "status_inactive": "Inactive", "member_since_label": "Member since", "permissions_title": "Permissions and Role", "team_role_label": "Team Role", "client_data_access_label": "Client Data Access", "specialties_title": "Specialties", "notes_title": "Notes", "stats_title": "Statistics", "experience_label": "Experience", "experience_years": "{count} years", "rating_label": "Rating", "appointments_label": "Appointments", "management_title": "Team Member Management", "chief_groomer_restriction": "Chief <PERSON><PERSON><PERSON> cannot be edited. For changes, contact the administrator.", "edit_member_button": "Edit member", "deactivate_button": "Deactivate", "activate_button": "Activate", "delete_member_button": "Delete member from team", "delete_confirmation_title": "Delete member", "delete_confirmation_message": "Are you sure you want to delete {name} from the team?", "delete_confirmation_warning": "This action cannot be undone. The member will be permanently removed from the team.", "cancel_button": "Cancel", "delete_button": "Delete", "status_changed_success": "{name} has been {status} successfully", "status_activated": "activated", "status_deactivated": "deactivated", "status_change_error": "Could not change status", "member_deleted_success": "Member deleted", "member_deleted_message": "{name} has been removed from the team successfully", "delete_error_title": "Delete error", "delete_error_message": "Could not delete member from team", "delete_error_unexpected": "An unexpected error occurred: {error}", "try_again_button": "Try again", "data_updated_success": "Data has been updated successfully", "schedule_loading_error": "Error loading schedule", "schedule_try_again": "Try again", "schedule_title": "Schedule {name}", "template_applied_success": "Template applied!", "template_applied_message": "The schedule template has been applied successfully", "template_error_title": "Template error", "template_error_message": "Could not apply schedule template", "template_apply_error": "Error applying template: {error}", "schedule_updated_success": "Schedule has been updated successfully!", "schedule_update_error": "Error updating schedule", "schedule_update_error_detail": "Error updating schedule: {error}", "closure_added_success": "Custom closure has been added successfully!", "closure_add_error": "Error adding closure", "closure_add_error_detail": "Error adding closure: {error}", "closure_deleted_success": "Closure has been deleted successfully!", "closure_delete_error": "Error deleting closure", "closure_delete_error_detail": "Error deleting closure: {error}"}, "staff_performance_report": {"title": "Staff Performance", "export_excel_tooltip": "Export Excel", "demo_button": "DEMO", "real_button": "REAL", "select_team_member": "Select team member", "no_team_members": "No team members", "loading_team": "Loading team...", "performance_title": "Performance {name}", "total_appointments": "Total Appointments", "total_revenue": "Total Revenue", "success_rate": "Success Rate: {percentage}%", "completed_out_of_total": "{completed} completed out of {total} appointments", "service_distribution_title": "Service Distribution", "revenue_evolution_title": "Revenue Evolution", "service_axis_label": "Service", "number_axis_label": "Number", "date_axis_label": "Date", "revenue_axis_label": "Revenue (RON)", "export_chart_tooltip": "Export chart", "completed_appointments": "Completed appointments", "cancelled_appointments": "Cancelled appointments", "average_revenue_per_appointment": "Average revenue/appointment", "empty_state_title": "No data available", "empty_state_subtitle": "Select a different member or enable sample data", "generating_report": "Generating report...", "excel_sheet_title": "Staff Performance", "excel_report_title": "Staff Performance Report", "excel_staff_label": "Staff: {name}", "excel_total_appointments": "Total appointments: {count}", "excel_service_header": "Service", "excel_number_header": "Number", "excel_filename": "staff_report_{name}.xlsx", "excel_share_text": "Staff report generated from Animalia app", "report_downloaded_success": "Report downloaded successfully!", "error_loading_team": "Error loading team: {error}", "error_loading_data": "Error loading data: {error}", "export_chart_coming_soon": "Export {type} chart - coming soon!"}, "staff_schedule_screen": {"title": "Staff Schedule", "error": "Error: {error}", "no_active_members": "No active members"}, "staff_schedule_templates": {"standard_groomer_name": "Standard groomer schedule", "standard_groomer_desc": "Mon-Fri 9:00-17:00\nSat 10:00-15:00", "part_time_name": "Part-time schedule", "part_time_desc": "Mon, Wed, Fri\n10:00-16:00", "weekend_name": "Weekend schedule", "weekend_desc": "Sat-Sun\n9:00-18:00", "flexible_name": "Flexible schedule", "flexible_desc": "<PERSON><PERSON><PERSON><PERSON>t\n10:00-19:00", "assistant_name": "Assistant schedule", "assistant_desc": "Mon-Fri 8:00-16:00\nSupport for groomers", "nonstop_name": "Non-stop schedule", "nonstop_desc": "Open 24/7 permanently"}, "subscription": {"view_plans": "View Plans", "first_appointment": {"title": "Congrats on your first appointment!", "subtitle": "🎉 You've discovered how easy it is to manage appointments!", "upgrade_title": "Upgrade to unlock advanced features:", "features": {"sms": "• Automated SMS for clients", "team": "• Team management", "reports": "• Detailed reports", "support": "• Priority support"}}, "staff_limit": {"title": "Your team is growing!", "subtitle": "👥 To add more groomers to your team, upgrade to a higher plan.", "upgrade_title": "Upgrade benefits:", "features": {"limit": "• Up to 5 groomers (Silver) or unlimited (Gold)", "team_management": "• Advanced team management", "reports_per_groomer": "• Reports per groomer", "parallel_appointments": "• Parallel appointments"}}, "sms_quota": {"title": "SMS quota reached", "subtitle": "📱 You have used all SMS for this month.", "upgrade_title": "Upgrade for more messages:", "features": {"sms_200": "• 200 SMS/month (Silver)", "sms_500": "• 500 SMS/month (Gold)", "auto_confirm": "• Automated confirmation SMS", "reminders": "• Custom reminders"}}, "milestone": {"title": "You're on the right track!", "completed_appointments": "🚀 You have completed {count} appointments!", "upgrade_title": "Upgrade for automated SMS and more features:", "features": {"auto_confirm": "• Automated confirmations", "reminders": "• Custom reminders", "advanced_reports": "• Advanced reports", "priority_support": "• Priority support"}}}, "subscription_indicator": {"could_not_load_details": "Could not load subscription details"}, "subscription_plans": {"title": "Choose Your Plan", "loading_plans": "Loading plans...", "error_loading_plans": "Error loading plans", "try_again": "Try Again", "billing_monthly": "Monthly", "billing_annual": "Annual (2 months free)", "per_year": "year", "per_month": "month", "savings_message": "Save {amount} RON/month • 2 months free!", "active_plan": "Your active plan", "current_plan_badge": "ACTIVE", "popular_badge": "POPULAR", "current_plan_button": "✨ Your active plan ✨", "current_plan_subtitle": "Your active plan", "current_plan_message": "Thank you for being with us!", "trial_button": "Start free trial period", "upgrade_button": "Upgrade to {tier}", "downgrade_button": "Switch to {tier}", "free_plan_title": "Free plan is always available!", "free_plan_subtitle": "Start using the app with basic features.", "benefit_staff_unlimited": "Unlimited team", "benefit_staff_single": "1 groomer", "benefit_staff_count": "{count} groomers", "benefit_sms_none": "No SMS", "benefit_sms_count": "{count} SMS/month", "benefit_clients_unlimited": "Unlimited clients", "benefit_salons_multiple": "Multiple salons", "tier_features_free": "What you get for free:", "tier_features_freelancer": "Perfect for independent groomers:", "tier_features_team": "Everything from Freelancer, plus team benefits:", "tier_features_enterprise": "Everything from Team, plus advanced business features:", "purchase_success_title": "Subscription Activated", "purchase_success_message": "The {tier} subscription has been activated successfully!", "purchase_error_title": "Purchase Error", "purchase_error_message": "Could not activate subscription. Check your connection and try again.", "purchase_cancelled_title": "Purchase Cancelled", "purchase_cancelled_message": "The subscription purchase was cancelled by the user. You can try again anytime to activate premium features or continue using the app with the current available plan.", "purchase_cancelled_button": "I Understand", "restore_purchases": "<PERSON><PERSON> Purchases", "restore_success_title": "Purchases Restored", "restore_success_message": "The {tier} subscription has been restored successfully!", "restore_no_purchases_title": "Information", "restore_no_purchases_message": "No purchases found to restore for this account.", "restore_error_title": "<PERSON><PERSON>", "restore_error_message": "Could not restore purchases. Check your connection and try again.", "privacy_policy": "Privacy Policy", "terms_of_service": "Terms of Service", "terms_of_use_eula": "Terms of Use (EULA)", "stripe_redirect_title": "Redirecting to Payment", "stripe_redirect_message": "Redirecting you to the secure Stripe payment page...", "stripe_error_title": "Opening Error", "stripe_error_message": "Could not open payment page. Please try again.", "cancel_subscription": "Cancel Subscription", "cancel_success_title": "Subscription Cancelled", "cancel_success_message": "The subscription has been cancelled successfully. You will have access to premium features until the end of the billing period.", "cancel_error_title": "Cancellation Error", "cancel_error_message": "If you purchased the subscription on web, it must be cancelled from the email received from <PERSON>e. For mobile subscriptions, please try again from mobile.", "cancel_keep_button": "No, keep subscription", "cancel_confirm_button": "Yes, cancel", "package_not_available": "Selected package is not available", "unexpected_error_title": "Unexpected Error", "unexpected_error_message": "An unexpected error occurred. Please try again.", "stripe_initiate_error": "Could not initiate purchase. Check your connection and try again.", "error_network": "Check your internet connection and try again.", "error_payment_pending": "Payment is pending. You will be notified when it's processed.", "error_invalid_credentials": "Apple ID credentials are not valid.", "error_product_not_available": "Product is not available in your region.", "error_insufficient_permissions": "You don't have the necessary permissions for this purchase.", "error_generic": "An unexpected error occurred. Please try again.", "cancel_platform_specific_error": "If you purchased the subscription on web, it must be cancelled from the email received from <PERSON>e. For mobile subscriptions, please try again from mobile.", "cancel_subscription_title": "Cancel Subscription", "cancel_confirmation_message": "Are you sure you want to cancel the {tier} subscription?", "cancel_what_happens": "What will happen:", "cancel_immediate": "• Subscription will be cancelled immediately", "cancel_access_until_end": "• You will have access to premium features until the end of the billing period", "cancel_no_future_charges": "• You will not be charged in the future", "cancel_reactivate_anytime": "• You can reactivate the subscription anytime", "tier_subtitle_free": "Basic features", "tier_subtitle_freelancer": "For independent groomers", "tier_subtitle_team": "For small teams", "tier_subtitle_enterprise": "For multiple salons and large teams", "cancel_confirmation_info": "What will happen:", "cancel_info_immediate": "• Subscription will be cancelled immediately", "cancel_info_access": "• You will have access to premium features until the end of the billing period", "cancel_info_billing": "• You will not be charged in the future", "cancel_info_reactivate": "• You can reactivate the subscription anytime", "features": {"quick_booking": "Quick booking in just 3 clicks", "drag_drop_reschedule": "Reschedule with drag & drop", "direct_communication": "Direct communication with clients (phone, WhatsApp)", "mobile_app": "Mobile app", "synced_calendar": "Synced calendar", "max_one_groomer": "Maximum 1 groomer", "no_sms": "No SMS messages", "unlimited_clients": "Unlimited clients", "automated_sms_100": "Automated SMS messages (100 free)", "max_two_groomers": "Maximum 2 groomers in team", "email_support": "Technical support via email", "automated_sms_200": "Automated SMS messages (200 free)", "max_ten_groomers": "Maximum 10 groomers in team", "recurring_appointments": "Recurring appointments", "team_management": "Team management", "performance_reports": "Performance reports", "unlimited_groomers": "Unlimited groomers in team", "multiple_salons": "Create multiple salons", "multi_location_management": "Multi-location management", "automated_sms_500": "Automated SMS messages (500/month)", "custom_branding": "Custom branding", "priority_support": "Priority support + phone", "personal_training": "Personal training sessions", "auto_backup": "Automatic data backup", "early_access": "Priority access to new features"}}, "subscriptions": {"subscriptions": "Subscriptions", "service_subscriptions": "Service Subscriptions", "recurring_appointments": "Recurring Appointments", "no_subscriptions": "No subscriptions", "salon_info_error": "Could not load salon information", "sessions": "Sessions", "no_recurring_subscriptions": "No recurring subscriptions", "recurring_subscriptions_description": "Recurring subscriptions will appear here when the client creates recurring appointments.", "error_loading_subscriptions": "Error loading subscriptions", "retry": "Retry", "appointments_completed": "{completed}/{total} appointments completed", "remaining": "{count} remaining", "subscription_appointments": "Subscription appointments", "no_appointments_for_subscription": "No appointments for this subscription", "view_all_appointments": "View all appointments ({count})", "today": "Today"}, "team_management": {"title": "Team Management", "help_tooltip": "Here you can invite groomers and manage the team. Requires Team or Enterprise for more members.", "help_tooltip_short": "Help", "error_loading_team_title": "Error loading team", "error_loading_team_message": "Error: {error}", "try_again_button": "Try again", "no_team_members_title": "No team members", "no_team_members_message": "Add the first team member", "members_count": "{count} team members", "loading_team": "Loading team...", "staff_card_active_status": "Active", "staff_card_inactive_status": "Inactive", "staff_card_details_button": "Details", "staff_card_specialties_title": "Specialties:", "pending_invitations_title": "Pending invitations", "invitation_card_expires": "Expires {date}", "invitation_card_expired": "Expired", "invitation_card_invited_by": "Invited by: {name}", "invitation_card_invited_at": "Invited at: {date}", "invitation_menu_resend": "Resend invitation", "invitation_menu_cancel": "Cancel invitation", "invitation_cancel_title": "Cancel Invitation", "invitation_cancel_message": "Are you sure you want to cancel the invitation for {phone}?\n\nThis action cannot be undone.", "invitation_cancel_no_button": "No", "invitation_cancel_yes_button": "Yes, cancel", "invitation_resent_error_title": "Error resending", "invitation_resent_error_message": "Could not resend invitation", "invitation_cancelled_error_title": "Error cancelling", "invitation_cancelled_error_message": "Could not cancel invitation", "invitation_cancelled_success": "Invitation for {phone} has been cancelled successfully", "unexpected_error_title": "Unexpected error", "unexpected_error_message": "An unexpected problem occurred", "upgrade_for_team_title": "Upgrade for Team", "upgrade_for_team_message": "Free plan allows only 1 team member. Upgrade to Freelancer for up to 2 members and advanced features.", "extend_team_title": "Extend Your Team", "extend_team_message": "Freelancer plan allows only 2 team members. Upgrade to Enterprise for unlimited members and all premium features.", "unlimited_team_title": "Unlimited Team", "unlimited_team_message": "Team plan allows maximum 10 team members. Upgrade to Enterprise for unlimited members and all premium features.", "limit_reached_title": "Limit Reached", "limit_reached_message": "Cannot add more team members with current plan.", "invitation_resent_success": "Invitation for {phone} has been resent successfully", "date_format_months": ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"]}, "time": {"today": "Today", "yesterday": "Yesterday", "days_ago": "{count} days ago", "weeks_ago": "{count} weeks ago"}, "usage_dashboard": {"staff_label": "staff", "pets_label": "pets"}, "user_roles": {"groomer_role_chief_groomer": "Chief <PERSON><PERSON>er", "groomer_role_groomer": "<PERSON><PERSON><PERSON>", "groomer_role_assistant": "Assistant", "groomer_role_chief_description": "Can manage team and salon appointments", "groomer_role_groomer_description": "Can manage own appointments", "groomer_role_assistant_description": "Assists with grooming services", "client_permission_no_access": "No access", "client_permission_limited_access": "Limited access", "client_permission_full_access": "Full access", "client_permission_no_access_description": "Can only see appointment times", "client_permission_limited_description": "Can see client name and phone", "client_permission_full_description": "Can see all client information"}, "chat": {"title": "Cha<PERSON>", "conversation_with": "Conversation with {name}", "refresh_tooltip": "Refresh", "loading_messages": "Loading messages...", "error_loading_messages": "Error loading messages", "error_loading_messages_details": "Error loading messages: {error}", "no_messages": "No conversation yet", "no_messages_description": "Messages sent to this client will appear here", "message_not_associated": "This message is not associated with an appointment", "appointment_not_found": "Appointment not found", "error_loading_appointment": "Error loading appointment: {error}", "today": "Today", "yesterday": "Yesterday", "incoming_messages_coming_soon": "Incoming messages will be available soon"}, "smsHistory": "SMS History"}