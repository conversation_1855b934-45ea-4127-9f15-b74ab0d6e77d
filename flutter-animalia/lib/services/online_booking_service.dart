import '../models/api_response.dart';
import '../models/online_booking.dart';
import '../utils/debug_logger.dart';
import 'api_service.dart';

/// Service for online booking functionality (public endpoints)
class OnlineBookingService {
  /// Get available time slots for a specific date and services
  ///
  /// [salonId] - The salon ID
  /// [date] - Date in yyyy-MM-dd format
  /// [serviceIds] - List of service IDs to book
  static Future<ApiResponse<AvailableTimeSlotsResponse>> getAvailableTimeSlots({
    required String salonId,
    required String date,
    required List<String> serviceIds,
  }) async {
    try {
      DebugLogger.logApi('🗓️ OnlineBookingService: Getting available slots for $date');

      // Build query parameters
      final queryParams = {
        'date': date,
        'serviceIds': serviceIds.join(','),
      };

      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/public/salons/$salonId/online-booking/available-slots',
        queryParams: queryParams,
        fromJson: (data) => data as Map<String, dynamic>,
        requiresAuth: false,
      );

      if (response.success && response.data != null) {
        final slotsResponse = AvailableTimeSlotsResponse.fromJson(response.data!);
        DebugLogger.logApi('✅ OnlineBookingService: Retrieved ${slotsResponse.slots.length} slots');
        return ApiResponse.success(slotsResponse);
      } else {
        DebugLogger.logError('❌ OnlineBookingService: API error - ${response.error}');
        return ApiResponse.error(response.error ?? 'Failed to load available slots');
      }
    } catch (e) {
      DebugLogger.logError('❌ OnlineBookingService: Exception - $e');
      return ApiResponse.error('Error loading available slots: $e');
    }
  }

  /// Create a proto-appointment to reserve a time slot temporarily
  ///
  /// [salonId] - The salon ID
  /// [request] - Proto-appointment creation request with date, time, and services
  static Future<ApiResponse<ProtoAppointmentResponse>> createProtoAppointment({
    required String salonId,
    required CreateProtoAppointmentRequest request,
  }) async {
    try {
      DebugLogger.logApi('📌 OnlineBookingService: Creating proto-appointment for ${request.date} ${request.startTime}');

      final response = await ApiService.post<Map<String, dynamic>>(
        '/api/public/salons/$salonId/online-booking/proto-appointment',
        body: request.toJson(),
        fromJson: (data) => data as Map<String, dynamic>,
        requiresAuth: false,
      );

      if (response.success && response.data != null) {
        final protoAppointmentResponse = ProtoAppointmentResponse.fromJson(response.data!);
        DebugLogger.logApi('✅ OnlineBookingService: Proto-appointment created - ${protoAppointmentResponse.appointmentId}');
        return ApiResponse.success(protoAppointmentResponse);
      } else {
        DebugLogger.logError('❌ OnlineBookingService: API error - ${response.error}');
        return ApiResponse.error(response.error ?? 'Failed to create proto-appointment');
      }
    } catch (e) {
      DebugLogger.logError('❌ OnlineBookingService: Exception - $e');
      return ApiResponse.error('Error creating proto-appointment: $e');
    }
  }

  /// Finalize the booking with client and pet information
  ///
  /// [salonId] - The salon ID
  /// [request] - Booking finalization request with client/pet details
  static Future<ApiResponse<BookingResponse>> finalizeBooking({
    required String salonId,
    required FinalizeBookingRequest request,
  }) async {
    try {
      DebugLogger.logApi('✅ OnlineBookingService: Finalizing booking for proto-appointment ${request.appointmentId}');

      final response = await ApiService.post<Map<String, dynamic>>(
        '/api/public/salons/$salonId/online-booking/finalize',
        body: request.toJson(),
        fromJson: (data) => data as Map<String, dynamic>,
        requiresAuth: false,
      );

      if (response.success && response.data != null) {
        final bookingResponse = BookingResponse.fromJson(response.data!);
        DebugLogger.logApi('✅ OnlineBookingService: Booking finalized - ${bookingResponse.appointmentId}');
        return ApiResponse.success(bookingResponse);
      } else {
        DebugLogger.logError('❌ OnlineBookingService: API error - ${response.error}');
        return ApiResponse.error(response.error ?? 'Failed to finalize booking');
      }
    } catch (e) {
      DebugLogger.logError('❌ OnlineBookingService: Exception - $e');
      return ApiResponse.error('Error finalizing booking: $e');
    }
  }
}

