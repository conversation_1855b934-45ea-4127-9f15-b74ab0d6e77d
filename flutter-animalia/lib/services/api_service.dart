import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';

import '../config/api_config.dart';
import '../models/api_response.dart';
import '../utils/logging_client.dart';
import '../utils/debug_logger.dart';

class ApiService {
  static String get baseUrl => ApiConfig.baseUrl;

  static Duration get defaultTimeout => ApiConfig.timeout;

  // HTTP client used for all requests with logging enabled
  static final http.Client _client = LoggingClient(http.Client());

  // Headers for all requests
  static Map<String, String> get _headers {
    final headers = _authToken != null
        ? ApiConfig.getAuthHeaders(_authToken!)
        : ApiConfig.defaultHeaders;

    // Debug logging for headers (excluding sensitive token details)
    if (ApiConfig.enableLogging) {
      final debugHeaders = Map<String, String>.from(headers);
      if (debugHeaders.containsKey('Authorization')) {
        final authValue = debugHeaders['Authorization']!;
        if (authValue.startsWith('Bearer ')) {
          final tokenPreview = authValue.length > 20
              ? '${authValue.substring(0, 20)}...'
              : authValue;
          debugHeaders['Authorization'] = tokenPreview;
        }
      }
    }

    return headers;
  }

  static String? _authToken;

  // Set authentication token
  static void setAuthToken(String? token) {
    _authToken = token;
    if (ApiConfig.enableLogging) {
      if (token != null) {
        final tokenPreview = token;
        DebugLogger.logVerbose('🔑 Auth token set: $tokenPreview');
      } else {
        DebugLogger.logVerbose('🔑 Auth token cleared');
      }
    }
  }

  // Get authentication token
  static String? get authToken => _authToken;

  // Get authentication token (alias for compatibility)
  static String? getAuthToken() => _authToken;

  // Clear authentication token
  static void clearAuthToken() {
    _authToken = null;
    if (ApiConfig.enableLogging) {
      DebugLogger.logVerbose('🔑 Auth token cleared');
    }
  }

  // Check if we have a valid auth token
  static bool get hasAuthToken => _authToken != null && _authToken!.isNotEmpty;

  // Public getter for headers (for testing)
  static Map<String, String> get headers => _headers;

  // Generic GET request
  static Future<ApiResponse<T>> get<T>(String endpoint, {
    Map<String, dynamic>? queryParams,
    T Function(dynamic)? fromJson,
    bool requiresAuth = true,
  }) async {
    // Check authentication if required
    if (requiresAuth && !hasAuthToken) {
      // Try to restore token from storage as a fallback
      await _restoreAuthTokenFromStorage();

      if (!hasAuthToken) {
        DebugLogger.logError('GET request requires authentication but no token available: $endpoint');
        return ApiResponse.error('Authentication required');
      }
    }

    try {
      DebugLogger.logApi('GET: $endpoint');
      final uri = Uri.parse('$baseUrl$endpoint');
      final finalUri = queryParams != null
          ? uri.replace(queryParameters: queryParams)
          : uri;

      if (ApiConfig.enableLogging) {
        DebugLogger.logApi('GET:  $finalUri');
      }

      final response = await _client.get(
        finalUri,
        headers: _headers,
      ).timeout(defaultTimeout);

      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('📥 GET Response: ${response.statusCode}');
        DebugLogger.logVerbose('📥 GET Response: ${response.body}');
      }
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      DebugLogger.logError('GET Error: $e');
      return ApiResponse.error(_getErrorMessage(e));
    }
  }

  // Public GET request (no authentication required)
  static Future<ApiResponse<T>> getPublic<T>(String endpoint, {
    Map<String, dynamic>? queryParams,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      DebugLogger.logApi('GET PUBLIC: $endpoint');
      final uri = Uri.parse('$baseUrl$endpoint');
      final finalUri = queryParams != null
          ? uri.replace(queryParameters: queryParams)
          : uri;

      if (ApiConfig.enableLogging) {
        DebugLogger.logApi('GET PUBLIC:  $finalUri');
      }

      // Use only default headers without authentication
      final publicHeaders = ApiConfig.defaultHeaders;

      final response = await _client.get(
        finalUri,
        headers: publicHeaders,
      ).timeout(defaultTimeout);

      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('📥 GET PUBLIC Response: ${response.statusCode}');
        DebugLogger.logVerbose('📥 GET PUBLIC Response: ${response.body}');
      }
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      DebugLogger.logError('GET PUBLIC Error: $e');
      return ApiResponse.error(_getErrorMessage(e));
    }
  }

  // Generic POST request
  static Future<ApiResponse<T>> post<T>(String endpoint, {
    Map<String, dynamic>? body,
    T Function(dynamic)? fromJson,
    bool requiresAuth = true,
  }) async {
    // Check authentication if required
    // Special handling for auth endpoints that require authentication
    final isAuthEndpoint = endpoint.contains('/auth/');
    final needsAuth = requiresAuth && (!isAuthEndpoint ||
        endpoint.contains('/auth/profile') ||
        endpoint.contains('/auth/phone') ||
        endpoint.contains('/auth/logout'));

    if (needsAuth && !hasAuthToken) {
      // Try to restore token from storage as a fallback
      await _restoreAuthTokenFromStorage();

      if (!hasAuthToken) {
        if (ApiConfig.enableLogging) {
          DebugLogger.logVerbose(
              '🚫 POST request requires authentication but no token available: $endpoint');
        }
        return ApiResponse.error('Authentication required');
      }
    }

    try {
      final uri = Uri.parse('$baseUrl$endpoint');

      if (ApiConfig.enableVerboseLogging) {
        DebugLogger.logVerbose('🚀 === API SERVICE POST REQUEST START ===');
        DebugLogger.logVerbose('📤 POST URI: $uri');
        DebugLogger.logVerbose('🔗 Endpoint: $endpoint');
        DebugLogger.logVerbose('🔑 Has auth token: ${hasAuthToken}');
        if (ApiConfig.enableResponseBodyLogging) {
          DebugLogger.logVerbose('🔑 Auth token length: ${_authToken?.length ?? 0}');
          DebugLogger.logVerbose('📋 Headers: $_headers');
          if (body != null) {
            // Don't log sensitive auth data
            if (endpoint.contains('/auth/')) {
              DebugLogger.logVerbose('📝 Body: [Auth request - body hidden for security]');
            } else {
              DebugLogger.logVerbose('📝 Body: ${jsonEncode(body)}');
            }
          } else {
            DebugLogger.logVerbose('📝 Body: null');
          }
        }
      }

      DebugLogger.logApi('POST: $endpoint');
      final response = await _client.post(
        uri,
        headers: _headers,
        body: body != null ? jsonEncode(body) : null,
      ).timeout(defaultTimeout);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      DebugLogger.logVerbose('💥 === API SERVICE POST REQUEST EXCEPTION ===');
      DebugLogger.logVerbose('❌ Exception: $e');
      DebugLogger.logVerbose('❌ Exception type: ${e.runtimeType}');
      DebugLogger.logVerbose('❌ Target URL: $baseUrl$endpoint');
      DebugLogger.logVerbose('❌ Timeout setting: ${ApiConfig.timeout}');
      DebugLogger.logVerbose('❌ Stack trace: ${StackTrace.current}');
      DebugLogger.logError('POST Error: $e');
      if (e.toString().contains('TimeoutException')) {
        DebugLogger.logVerbose('🔧 TIMEOUT TROUBLESHOOTING:');
        DebugLogger.logVerbose('   1. Check Android device WiFi connection');
        DebugLogger.logVerbose('   2. Verify server IP: $baseUrl');
        DebugLogger.logVerbose('   3. Test manually: curl -X POST $baseUrl$endpoint');
        DebugLogger.logVerbose('   4. Increase timeout in environment.dart');
      }
      DebugLogger.logVerbose('🏁 === API SERVICE POST REQUEST EXCEPTION END ===');
      return ApiResponse.error(_getErrorMessage(e));
    }
  }

  // Generic PUT request
  static Future<ApiResponse<T>> put<T>(String endpoint, {
    Map<String, dynamic>? body,
    T Function(dynamic)? fromJson,
    bool requiresAuth = true,
  }) async {
    // Check authentication if required
    if (requiresAuth && !hasAuthToken) {
      // Try to restore token from storage as a fallback
      await _restoreAuthTokenFromStorage();

      if (!hasAuthToken) {
        if (ApiConfig.enableLogging) {
          DebugLogger.logVerbose(
              '🚫 PUT request requires authentication but no token available: $endpoint');
        }
        return ApiResponse.error('Authentication required');
      }
    }

    try {
      final uri = Uri.parse('$baseUrl$endpoint');

      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('📤 PUT: $uri');
        if (body != null) {
          DebugLogger.logVerbose('📝 Body: ${jsonEncode(body)}');
        }
      }

      final response = await _client.put(
        uri,
        headers: _headers,
        body: body != null ? jsonEncode(body) : null,
      ).timeout(defaultTimeout);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('❌ PUT Error: $e');
      }
      return ApiResponse.error(_getErrorMessage(e));
    }
  }

  // Generic PATCH request
  static Future<ApiResponse<T>> patch<T>(String endpoint, {
    Map<String, dynamic>? body,
    T Function(dynamic)? fromJson,
    bool requiresAuth = true,
  }) async {
    // Check authentication if required
    if (requiresAuth && !hasAuthToken) {
      DebugLogger.logVerbose('🚫 === PATCH AUTH CHECK FAILED ===');
      DebugLogger.logVerbose('🚫 Endpoint: $endpoint');
      DebugLogger.logVerbose('🚫 Requires auth: $requiresAuth');
      DebugLogger.logVerbose('🚫 Has auth token: $hasAuthToken');
      DebugLogger.logVerbose('🚫 Auth token value: ${_authToken ?? 'NULL'}');
      DebugLogger.logVerbose('🚫 Auth token length: ${_authToken?.length ?? 0}');

      // Try to restore token from storage as a fallback
      DebugLogger.logVerbose('🔄 Attempting to restore auth token from storage...');
      await _restoreAuthTokenFromStorage();

      // Check again after restoration attempt
      if (!hasAuthToken) {
        if (ApiConfig.enableLogging) {
          DebugLogger.logVerbose(
              '🚫 PATCH request requires authentication but no token available: $endpoint');
        }
        return ApiResponse.error('Authentication required');
      } else {
        DebugLogger.logVerbose('✅ Auth token restored from storage successfully');
      }
    }

    try {
      final uri = Uri.parse('$baseUrl$endpoint');

      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('📤 PATCH: $uri');
        if (body != null) {
          DebugLogger.logVerbose('📝 Body: ${jsonEncode(body)}');
        }
      }

      final response = await _client.patch(
        uri,
        headers: _headers,
        body: body != null ? jsonEncode(body) : null,
      ).timeout(defaultTimeout);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('❌ PATCH Error: $e');
      }
      return ApiResponse.error(_getErrorMessage(e));
    }
  }

  // Generic DELETE request
  static Future<ApiResponse<T>> delete<T>(String endpoint, {
    Map<String, dynamic>? body,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint');

      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('DELETE: $uri');
        if (body != null) {
          DebugLogger.logVerbose('DELETE Body: $body');
        }
      }

      final response = await _client.delete(
        uri,
        headers: _headers,
        body: body != null ? jsonEncode(body) : null,
      ).timeout(defaultTimeout);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      DebugLogger.logVerbose('DELETE Error: $e');
      return ApiResponse.error(_getErrorMessage(e));
    }
  }

  // Handle HTTP response
  static ApiResponse<T> _handleResponse<T>(http.Response response,
      T Function(dynamic)? fromJson,) {
    // Only log verbose details in development mode
    if (ApiConfig.enableVerboseLogging) {
      DebugLogger.logVerbose('🔍 === API SERVICE RESPONSE HANDLING START ===');
      DebugLogger.logVerbose('📥 Response Status: ${response.statusCode}');
      if (ApiConfig.enableResponseBodyLogging) {
        DebugLogger.logVerbose('📥 Response Headers: ${response.headers}');
        DebugLogger.logVerbose('📥 Response Body: ${response.body}');
        DebugLogger.logVerbose('📥 Response Body Length: ${response.body.length}');
        DebugLogger.logVerbose('📥 Response Content-Type: ${response.headers['content-type']}');
      }
    }

    // Handle authentication errors
    if (response.statusCode == 401 || response.statusCode == 403) {
      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('🚫 Authentication error: ${response.statusCode}');
        DebugLogger.logVerbose('🔑 Current token exists: ${_authToken != null}');
        DebugLogger.logVerbose('📥 Response body: ${response.body}');
        if (_authToken != null) {
          final tokenPreview = _authToken!.length > 20
              ? '${_authToken!.substring(0, 20)}...'
              : _authToken!;
        }
      }

      // Only clear token on 401 (unauthorized), not 403 (forbidden)
      // 403 might be a permissions issue, not an invalid token
      if (response.statusCode == 401) {
        clearAuthToken();
      }

      // For 403 errors, check if the response contains specific validation errors
      // instead of generic permission errors
      if (response.statusCode == 403) {
        try {
          final Map<String, dynamic> jsonData = jsonDecode(response.body);

          // Check if this is a validation error with specific message
          if (jsonData['success'] == false &&
              jsonData['error'] != null &&
              _isValidationError(jsonData['error'].toString())) {

            if (ApiConfig.enableLogging) {
              DebugLogger.logVerbose('🔍 403 contains validation error, preserving original message: ${jsonData['error']}');
            }

            // Return the validation error instead of generic permission error
            // Use the same error handling logic as the main response processing
            return ApiResponse.error(
              jsonData['error']?.toString() ?? 'Validation error',
              statusCode: response.statusCode,
            );
          }
        } catch (e) {
          // If JSON parsing fails, fall through to generic permission error
          if (ApiConfig.enableLogging) {
            DebugLogger.logVerbose('🔍 Failed to parse 403 response body for validation errors: $e');
          }
        }
      }

      return ApiResponse.error(
        response.statusCode == 401
            ? 'Sesiunea a expirat. Vă rugăm să vă autentificați din nou.'
            : 'Nu aveți permisiunea să accesați această resursă.',
        statusCode: response.statusCode,
      );
    }

    try {
      if (ApiConfig.enableLogging) {
        // DebugLogger.logVerbose(message)('📋 Attempting to parse JSON response...');
      }

      final Map<String, dynamic> jsonData = jsonDecode(response.body);

      if (ApiConfig.enableLogging) {
        // DebugLogger.logVerbose(message)('📋 JSON parsed successfully');
        // DebugLogger.logVerbose(message)('📋 JSON keys: ${jsonData.keys.toList()}');
      }

      if (response.statusCode >= 200 && response.statusCode < 300) {
        // Success response - only log in verbose mode
        if (ApiConfig.enableVerboseLogging) {
          DebugLogger.logVerbose('✅ Success response detected');
          if (ApiConfig.enableResponseBodyLogging) {
            DebugLogger.logVerbose('📄 JSON data keys: ${jsonData.keys.toList()}');
            DebugLogger.logVerbose('📄 Has data field: ${jsonData.containsKey('data')}');
            DebugLogger.logVerbose('📄 Data value: ${jsonData['data']}');
            DebugLogger.logVerbose('📄 Message: ${jsonData['message']}');
          }
        }

        if (fromJson != null && jsonData['data'] != null) {
          if (ApiConfig.enableVerboseLogging) {
            DebugLogger.logVerbose('🔄 Using fromJson to parse data');
          }
          final data = fromJson(jsonData['data']);
          if (ApiConfig.enableVerboseLogging) {
            DebugLogger.logVerbose('✅ Data parsed successfully: $data');
            DebugLogger.logVerbose('🔍 === API SERVICE RESPONSE HANDLING END (SUCCESS WITH PARSER) ===');
          }
          return ApiResponse.success(data, message: jsonData['message']);
        } else {
          if (ApiConfig.enableVerboseLogging) {
            DebugLogger.logVerbose('📄 Returning raw data without parsing');
            DebugLogger.logVerbose('🔍 === API SERVICE RESPONSE HANDLING END (SUCCESS RAW) ===');
          }
          return ApiResponse.success(
              jsonData['data'], message: jsonData['message']);
        }
      } else {
        // Error response - handle multiple error formats
        String errorMessage = 'Unknown error';
        String? errorCode;
        Map<String, dynamic>? errorDetails;

        // Check for top-level errorCode and details (current backend format)
        if (jsonData.containsKey('errorCode')) {
          errorCode = jsonData['errorCode'];
          errorDetails = jsonData['details'];

          // Get error message from 'error' field or 'message' field
          if (jsonData['error'] is String) {
            errorMessage = jsonData['error'];
          } else if (jsonData['message'] is String) {
            errorMessage = jsonData['message'];
          }

          if (ApiConfig.enableLogging) {
            DebugLogger.logVerbose('📋 Parsed top-level error format:');
            DebugLogger.logVerbose('  - errorCode: $errorCode');
            DebugLogger.logVerbose('  - errorMessage: $errorMessage');
            DebugLogger.logVerbose('  - errorDetails keys: ${errorDetails?.keys.toList()}');
          }
        } else {
          // Handle nested error format: {error: {message: "...", code: "..."}}
          final errorData = jsonData['error'];

          if (errorData is Map<String, dynamic>) {
            errorMessage = errorData['message'] ?? 'Unknown error';
            errorCode = errorData['code'];
            errorDetails = errorData;
          } else if (errorData is String) {
            errorMessage = errorData;
          } else if (jsonData['message'] is String) {
            errorMessage = jsonData['message'];
          }

          if (ApiConfig.enableLogging) {
            DebugLogger.logVerbose('📋 Parsed nested error format:');
            DebugLogger.logVerbose('  - errorCode: $errorCode');
            DebugLogger.logVerbose('  - errorMessage: $errorMessage');
            DebugLogger.logVerbose('  - errorDetails: $errorDetails');
          }
        }

        if (ApiConfig.enableLogging) {
          DebugLogger.logVerbose('❌ API Error: $errorMessage (${response.statusCode})');
          if (ApiConfig.enableVerboseLogging) {
            DebugLogger.logVerbose('❌ Error code: $errorCode');
            DebugLogger.logVerbose('🔍 === API SERVICE RESPONSE HANDLING END (ERROR) ===');
          }
        }
        return ApiResponse.error(
          errorMessage,
          statusCode: response.statusCode,
          errorCode: errorCode,
          errorDetails: errorDetails,
        );
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('❌ JSON Parse Error: $e');
        if (ApiConfig.enableVerboseLogging) {
          DebugLogger.logVerbose('❌ Exception type: ${e.runtimeType}');
          DebugLogger.logVerbose('❌ Response body that failed to parse: "${response.body}"');
          DebugLogger.logVerbose('❌ Response body type: ${response.body.runtimeType}');
          DebugLogger.logVerbose('❌ Response content-type: ${response.headers['content-type']}');
          DebugLogger.logVerbose('🔍 === API SERVICE RESPONSE HANDLING END (PARSE ERROR) ===');
        }
      }
      print('❌ JSON Parse Error: $e');
      return ApiResponse.error(
        'Failed to parse response',
        statusCode: response.statusCode,
      );
    }
  }

  // Check if error message is a validation error that should be preserved
  static bool _isValidationError(String errorMessage) {
    return errorMessage.contains('Staff schedule violates business hours constraints') ||
           errorMessage.contains('Invalid time format') ||
           errorMessage.contains('Start time must be before end time') ||
           errorMessage.contains('Break time must be within working hours') ||
           errorMessage.contains('Staff cannot work on') ||
           errorMessage.contains('business end time') ||
           errorMessage.contains('business start time');
  }

  // Get user-friendly error message
  static String _getErrorMessage(dynamic error) {
    if (error is SocketException) {
      return 'Nu se poate conecta la server. Verificați conexiunea la internet.';
    } else if (error is HttpException) {
      return 'Eroare de comunicare cu serverul.';
    } else if (error.toString().contains('TimeoutException')) {
      return 'Timpul de așteptare a expirat. Încercați din nou.';
    } else {
      return 'A apărut o eroare neașteptată: ${error.toString()}';
    }
  }

  // Retry mechanism for failed requests
  static Future<ApiResponse<T>> retryRequest<T>(
      Future<ApiResponse<T>> Function() request, {
        int maxRetries = 3,
        Duration delay = const Duration(seconds: 1),
      }) async {
    ApiResponse<T>? lastResponse;

    for (int i = 0; i < maxRetries; i++) {
      lastResponse = await request();

      if (lastResponse.success) {
        return lastResponse;
      }

      if (i < maxRetries - 1) {
        await Future.delayed(delay * (i + 1)); // Exponential backoff
      }
    }

    return lastResponse!;
  }

  // Upload file (for future use)
  static Future<ApiResponse<T>> uploadFile<T>(String endpoint,
      String filePath, {
        Map<String, String>? fields,
        T Function(dynamic)? fromJson,
      }) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint');
      final request = http.MultipartRequest('POST', uri);

      // Add headers
      request.headers.addAll(_headers);

      // Add file
      request.files.add(await http.MultipartFile.fromPath('file', filePath));

      // Add fields
      if (fields != null) {
        request.fields.addAll(fields);
      }

      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('UPLOAD: $uri');
      }

      final streamedResponse = await _client.send(request).timeout(defaultTimeout);
      final response = await http.Response.fromStream(streamedResponse);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('UPLOAD Error: $e');
      }
      return ApiResponse.error(_getErrorMessage(e));
    }
  }

  // Upload XFile (cross-platform compatible)
  static Future<ApiResponse<T>> uploadXFile<T>(String endpoint,
      XFile file, {
        Map<String, String>? fields,
        T Function(dynamic)? fromJson,
      }) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint');
      final request = http.MultipartRequest('POST', uri);

      // Add headers
      request.headers.addAll(_headers);

      // Add file from XFile (works on both web and mobile)
      final bytes = await file.readAsBytes();
      request.files.add(http.MultipartFile.fromBytes(
        'file',
        bytes,
        filename: file.name,
      ));

      // Add fields
      if (fields != null) {
        request.fields.addAll(fields);
      }

      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('UPLOAD XFILE: $uri');
        DebugLogger.logVerbose('File name: ${file.name}');
        DebugLogger.logVerbose('File size: ${bytes.length} bytes');
      }

      final streamedResponse = await _client.send(request).timeout(defaultTimeout);
      final response = await http.Response.fromStream(streamedResponse);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('UPLOAD XFILE Error: $e');
      }
      return ApiResponse.error(_getErrorMessage(e));
    }
  }

  // Helper method to restore auth token from storage
  static Future<void> _restoreAuthTokenFromStorage() async {
    try {
      // Import AuthService to access token storage
      final storedToken = await _getStoredAccessToken();
      if (storedToken != null && storedToken.isNotEmpty) {
        setAuthToken(storedToken);
        if (ApiConfig.enableLogging) {
          DebugLogger.logVerbose('🔄 Auth token restored from storage');
        }
      } else {
        if (ApiConfig.enableLogging) {
          DebugLogger.logVerbose('🚫 No stored auth token found');
        }
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('❌ Error restoring auth token from storage: $e');
      }
    }
  }

  // Helper method to get stored access token (to avoid circular dependency)
  static Future<String?> _getStoredAccessToken() async {
    try {
      const storage = FlutterSecureStorage();
      return await storage.read(key: 'access_token');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('❌ Error reading stored access token: $e');
      }
      return null;
    }
  }

  // Validate current authentication state
  static Future<bool> validateAuthState() async {
    if (!hasAuthToken) {
      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('🔑 No auth token available, attempting to restore...');
      }
      await _restoreAuthTokenFromStorage();

      if (!hasAuthToken) {
        if (ApiConfig.enableLogging) {
          DebugLogger.logVerbose('🔑 Still no auth token available after restore attempt');
        }
        return false;
      }
    }

    try {
      // Try a simple authenticated request to verify token validity
      // Use /api/auth/profile since it's a standard auth endpoint that should always exist
      final response = await get<Map<String, dynamic>>(
        '/api/auth/profile',
        fromJson: (data) => Map<String, dynamic>.from(data),
        requiresAuth: true,
      );

      final isValid = response.success;
      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('🔑 Auth token validation: ${isValid ? 'VALID' : 'INVALID'}');
        if (!isValid) {
          DebugLogger.logVerbose('🔑 Auth validation error: ${response.error}');
          DebugLogger.logVerbose('🔑 Auth validation status: ${response.statusCode}');
        }
      }

      return isValid;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('🔑 Auth validation failed: $e');
      }
      return false;
    }
  }

  /// Update salon's Google review link
  static Future<ApiResponse<Map<String, dynamic>>> updateSalonReviewLink(
    String salonId,
    String googleReviewLink,
  ) async {
    return await patch<Map<String, dynamic>>(
      '/api/salons/$salonId/google-review-link',
      body: {'googleReviewLink': googleReviewLink},
      fromJson: (data) => Map<String, dynamic>.from(data),
    );
  }

  /// Get salon details including Google review link
  static Future<ApiResponse<Map<String, dynamic>>> getSalonDetails(
    String salonId,
  ) async {
    return await get<Map<String, dynamic>>(
      '/api/salons/$salonId',
      fromJson: (data) => Map<String, dynamic>.from(data),
    );
  }
}

