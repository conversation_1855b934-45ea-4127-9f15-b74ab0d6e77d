import '../../models/api_response.dart';
import '../../models/appointment.dart';
import '../../models/client.dart';
import '../../models/inactive_client.dart';
import '../../models/pet.dart';
import '../../models/review.dart';
import '../../models/subscription.dart';
import '../../utils/debug_logger.dart';
import '../api_service.dart';
import '../auth/auth_service.dart';
import '../base_service.dart';

class ClientService {
  // Get all clients for the current salon
  static Future<ApiResponse<List<Client>>> getClients({
    String? search,
    bool? active,
    int? limit,
    int? offset,
  }) async {
    final queryParams = BaseService.buildQueryParams({
      'search': search,
      'active': active,
      'limit': limit ?? 100,
      'offset': offset,
    });

    return await BaseService.getSalonResource<List<Client>>(
      'clients',
      queryParams: queryParams,
      fromJson: (data) => BaseService.parseListResponse<Client>(data, Client.fromJson),
    );
  }

  // Get total count of clients (separate from pagination)
  static Future<ApiResponse<int>> getClientsCount({
    String? search,
    bool? active,
  }) async {
    final queryParams = BaseService.buildQueryParams({
      'search': search,
      'active': active,
    });

    return await BaseService.getSalonResource<int>(
      'clients/count',
      queryParams: queryParams,
      fromJson: (data) => (data['count'] as num).toInt(),
    );
  }

  // Get client by ID
  static Future<ApiResponse<Client>> getClient(String id) async {
    return await BaseService.getSalonResource<Client>(
      'clients/$id',
      fromJson: (data) => Client.fromJson(data),
    );
  }

  // Create new client
  static Future<ApiResponse<Client>> createClient(Client client) async {
    return await BaseService.postSalonResource<Client>(
      'clients',
      body: client.toJson(),
      fromJson: (data) => Client.fromJson(data),
    );
  }

  // Update existing client
  static Future<ApiResponse<Client>> updateClient(String id, Client client) async {
    // Create UpdateClientRequest body to match backend API
    final updateRequest = {
      'name': client.name,
      'phone': client.phone.isEmpty ? null : client.phone,
      'secondaryPhone': client.secondaryPhone?.isEmpty == true ? null : client.secondaryPhone,
      'email': client.email.isEmpty ? null : client.email,
      'address': client.address.isEmpty ? null : client.address,
      'notes': client.notes.isEmpty ? null : client.notes,
    };

    return await BaseService.putSalonResource<Client>(
      'clients/$id',
      body: updateRequest,
      fromJson: (data) => Client.fromJson(data),
    );
  }

  // Delete client
  static Future<ApiResponse<bool>> deleteClient(String id) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<bool>.error('No salon selected');
      }

      DebugLogger.logVerbose('🗑️ ClientService: Deleting client: $id from salon: $salonId');

      final response = await ApiService.delete<Map<String, dynamic>>(
        '/api/salons/$salonId/clients/$id',
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final data = response.data!;
        final success = data['success'] == true;
        final message = data['message'] ?? 'Client deleted successfully';

        DebugLogger.logVerbose('✅ ClientService: Delete response - Success: $success, Message: $message');

        if (success) {
          return ApiResponse<bool>.success(true);
        } else {
          return ApiResponse<bool>.error(message);
        }
      } else {
        DebugLogger.logVerbose('❌ ClientService: Delete failed - ${response.error}');
        return ApiResponse<bool>.error(response.error ?? 'Failed to delete client');
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ ClientService: Exception during client deletion: $e');
      return ApiResponse<bool>.error('Failed to delete client: $e');
    }
  }

  // Get client's pets
  static Future<ApiResponse<List<Pet>>> getClientPets(String clientId) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<List<Pet>>.error('No salon selected');
      }

      final response = await ApiService.get<List<Pet>>(
        '/api/salons/$salonId/clients/$clientId/pets',
        fromJson: (data) => (data as List).map((item) => Pet.fromJson(item)).toList(),
      );

      return response;
    } catch (e) {
      return ApiResponse<List<Pet>>.error('Failed to load client pets: $e');
    }
  }

  // Add pet to client
  static Future<ApiResponse<Pet>> addPetToClient(String clientId, Pet pet) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<Pet>.error('No salon selected');
      }

      final response = await ApiService.post<Pet>(
        '/api/salons/$salonId/clients/$clientId/pets',
        body: pet.toJson(),
        fromJson: (data) => Pet.fromJson(data),
      );

      return response;
    } catch (e) {
      return ApiResponse<Pet>.error('Failed to add pet to client: $e');
    }
  }

  // Update pet
  static Future<ApiResponse<Pet>> updatePet(String petId, Pet pet) async {
    final response = await ApiService.put<Pet>(
      '/api/pets/$petId',
      body: pet.toJson(),
      fromJson: (data) => Pet.fromJson(data),
    );

    return response;
  }

  // Delete pet
  static Future<ApiResponse<void>> deletePet(String petId) async {
    final response = await ApiService.delete<void>('/api/pets/$petId');
    return response;
  }

  // Get pet by ID
  static Future<ApiResponse<Pet>> getPet(String petId) async {
    final response = await ApiService.get<Pet>(
      '/api/pets/$petId',
      fromJson: (data) => Pet.fromJson(data),
    );

    return response;
  }

  // Search clients by name, phone, or email
  static Future<ApiResponse<List<Client>>> searchClients(String query) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<List<Client>>.error('No salon selected');
      }

      final response = await ApiService.get<List<Client>>(
        '/api/salons/$salonId/clients',
        queryParams: {'search': query},
        fromJson: (data) => (data as List).map((item) => Client.fromJson(item)).toList(),
      );

      return response;
    } catch (e) {
      return ApiResponse<List<Client>>.error('Failed to search clients: $e');
    }
  }

  // Get client statistics
  static Future<ApiResponse<Map<String, dynamic>>> getClientStats(String clientId) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<Map<String, dynamic>>.error('No salon selected');
      }

      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/salons/$salonId/clients/$clientId/stats',
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      return response;
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>.error('Failed to load client stats: $e');
    }
  }

  // Get recent clients (last 30 days)
  static Future<ApiResponse<List<Client>>> getRecentClients({int limit = 10}) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<List<Client>>.error('No salon selected');
      }

      final response = await ApiService.get<List<Client>>(
        '/api/salons/$salonId/clients',
        queryParams: {
          'recent': 'true',
          'limit': limit.toString(),
        },
        fromJson: (data) => (data as List).map((item) => Client.fromJson(item)).toList(),
      );

      return response;
    } catch (e) {
      return ApiResponse<List<Client>>.error('Failed to load recent clients: $e');
    }
  }

  // Get inactive clients (no appointments in the last X days)
  static Future<ApiResponse<List<InactiveClient>>> getInactiveClients({int daysSinceLastAppointment = 30}) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<List<InactiveClient>>.error('No salon selected');
      }

      final response = await ApiService.get<List<InactiveClient>>(
        '/api/salons/$salonId/clients/inactive',
        queryParams: {
          'daysSinceLastAppointment': daysSinceLastAppointment.toString(),
        },
        fromJson: (data) => (data as List).map((item) => InactiveClient.fromJson(item)).toList(),
      );

      return response;
    } catch (e) {
      return ApiResponse<List<InactiveClient>>.error('Failed to load inactive clients: $e');
    }
  }

  // Validate client data before sending to server
  static String? validateClient(Client client) {
    if (client.name.trim().isEmpty) {
      return 'Numele clientului este obligatoriu';
    }

    if (client.phone.trim().isEmpty) {
      return 'Numărul de telefon este obligatoriu';
    }

    if (client.email.trim().isNotEmpty && !_isValidEmail(client.email)) {
      return 'Adresa de email nu este validă';
    }

    return null; // No validation errors
  }

  // Validate pet data before sending to server
  static String? validatePet(Pet pet) {
    if (pet.name.trim().isEmpty) {
      return 'Numele animalului este obligatoriu';
    }

    if (pet.species.trim().isEmpty) {
      return 'Specia animalului este obligatorie';
    }

    if (pet.weight <= 0) {
      return 'Greutatea trebuie să fie mai mare decât 0';
    }

    return null; // No validation errors
  }

  // Helper method to validate email format
  static bool _isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(email);
  }

  // Retry failed requests with exponential backoff
  static Future<ApiResponse<T>> retryClientRequest<T>(
    Future<ApiResponse<T>> Function() request,
  ) async {
    return ApiService.retryRequest(request, maxRetries: 3);
  }

  // Bulk operations
  static Future<ApiResponse<List<Client>>> createMultipleClients(List<Client> clients) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<List<Client>>.error('No salon selected');
      }

      final response = await ApiService.post<List<Client>>(
        '/api/salons/$salonId/clients/batch',
        body: {
          'clients': clients.map((c) => {
            'name': c.name,
            'phone': c.phone,
            'email': c.email.isEmpty ? null : c.email,
            'address': c.address.isEmpty ? null : c.address,
            'notes': c.notes.isEmpty ? null : c.notes,
          }).toList()
        },
        fromJson: (data) => (data as List).map((item) => Client.fromJson(item)).toList(),
      );

      return response;
    } catch (e) {
      return ApiResponse<List<Client>>.error('Failed to create multiple clients: $e');
    }
  }

  // Delete multiple clients
  static Future<ApiResponse<Map<String, dynamic>>> deleteMultipleClients(List<String> clientIds) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<Map<String, dynamic>>.error('No salon selected');
      }

      DebugLogger.logVerbose('🗑️ ClientService: Deleting ${clientIds.length} clients from salon: $salonId');

      final response = await ApiService.delete<Map<String, dynamic>>(
        '/api/salons/$salonId/clients/batch',
        body: {'clientIds': clientIds},
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final data = response.data!;
        final deletedCount = data['deletedCount'] ?? 0;
        final failedCount = data['failedCount'] ?? 0;
        final errors = data['errors'] as List<dynamic>? ?? [];

        DebugLogger.logVerbose('✅ ClientService: Bulk delete response - Deleted: $deletedCount, Failed: $failedCount');

        return ApiResponse<Map<String, dynamic>>.success({
          'deletedCount': deletedCount,
          'failedCount': failedCount,
          'errors': errors,
        });
      } else {
        DebugLogger.logVerbose('❌ ClientService: Bulk delete failed - ${response.error}');
        return ApiResponse<Map<String, dynamic>>.error(response.error ?? 'Failed to delete clients');
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ ClientService: Exception during bulk client deletion: $e');
      return ApiResponse<Map<String, dynamic>>.error('Failed to delete clients: $e');
    }
  }

  // Export clients data
  static Future<ApiResponse<String>> exportClients({
    String format = 'csv',
    List<String>? clientIds,
  }) async {
    final body = <String, dynamic>{
      'format': format,
      if (clientIds != null) 'clientIds': clientIds,
    };

    final response = await ApiService.post<String>(
      '/api/clients/export',
      body: body,
      fromJson: (data) => data.toString(),
    );

    return response;
  }

  // Get client statistics (placeholder for backward compatibility)
  static Future<Map<String, dynamic>?> getClientStatistics(String clientId) async {
    final response = await getClientStats(clientId);
    return response.success ? response.data : null;
  }

  // Get client subscriptions
  static Future<ApiResponse<List<Subscription>>> getClientSubscriptions(String clientId) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<List<Subscription>>.error('No salon selected');
      }

      final response = await ApiService.get<List<Subscription>>(
        '/api/salons/$salonId/clients/$clientId/subscriptions',
        fromJson: (data) => (data as List).map((item) => Subscription.fromJson(item)).toList(),
      );

      return response;
    } catch (e) {
      return ApiResponse<List<Subscription>>.error('Failed to load client subscriptions: $e');
    }
  }

  // Get client appointments
  static Future<ApiResponse<List<Appointment>>> getClientAppointments(String clientId) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<List<Appointment>>.error('No salon selected');
      }

      final response = await ApiService.get<List<Appointment>>(
        '/api/salons/$salonId/clients/$clientId/appointments',
        fromJson: (data) => (data as List).map((item) => Appointment.fromJson(item)).toList(),
      );

      return response;
    } catch (e) {
      return ApiResponse<List<Appointment>>.error('Failed to load client appointments: $e');
    }
  }

  // Get client's last appointment date
  static Future<ApiResponse<DateTime?>> getClientLastAppointmentDate(String clientId) async {
    try {
      final appointmentsResponse = await getClientAppointments(clientId);

      if (!appointmentsResponse.success || appointmentsResponse.data == null) {
        return ApiResponse<DateTime?>.success(null);
      }

      final appointments = appointmentsResponse.data!;
      if (appointments.isEmpty) {
        return ApiResponse<DateTime?>.success(null);
      }

      // Find appointments that actually happened (not cancelled, no-show, or rescheduled)
      // Include: completed, confirmed, scheduled, in-progress (appointments that were/are real visits)
      final validAppointments = appointments
          .where((apt) {
            final status = apt.status.toLowerCase();
            return status == 'completed' ||
                   status == 'confirmed' ||
                   status == 'scheduled' ||
                   status == 'in_progress' ||
                   status == 'in-progress' ||
                   status == 'programat' ||
                   status == 'confirmat' ||
                   status == 'finalizat';
          })
          .toList();

      if (validAppointments.isEmpty) {
        return ApiResponse<DateTime?>.success(null);
      }

      // Sort by start time descending and get the most recent
      validAppointments.sort((a, b) => b.startTime.compareTo(a.startTime));
      final lastAppointmentDate = validAppointments.first.startTime;

      return ApiResponse<DateTime?>.success(lastAppointmentDate);
    } catch (e) {
      return ApiResponse<DateTime?>.error('Failed to get client last appointment date: $e');
    }
  }

  // Get client reviews
  static Future<ApiResponse<List<Review>>> getClientReviews(String clientId) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<List<Review>>.error('No salon selected');
      }

      final response = await ApiService.get<List<Review>>(
        '/api/salons/$salonId/clients/$clientId/reviews',
        fromJson: (data) => (data as List).map((item) => Review.fromJson(item)).toList(),
      );

      return response;
    } catch (e) {
      return ApiResponse<List<Review>>.error('Failed to load client reviews: $e');
    }
  }

  // Backward compatibility methods
  static Future<List<Map<String, dynamic>>> getClientSubscriptionsLegacy(String clientId) async {
    final response = await getClientSubscriptions(clientId);
    return response.success ? response.data!.map((s) => s.toJson()).toList() : [];
  }

  static Future<List<Map<String, dynamic>>> getClientReviewsLegacy(String clientId) async {
    final response = await getClientReviews(clientId);
    return response.success ? response.data!.map((r) => r.toJson()).toList() : [];
  }
}
