import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:showcaseview/showcaseview.dart';

import '../../utils/debug_logger.dart';
import '../auth/auth_service.dart';
import '../feature_toggle_service.dart';

/// Service to manage the interactive onboarding tour
class OnboardingTourService {
  static const String _tourCompletedKey = 'onboarding_tour_completed';
  static const String _tourVersionKey = 'onboarding_tour_version';
  static const String _tourProgressKey = 'onboarding_tour_progress';
  static const String _tourSkippedKey = 'onboarding_tour_skipped';
  
  static const int currentTourVersion = 1;
  
  // Tour step identifiers
  static const String welcomeStep = 'welcome';
  static const String navigationStep = 'navigation';
  static const String settingsStep = 'settings';
  static const String nameEditStep = 'name_edit';
  static const String phoneEditStep = 'phone_edit';
  static const String salonSwitcherStep = 'salon_switcher';
  static const String themeToggleStep = 'theme_toggle';
  static const String profileSectionStep = 'profile_section';
  static const String appSettingsStep = 'app_settings';
  static const String helpSectionStep = 'help_section';
  static const String logoutButtonStep = 'logout_button';
  static const String salonManagementStep = 'salon_management';
  static const String teamManagementStep = 'team_management';
  static const String servicesManagementStep = 'services_management';
  static const String workingHoursStep = 'working_hours';
  static const String notificationSettingsStep = 'notification_settings';
  static const String salonNameStep = 'salon_name';
  static const String salonAddressStep = 'salon_address';
  static const String salonContactStep = 'salon_contact';
  static const String salonDescriptionStep = 'salon_description';
  static const String salonSaveStep = 'salon_save';
  static const String calendarStep = 'calendar';
  static const String calendarViewsStep = 'calendar_views';
  static const String staffSelectionStep = 'staff_selection';
  static const String timeSlotStep = 'time_slot';
  static const String fabStep = 'fab';
  static const String appointmentDetailsStep = 'appointment_details';
  static const String clientsStep = 'clients';
  static const String addClientStep = 'add_client';
  static const String clientSearchStep = 'client_search';
  static const String clientDetailsStep = 'client_details';

  static const String notificationsStep = 'notifications';
  static const String smsStep = 'sms';
  static const String completionStep = 'completion';
  static const String nextStepsStep = 'next_steps';

  /// Check if the user should see the onboarding tour
  static Future<bool> shouldShowTour() async {
    DebugLogger.logShowcase('🎯 OnboardingTourService: Checking if tour should show...');
    
    // First check if the showcase feature is enabled via feature flag
    final isFeatureEnabled = await FeatureToggleService.isShowcaseEnabled();
    if (!isFeatureEnabled) {
      DebugLogger.logShowcase('🎯 OnboardingTourService: Showcase feature flag is disabled');
      return false;
    }
    
    final prefs = await SharedPreferences.getInstance();

    // Check if tour was completed
    final isCompleted = prefs.getBool(_tourCompletedKey) ?? false;
    DebugLogger.logShowcase('🎯 OnboardingTourService: Tour completed: $isCompleted');

    if (isCompleted) {
      // Check if tour version has been updated
      final savedVersion = prefs.getInt(_tourVersionKey) ?? 0;
      DebugLogger.logShowcase('🎯 OnboardingTourService: Saved version: $savedVersion, Current version: $currentTourVersion');
      return savedVersion < currentTourVersion;
    }

    // Check if tour was skipped
    final isSkipped = prefs.getBool(_tourSkippedKey) ?? false;
    DebugLogger.logShowcase('🎯 OnboardingTourService: Tour skipped: $isSkipped');

    if (isSkipped) {
      return false;
    }

    // New user should see the tour
    DebugLogger.logShowcase('🎯 OnboardingTourService: New user - should show tour');
    return true;
  }

  OnboardingTourService();

  /// Mark the tour as completed
  static Future<void> markTourCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_tourCompletedKey, true);
    await prefs.setInt(_tourVersionKey, currentTourVersion);
    await prefs.remove(_tourProgressKey);
  }

  /// Reset tour completion status (for testing or manual restart)
  static Future<void> resetTour() async {
    DebugLogger.logShowcase('🎯 OnboardingTourService: Resetting tour status');
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_tourCompletedKey, false);
    await prefs.remove(_tourVersionKey);
    await prefs.remove(_tourProgressKey);
  }

  /// Mark the tour as skipped
  static Future<void> markTourSkipped() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_tourSkippedKey, true);
    await prefs.remove(_tourProgressKey);
  }

  /// Save tour progress
  static Future<void> saveTourProgress(List<String> completedSteps) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList(_tourProgressKey, completedSteps);
  }

  /// Get saved tour progress
  static Future<List<String>> getTourProgress() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(_tourProgressKey) ?? [];
  }

  /// Reset tour state (for testing or manual restart)
  static Future<void> resetTourState() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tourCompletedKey);
    await prefs.remove(_tourVersionKey);
    await prefs.remove(_tourProgressKey);
    await prefs.remove(_tourSkippedKey);
  }

  /// Get tour steps based on user role and subscription
  static Future<List<String>> getTourStepsForUser() async {
    final baseSteps = [
      welcomeStep,
      navigationStep,
      settingsStep,
      nameEditStep,
      themeToggleStep,
    ];

    // Add salon management steps for chief groomers
    final salonId = await AuthService.getCurrentSalonId();
    if (salonId != null && salonId.isNotEmpty) {
      baseSteps.addAll([
        salonManagementStep,
        calendarStep,
        calendarViewsStep,
        staffSelectionStep,
        timeSlotStep,
        fabStep,
        clientsStep,
        addClientStep,
        clientSearchStep,
        clientDetailsStep,
        notificationsStep,
      ]);

      // Add subscription-dependent steps
      // Note: This would need to be enhanced with actual subscription checking
      // baseSteps.addAll([smsStep]);
    } else {
      // User without salon - show salon creation steps
      baseSteps.addAll([
        salonNameStep,
        salonAddressStep,
        salonContactStep,
        salonDescriptionStep,
        salonSaveStep,
      ]);
    }

    baseSteps.addAll([completionStep, nextStepsStep]);
    return baseSteps;
  }

  /// Start the tour with proper context
  static Future<void> startTour(BuildContext context, List<GlobalKey> showcaseKeys) async {
    // Check feature flag and other conditions
    if (!await shouldShowTour()) {
      DebugLogger.logShowcase('🎯 OnboardingTourService: Tour not shown due to conditions');
      return;
    }

    // Start the showcase tour
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ShowCaseWidget.of(context).startShowCase(showcaseKeys);
    });
  }

  /// Continue tour to next phase
  static void continueToNextPhase(BuildContext context, List<GlobalKey> nextKeys) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ShowCaseWidget.of(context).startShowCase(nextKeys);
    });
  }

  /// Get localized tour content
  static Map<String, TourStepContent> getTourContent() {
    return {
      welcomeStep: TourStepContent(
        title: 'Bine ai venit în Animalia!',
        description: 'Să îți arătăm rapid ceum să folosești aplicația.',
        targetDescription: 'Apasă pentru a continua',
      ),
      navigationStep: TourStepContent(
        title: 'Navigare Principală',
        description: 'Aceasta este bara de navigare principală. Aici găsești toate secțiunile importante ale aplicației.',
        targetDescription: 'Bara de navigare de jos',
      ),
      settingsStep: TourStepContent(
        title: 'Setări',
        description: 'Să începem cu setările. Apasă aici pentru a personaliza aplicația după preferințele tale.',
        targetDescription: 'Apasă pe Setări',
      ),
      nameEditStep: TourStepContent(
        title: 'Editează Numele',
        description: 'Aici poți să îți schimbi numele care va apărea în calendar, notificari, si mesaje automate.',
        targetDescription: 'Opțiunea de editare nume',
      ),
      themeToggleStep: TourStepContent(
        title: 'Temă Aplicație',
        description: 'Poți comuta între tema deschisă și întunecată pentru confort vizual optim. Acum să mergem la gestionarea salonului!',
        targetDescription: 'Comutator temă',
      ),
      'profile_section': TourStepContent(
        title: 'Secțiunea Salon',
        description: 'Aici vezi informațiile despre salonul tău și poți gestiona setările principale. Aceasta este zona de control pentru salonul tău.',
        targetDescription: 'Secțiunea salon',
      ),
      'user_settings': TourStepContent(
        title: 'Setări Utilizator',
        description: 'În această secțiune poți edita numele tău, schimba numărul de telefon și personaliza alte setări ale contului.',
        targetDescription: 'Setări utilizator',
      ),
      'app_settings': TourStepContent(
        title: 'Setări Aplicație',
        description: 'Aici poți schimba tema aplicației, limba și alte preferințe pentru o experiență personalizată.',
        targetDescription: 'Setări aplicație',
      ),
      'help_section': TourStepContent(
        title: 'Ajutor și Suport',
        description: 'Când ai nevoie de ajutor, aici găsești documentația, termenii și condițiile și modalități de contact.',
        targetDescription: 'Secțiunea ajutor',
      ),
      'logout_button': TourStepContent(
        title: 'Deconectare',
        description: 'Când termini de folosit aplicația, poți să te deconectezi în siguranță de aici. Acum să explorăm salonul!',
        targetDescription: 'Buton deconectare',
      ),
      salonManagementStep: TourStepContent(
        title: 'Gestionare Salon',
        description: 'Acum să explorăm funcționalitățile principale pentru gestionarea salonului tău de pet grooming.',
        targetDescription: 'Tabul Salon',
      ),
      teamManagementStep: TourStepContent(
        title: 'Echipa Mea',
        description: 'Gestionează membrii echipei tale - adaugă groomeri, asigna roluri și configurează permisiunile pentru fiecare membru.',
        targetDescription: 'Secțiunea echipei',
      ),
      servicesManagementStep: TourStepContent(
        title: 'Serviciile Mele',
        description: 'Configurează serviciile oferite de salon - prețuri, durata, descrieri și categorii pentru o experiență completă.',
        targetDescription: 'Gestionarea serviciilor',
      ),
      workingHoursStep: TourStepContent(
        title: 'Orogram de Lucru',
        description: 'Stabilește programul de lucru al salonului - ore de funcționare, zile libere și sărbători pentru planificare optimă.',
        targetDescription: 'Configurare program',
      ),
      notificationSettingsStep: TourStepContent(
        title: 'Setări Notificări',
        description: 'Personalizează notificările pentru salon - alerte pentru programări, reminder-e și comunicări cu clienții.',
        targetDescription: 'Setări notificări',
      ),
      calendarStep: TourStepContent(
        title: 'Calendar Programări',
        description: 'Calendarul este inima aplicației - aici gestionezi toate programările și vezi disponibilitatea.',
        targetDescription: 'Apasă pe Calendar',
      ),
      clientsStep: TourStepContent(
        title: 'Gestionare Clienți',
        description: 'Aici gestionezi baza de date cu clienții și animalele lor pentru o experiență personalizată.',
        targetDescription: 'Apasă pe Clienți',
      ),
      notificationsStep: TourStepContent(
        title: 'Centrul de Notificări',
        description: 'Primești notificări pentru programări noi, modificări și mesaje importante de la sistem.',
        targetDescription: 'Apasă pe Notificări',
      ),
      phoneEditStep: TourStepContent(
        title: 'Editează Telefonul',
        description: 'Aici poți să îți actualizezi numărul de telefon pentru a fi contactat de clienți.',
        targetDescription: 'Opțiunea de editare telefon',
      ),
      salonSwitcherStep: TourStepContent(
        title: 'Schimbă Salonul',
        description: 'Dacă ai mai multe saloane, aici poți comuta rapid între ele.',
        targetDescription: 'Selector salon',
      ),
      completionStep: TourStepContent(
        title: 'Tur Completat!',
        description: 'Felicitări! Ai terminat turul de prezentare. Poți reaccesa acest ghid oricând din Setări.',
        targetDescription: 'Finalizare tur',
      ),
    };
  }
}

/// Data class for tour step content
class TourStepContent {
  final String title;
  final String description;
  final String targetDescription;

  const TourStepContent({
    required this.title,
    required this.description,
    required this.targetDescription,
  });
}
