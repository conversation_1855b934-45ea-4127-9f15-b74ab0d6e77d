import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../config/api_config.dart';
import 'user_analytics_engine.dart';

/// Service for managing privacy compliance, GDPR, and user consent
class PrivacyComplianceService {
  static const String _consentKey = 'analytics_privacy_consent';
  static const String _consentVersionKey = 'analytics_consent_version';
  static const String _consentDateKey = 'analytics_consent_date';
  static const String _dataRetentionKey = 'analytics_data_retention';
  
  static const int _currentConsentVersion = 1;
  static const Duration _defaultRetentionPeriod = Duration(days: 730); // 2 years
  
  static PrivacyConsent? _cachedConsent;

  /// Check if user has given consent for analytics tracking
  static Future<bool> hasAnalyticsConsent() async {
    final consent = await getConsent();
    return consent.analytics;
  }

  /// Check if user has given consent for performance tracking
  static Future<bool> hasPerformanceConsent() async {
    final consent = await getConsent();
    return consent.performance;
  }

  /// Check if user has given consent for marketing tracking
  static Future<bool> hasMarketingConsent() async {
    final consent = await getConsent();
    return consent.marketing;
  }

  /// Check if we can track a specific event type
  static Future<bool> canTrackEventType(AnalyticsEventType eventType) async {
    final consent = await getConsent();
    
    switch (eventType) {
      case AnalyticsEventType.navigation:
      case AnalyticsEventType.featureUsage:
      case AnalyticsEventType.uiInteraction:
      case AnalyticsEventType.session:
        return consent.analytics;
      
      case AnalyticsEventType.performance:
        return consent.performance;
      
      case AnalyticsEventType.subscription:
        return consent.marketing; // Subscription events are marketing-related
      
      case AnalyticsEventType.custom:
        return consent.analytics; // Default to analytics consent
    }
  }

  /// Get current consent status
  static Future<PrivacyConsent> getConsent() async {
    if (_cachedConsent != null) return _cachedConsent!;

    try {
      final prefs = await SharedPreferences.getInstance();
      final consentJson = prefs.getString(_consentKey);
      
      if (consentJson != null) {
        final consentData = jsonDecode(consentJson);
        _cachedConsent = PrivacyConsent.fromJson(consentData);
        
        // Check if consent version is outdated
        final consentVersion = prefs.getInt(_consentVersionKey) ?? 0;
        if (consentVersion < _currentConsentVersion) {
          // Consent is outdated, require new consent
          _cachedConsent = PrivacyConsent.none();
        }
      } else {
        // No consent given yet
        _cachedConsent = PrivacyConsent.none();
      }
      
      return _cachedConsent!;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to get privacy consent: $e');
      }
      _cachedConsent = PrivacyConsent.none();
      return _cachedConsent!;
    }
  }

  /// Update user consent preferences
  static Future<void> updateConsent(PrivacyConsent consent) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Save consent
      await prefs.setString(_consentKey, jsonEncode(consent.toJson()));
      await prefs.setInt(_consentVersionKey, _currentConsentVersion);
      await prefs.setString(_consentDateKey, DateTime.now().toIso8601String());
      
      // Update cached consent
      _cachedConsent = consent;
      
      // If analytics consent was granted, initialize analytics engine
      if (consent.analytics) {
        await UserAnalyticsEngine.initialize();
      }
      
      // Track consent change (if analytics consent is given)
      if (consent.analytics) {
        await UserAnalyticsEngine.trackEvent(AnalyticsEvent(
          eventType: AnalyticsEventType.custom,
          action: 'privacy_consent_updated',
          data: {
            'analytics_consent': consent.analytics,
            'performance_consent': consent.performance,
            'marketing_consent': consent.marketing,
            'consent_version': _currentConsentVersion,
          },
        ));
      }
      
      if (ApiConfig.enableLogging) {
        debugPrint('📊 Privacy consent updated: ${consent.toJson()}');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to update privacy consent: $e');
      }
    }
  }

  /// Show privacy consent dialog to user
  static Future<void> showConsentDialog(BuildContext context) async {
    final currentConsent = await getConsent();

    if (!context.mounted) return;

    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return PrivacyConsentDialog(initialConsent: currentConsent);
      },
    );
  }

  /// Check if consent dialog should be shown
  static Future<bool> shouldShowConsentDialog() async {
    final consent = await getConsent();
    final prefs = await SharedPreferences.getInstance();
    final consentVersion = prefs.getInt(_consentVersionKey) ?? 0;
    
    // Show dialog if no consent given or consent version is outdated
    return !consent.hasAnyConsent() || consentVersion < _currentConsentVersion;
  }

  /// Get consent status for debugging/display
  static Map<String, dynamic> getConsentStatus() {
    if (_cachedConsent == null) return {'status': 'not_loaded'};
    
    return {
      'analytics': _cachedConsent!.analytics,
      'performance': _cachedConsent!.performance,
      'marketing': _cachedConsent!.marketing,
      'version': _currentConsentVersion,
      'has_any_consent': _cachedConsent!.hasAnyConsent(),
    };
  }

  /// Clear all analytics data (for GDPR data deletion requests)
  static Future<void> clearAllAnalyticsData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Remove all analytics-related keys
      final keys = prefs.getKeys().where((key) => 
        key.startsWith('analytics_') || 
        key.startsWith('retention_') ||
        key.startsWith('completed_appointments_') ||
        key.startsWith('last_subscription_prompt_')
      ).toList();
      
      for (final key in keys) {
        await prefs.remove(key);
      }
      
      // Clear cached consent
      _cachedConsent = PrivacyConsent.none();
      
      if (ApiConfig.enableLogging) {
        debugPrint('📊 All analytics data cleared (${keys.length} keys removed)');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to clear analytics data: $e');
      }
    }
  }

  /// Schedule automatic data cleanup based on retention policy
  static Future<void> scheduleDataCleanup() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final retentionData = prefs.getString(_dataRetentionKey);
      
      DateTime cutoffDate;
      if (retentionData != null) {
        final retention = jsonDecode(retentionData);
        cutoffDate = DateTime.parse(retention['cutoff_date']);
      } else {
        cutoffDate = DateTime.now().subtract(_defaultRetentionPeriod);
        await prefs.setString(_dataRetentionKey, jsonEncode({
          'cutoff_date': cutoffDate.toIso8601String(),
          'retention_days': _defaultRetentionPeriod.inDays,
        }));
      }
      
      // TODO: Implement actual data cleanup logic based on cutoff date
      // This would remove analytics events older than the retention period
      
      if (ApiConfig.enableLogging) {
        debugPrint('📊 Data cleanup scheduled for data older than: $cutoffDate');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to schedule data cleanup: $e');
      }
    }
  }
}

/// Privacy consent data model
class PrivacyConsent {
  final bool analytics;
  final bool performance;
  final bool marketing;

  const PrivacyConsent({
    required this.analytics,
    required this.performance,
    required this.marketing,
  });

  /// Create consent with no permissions
  factory PrivacyConsent.none() {
    return const PrivacyConsent(
      analytics: false,
      performance: false,
      marketing: false,
    );
  }

  /// Create consent with all permissions
  factory PrivacyConsent.all() {
    return const PrivacyConsent(
      analytics: true,
      performance: true,
      marketing: true,
    );
  }

  /// Create consent with only essential permissions
  factory PrivacyConsent.essential() {
    return const PrivacyConsent(
      analytics: true,
      performance: true,
      marketing: false,
    );
  }

  bool hasAnyConsent() {
    return analytics || performance || marketing;
  }

  Map<String, dynamic> toJson() {
    return {
      'analytics': analytics,
      'performance': performance,
      'marketing': marketing,
    };
  }

  factory PrivacyConsent.fromJson(Map<String, dynamic> json) {
    return PrivacyConsent(
      analytics: json['analytics'] ?? false,
      performance: json['performance'] ?? false,
      marketing: json['marketing'] ?? false,
    );
  }

  PrivacyConsent copyWith({
    bool? analytics,
    bool? performance,
    bool? marketing,
  }) {
    return PrivacyConsent(
      analytics: analytics ?? this.analytics,
      performance: performance ?? this.performance,
      marketing: marketing ?? this.marketing,
    );
  }
}

/// Privacy consent dialog widget
class PrivacyConsentDialog extends StatefulWidget {
  final PrivacyConsent initialConsent;

  const PrivacyConsentDialog({
    super.key,
    required this.initialConsent,
  });

  @override
  State<PrivacyConsentDialog> createState() => _PrivacyConsentDialogState();
}

class _PrivacyConsentDialogState extends State<PrivacyConsentDialog> {
  late PrivacyConsent _consent;

  @override
  void initState() {
    super.initState();
    _consent = widget.initialConsent;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text(
        'Setări Confidențialitate',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Te rugăm să alegi ce date poți să colectăm pentru a îmbunătăți experiența ta:',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            
            _buildConsentOption(
              title: 'Analiză Utilizare',
              description: 'Colectăm date despre cum folosești aplicația pentru a o îmbunătăți.',
              value: _consent.analytics,
              onChanged: (value) => setState(() {
                _consent = _consent.copyWith(analytics: value);
              }),
            ),
            
            _buildConsentOption(
              title: 'Performanță',
              description: 'Monitorizăm performanța aplicației pentru a identifica probleme.',
              value: _consent.performance,
              onChanged: (value) => setState(() {
                _consent = _consent.copyWith(performance: value);
              }),
            ),
            
            _buildConsentOption(
              title: 'Marketing',
              description: 'Urmărim conversiile pentru a îmbunătăți ofertele noastre.',
              value: _consent.marketing,
              onChanged: (value) => setState(() {
                _consent = _consent.copyWith(marketing: value);
              }),
            ),
            
            const SizedBox(height: 16),
            const Text(
              'Poți schimba aceste setări oricând din meniul de setări.',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            PrivacyComplianceService.updateConsent(PrivacyConsent.none());
          },
          child: const Text('Refuz Tot'),
        ),
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            PrivacyComplianceService.updateConsent(PrivacyConsent.essential());
          },
          child: const Text('Doar Esențiale'),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop();
            PrivacyComplianceService.updateConsent(_consent);
          },
          child: const Text('Salvează'),
        ),
      ],
    );
  }

  Widget _buildConsentOption({
    required String title,
    required String description,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Checkbox(
            value: value,
            onChanged: (newValue) => onChanged(newValue ?? false),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
