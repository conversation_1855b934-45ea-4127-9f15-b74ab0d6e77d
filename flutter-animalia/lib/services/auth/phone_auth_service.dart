import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

import '../../utils/debug_logger.dart';

class PhoneAuthService {
  final FirebaseAuth _auth;
  String? _verificationId;

  PhoneAuthService(this._auth);

  // Phone Number Authentication - Send Code
  Future<void> verifyPhoneNumber(
    String phoneNumber, {
    required Function(PhoneAuthCredential) verificationCompleted,
    required Function(FirebaseAuthException) verificationFailed,
    required Function(String, int?) codeSent,
    required Function(String) codeAutoRetrievalTimeout,
  }) async {
    try {
      // Configure Firebase Auth based on platform
      if (defaultTargetPlatform == TargetPlatform.iOS) {
        // iOS: Use reCAPTCHA Enterprise
        await _auth.setSettings(
          appVerificationDisabledForTesting: false,
          forceRecaptchaFlow: true, // Force reCAPTCHA flow for iOS
          userAccessGroup: null,
        );
        DebugLogger.logVerbose('🔧 iOS: Using reCAPTCHA Enterprise for verification');
      } else if (defaultTargetPlatform == TargetPlatform.android) {
        // Android: Use Play Integrity API (preferred) or SafetyNet
        await _auth.setSettings(
          appVerificationDisabledForTesting: false,
          forceRecaptchaFlow: false, // Use Play Integrity API for Android
        );
        DebugLogger.logVerbose('🔧 Android: Using Play Integrity API for verification');
      }

      // Additional debug logging
      DebugLogger.logVerbose('🔧 Firebase Auth configured for phone auth:');
      DebugLogger.logVerbose('- Phone number: $phoneNumber');
      DebugLogger.logVerbose('- Platform: ${defaultTargetPlatform.name}');

      await _auth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        timeout: const Duration(seconds: 60),
        verificationCompleted: (PhoneAuthCredential credential) async {
          DebugLogger.logVerbose('✅ Verificare completată automat');
          verificationCompleted(credential);
        },
        verificationFailed: (FirebaseAuthException e) {
          DebugLogger.logError('❌ Verificare eșuată: ${e.code} - ${e.message}');
          if (e.code == 'unknown' && e.message?.contains('custom URL scheme') == true) {
            DebugLogger.logError(' URL SCHEME ERROR DETECTED ');
          } else if (e.code == 'invalid-app-credential') {
            DebugLogger.logError(' INVALID APP CREDENTIAL - REAL NUMBER ISSUE ');
            DebugLogger.logError(' Verifică dacă toate amprentele SHA sunt adăugate în Firebase Console');
          } else if (e.code == 'internal-error') {
            DebugLogger.logError(' INTERNAL ERROR - Verifică conexiunea la internet');
          }
          verificationFailed(e);
        },
        codeSent: (String verificationId, int? resendToken) {
          DebugLogger.logVerbose('✅ Cod SMS trimis cu succes');
          _verificationId = verificationId;
          codeSent(verificationId, resendToken);
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          DebugLogger.logVerbose('⏱️ Timeout pentru recuperarea automată a codului');
          _verificationId = verificationId;
          codeAutoRetrievalTimeout(verificationId);
        },
      );
    } catch (e) {
      rethrow;
    }
  }

  // Phone Number Authentication - Verify Code
  Future<UserCredential> verifyOTP(String otp) async {
    try {

      if (_verificationId == null) {
        throw Exception('Verification ID is null. Please request OTP again.');
      }

      // Create a PhoneAuthCredential with the code
      PhoneAuthCredential credential = PhoneAuthProvider.credential(
        verificationId: _verificationId!,
        smsCode: otp,
      );


      // Sign in with the credential
      final userCredential = await _auth.signInWithCredential(credential);

      return userCredential;
    } on FirebaseAuthException catch (e) {
      rethrow;
    } catch (e) {
      rethrow;
    }
  }
}