import 'package:flutter/foundation.dart';
import '../config/api_config.dart';
import '../models/api_response.dart';
import '../models/sms_log.dart';
import '../services/api_service.dart';
import '../utils/debug_logger.dart';
import 'auth/auth_service.dart';

/// Service for managing SMS logs
class SmsLogsService {
  /// Get SMS logs for the current salon with pagination
  static Future<ApiResponse<SmsLogPage>> getSmsLogs({
    int page = 0,
    int size = 10,
    SmsMessageType? messageType,
    SmsStatus? status,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<SmsLogPage>.error('No salon ID found');
      }

      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('📱 Getting SMS logs for salon: $salonId (page: $page, size: $size)');
      }

      // Build query parameters
      final queryParams = <String, dynamic>{
        'page': page.toString(),
        'size': size.toString(),
      };

      if (messageType != null) {
        queryParams['messageType'] = messageType.value;
      }

      if (status != null) {
        queryParams['status'] = status.value;
      }

      if (startDate != null) {
        queryParams['startDate'] = startDate.toIso8601String();
      }

      if (endDate != null) {
        queryParams['endDate'] = endDate.toIso8601String();
      }

      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('🔍 Query params: $queryParams');
      }
      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/salons/$salonId/sms-logs',
        queryParams: queryParams,
        fromJson: (data) {
          if (data == null) {
            return <String, dynamic>{};
          }
          return Map<String, dynamic>.from(data);
        },
      );

      if (response.success) {
        // Handle empty response or null data
        if (response.data == null || response.data!.isEmpty) {
          final emptySmsLogPage = SmsLogPage(
            content: [],
            totalElements: 0,
            totalPages: 0,
            number: 0,
            size: size,
            first: true,
            last: true,
          );
          return ApiResponse.success(emptySmsLogPage);
        }

        try {
          final smsLogPage = SmsLogPage.fromJson(response.data!);

          if (ApiConfig.enableLogging) {
            DebugLogger.logVerbose('✅ SMS logs retrieved successfully. Total: ${smsLogPage.totalElements}');
          }

          return ApiResponse.success(smsLogPage);
        } catch (parseError) {
          if (ApiConfig.enableLogging) {
            DebugLogger.logVerbose('❌ Error parsing SMS logs response: $parseError');
            DebugLogger.logVerbose('❌ Response data: ${response.data}');
          }
          return ApiResponse<SmsLogPage>.error('Failed to parse SMS logs response: $parseError');
        }
      }

      return ApiResponse<SmsLogPage>.error(response.error ?? 'Failed to get SMS logs');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('❌ Error getting SMS logs: $e');
      }
      return ApiResponse<SmsLogPage>.error('Failed to get SMS logs: $e');
    }
  }

  /// Get SMS statistics for the current salon
  static Future<ApiResponse<SmsStatistics>> getSmsStatistics() async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<SmsStatistics>.error('No salon ID found');
      }

      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('📊 Getting SMS statistics for salon: $salonId');
      }

      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/salons/$salonId/sms-logs/statistics',
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final statistics = SmsStatistics.fromJson(response.data!);
        
        if (ApiConfig.enableLogging) {
          DebugLogger.logVerbose('✅ SMS statistics retrieved successfully. Total SMS: ${statistics.totalSms}');
        }
        
        return ApiResponse.success(statistics);
      }

      return ApiResponse<SmsStatistics>.error(response.error ?? 'Failed to get SMS statistics');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('❌ Error getting SMS statistics: $e');
      }
      return ApiResponse<SmsStatistics>.error('Failed to get SMS statistics: $e');
    }
  }

  /// Get SMS logs for a specific appointment
  static Future<ApiResponse<List<SmsLog>>> getSmsLogsForAppointment(String appointmentId) async {
    try {
      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('📱 Getting SMS logs for appointment: $appointmentId');
      }

      final response = await ApiService.get<List<dynamic>>(
        '/api/salons/*/sms-logs/appointment/$appointmentId',
        fromJson: (data) => data as List<dynamic>,
      );

      if (response.success && response.data != null) {
        final smsLogs = response.data!
            .map((item) => SmsLog.fromJson(item as Map<String, dynamic>))
            .toList();
        
        if (ApiConfig.enableLogging) {
          DebugLogger.logVerbose('✅ SMS logs for appointment retrieved successfully. Count: ${smsLogs.length}');
        }
        
        return ApiResponse.success(smsLogs);
      }

      return ApiResponse<List<SmsLog>>.error(response.error ?? 'Failed to get SMS logs for appointment');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('❌ Error getting SMS logs for appointment: $e');
      }
      return ApiResponse<List<SmsLog>>.error('Failed to get SMS logs for appointment: $e');
    }
  }

  /// Get SMS logs for a specific client
  static Future<ApiResponse<SmsLogPage>> getSmsLogsForClient(
    String clientId, {
    int page = 0,
    int size = 10,
  }) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<SmsLogPage>.error('No salon ID found');
      }

      DebugLogger.logVerbose('📱 Getting SMS logs for client: $clientId (page: $page, size: $size)');
      DebugLogger.logVerbose('📱 Salon ID: $salonId');

      // Use the general SMS logs endpoint and filter by clientId
      // Backend doesn't have a specific client endpoint, so we fetch all and filter
      final queryParams = <String, dynamic>{
        'page': '0', // Always fetch from page 0 to get all messages
        'size': '1000', // Fetch a large number to get all client messages
      };

      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/salons/$salonId/sms-logs',
        queryParams: queryParams,
        fromJson: (data) {
          if (data == null) {
            return <String, dynamic>{};
          }
          return Map<String, dynamic>.from(data);
        },
      );

      DebugLogger.logVerbose('🔍 Response success: ${response.success}');

      if (response.success) {
        // Handle empty response or null data
        if (response.data == null || response.data!.isEmpty) {
          DebugLogger.logVerbose('⚠️ Response data is null/empty, returning empty page');
          final emptySmsLogPage = SmsLogPage(
            content: [],
            totalElements: 0,
            totalPages: 0,
            number: 0,
            size: size,
            first: true,
            last: true,
          );
          return ApiResponse.success(emptySmsLogPage);
        }

        try {
          final smsLogPage = SmsLogPage.fromJson(response.data!);

          // Filter messages by clientId
          final filteredContent = smsLogPage.content
              .where((smsLog) => smsLog.clientId == clientId)
              .toList();

          DebugLogger.logVerbose('📊 Total SMS logs: ${smsLogPage.totalElements}');
          DebugLogger.logVerbose('📊 Filtered for client: ${filteredContent.length}');

          // Paginate the filtered results
          final startIndex = page * size;
          final endIndex = (startIndex + size).clamp(0, filteredContent.length);
          final paginatedContent = startIndex < filteredContent.length
              ? filteredContent.sublist(startIndex, endIndex)
              : <SmsLog>[];

          final totalPages = (filteredContent.length / size).ceil();
          final isLast = page >= totalPages - 1 || totalPages == 0;

          final clientSmsLogPage = SmsLogPage(
            content: paginatedContent,
            totalElements: filteredContent.length,
            totalPages: totalPages,
            number: page,
            size: size,
            first: page == 0,
            last: isLast,
          );

          DebugLogger.logVerbose('✅ SMS logs for client retrieved successfully. Total: ${clientSmsLogPage.totalElements}');
          DebugLogger.logVerbose('✅ Content count: ${clientSmsLogPage.content.length}');

          return ApiResponse.success(clientSmsLogPage);
        } catch (parseError) {
          DebugLogger.logVerbose('❌ Error parsing SMS logs for client: $parseError');
          DebugLogger.logVerbose('❌ Response data: ${response.data}');

          // Return empty page instead of error for parse failures
          final emptySmsLogPage = SmsLogPage(
            content: [],
            totalElements: 0,
            totalPages: 0,
            number: 0,
            size: size,
            first: true,
            last: true,
          );

          return ApiResponse.success(emptySmsLogPage);
        }
      }

      DebugLogger.logVerbose('❌ Response not successful: ${response.error}');
      return ApiResponse<SmsLogPage>.error(response.error ?? 'Failed to get SMS logs for client');
    } catch (e) {
      DebugLogger.logVerbose('❌ Error getting SMS logs for client: $e');
      return ApiResponse<SmsLogPage>.error('Failed to get SMS logs for client: $e');
    }
  }
}
