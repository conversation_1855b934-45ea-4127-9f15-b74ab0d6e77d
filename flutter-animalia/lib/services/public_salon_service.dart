import '../models/api_response.dart';
import '../models/service.dart';
import '../utils/debug_logger.dart';
import 'api_service.dart';

/// Service for fetching public salon data without authentication
class PublicSalonService {
  /// Get active services for a salon by salon ID (public endpoint)
  static Future<ApiResponse<List<Service>>> getPublicServices(String salonId) async {
    try {
      DebugLogger.logShowcase('🌐 PublicSalonService: Getting public services for salon: $salonId');

      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/salons/public/$salonId/services',
        fromJson: (data) => data as Map<String, dynamic>,
        requiresAuth: false,
      );

      if (response.success && response.data != null) {
        // Backend returns ServiceListResponse with structure:
        // { services: [...], totalCount: X, activeCount: Y, inactiveCount: Z }
        final servicesList = response.data!['services'] as List<dynamic>;
        final services = servicesList
            .map((json) => Service.fromJson(json as Map<String, dynamic>))
            .toList();

        DebugLogger.logShowcase('✅ PublicSalonService: Retrieved ${services.length} services');
        return ApiResponse.success(services);
      } else {
        DebugLogger.logShowcase('❌ PublicSalonService: API error - ${response.error}');
        return ApiResponse.error(response.error ?? 'Failed to load services');
      }
    } catch (e) {
      DebugLogger.logShowcase('❌ PublicSalonService: Exception - $e');
      return ApiResponse.error('Error loading services: $e');
    }
  }
}

