import '../models/api_response.dart';
import 'api_service.dart';
import 'auth/auth_service.dart';

/// Service for managing appointment settings in the database
class AppointmentSettingsService {
  
  /// Get appointment settings for the current salon
  static Future<ApiResponse<Map<String, dynamic>>> getAppointmentSettings() async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<Map<String, dynamic>>.error('No salon selected');
      }

      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/salons/$salonId/appointment-settings',
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      return response;
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>.error('Failed to get appointment settings: $e');
    }
  }

  /// Update appointment settings for the current salon
  static Future<ApiResponse<Map<String, dynamic>>> updateAppointmentSettings({
    required bool autoFinalizeEnabled,
    required bool overdueNotificationsEnabled,
  }) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<Map<String, dynamic>>.error('No salon selected');
      }

      final body = {
        'autoFinalizeEnabled': autoFinalizeEnabled,
        'overdueNotificationsEnabled': overdueNotificationsEnabled,
      };

      final response = await ApiService.put<Map<String, dynamic>>(
        '/api/salons/$salonId/appointment-settings',
        body: body,
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      return response;
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>.error('Failed to update appointment settings: $e');
    }
  }

  /// Get default appointment settings
  static Map<String, dynamic> getDefaultSettings() {
    return {
      'autoFinalizeEnabled': false,
      'overdueNotificationsEnabled': true,
      'smsCompletionEnabled': true,
    };
  }
}
