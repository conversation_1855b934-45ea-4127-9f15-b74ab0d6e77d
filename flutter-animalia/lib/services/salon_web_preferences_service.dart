import 'dart:io';

import 'package:flutter/cupertino.dart';

import '../models/api_response.dart';
import '../models/salon_web_preferences.dart';
import '../services/api_service.dart';
import '../utils/debug_logger.dart';

/// Service for managing salon website preferences
class SalonWebPreferencesService {
  static const String _baseUrl = '/api/salon-web-preferences';

  /// Get web preferences for the current salon
  static Future<ApiResponse<SalonWebPreferences?>> getWebPreferences() async {
    try {
      DebugLogger.logShowcase('🌐 SalonWebPreferencesService: Getting web preferences');

      final response = await ApiService.get<SalonWebPreferences?>(
        _baseUrl,
        fromJson: (data) => data != null ? SalonWebPreferences.fromJson(data) : null,
      );
      debugPrint('Response from getWebPreferences: ${response.data}');

      if (response.success) {
        DebugLogger.logShowcase('✅ SalonWebPreferencesService: Retrieved web preferences');
        return response;
      } else {
        DebugLogger.logShowcase('❌ SalonWebPreferencesService: API error - ${response.error}');
        return response;
      }
    } catch (e) {
      DebugLogger.logShowcase('❌ SalonWebPreferencesService: Exception - $e');
      return ApiResponse.error('Error getting web preferences: $e');
    }
  }

  /// Save web preferences for the current salon
  static Future<ApiResponse<SalonWebPreferences>> saveWebPreferences(
    SalonWebPreferences preferences,
  ) async {
    try {
      DebugLogger.logShowcase('🌐 SalonWebPreferencesService: Saving web preferences');

      final response = await ApiService.post<SalonWebPreferences>(
        _baseUrl,
        body: preferences.toJson(),
        fromJson: (data) => SalonWebPreferences.fromJson(data),
      );

      if (response.success) {
        DebugLogger.logShowcase('✅ SalonWebPreferencesService: Saved web preferences');
        return response;
      } else {
        DebugLogger.logShowcase('❌ SalonWebPreferencesService: API error - ${response.error}');
        return response;
      }
    } catch (e) {
      DebugLogger.logShowcase('❌ SalonWebPreferencesService: Exception - $e');
      return ApiResponse.error('Error saving web preferences: $e');
    }
  }

  /// Update web preferences for the current salon
  static Future<ApiResponse<SalonWebPreferences>> updateWebPreferences(
    SalonWebPreferences preferences,
  ) async {
    try {
      DebugLogger.logShowcase('🌐 SalonWebPreferencesService: Updating web preferences');

      final response = await ApiService.put<SalonWebPreferences>(
        _baseUrl,
        body: preferences.toJson(),
        fromJson: (data) => SalonWebPreferences.fromJson(data),
      );

      if (response.success) {
        DebugLogger.logShowcase('✅ SalonWebPreferencesService: Updated web preferences');
        return response;
      } else {
        DebugLogger.logShowcase('❌ SalonWebPreferencesService: API error - ${response.error}');
        return response;
      }
    } catch (e) {
      DebugLogger.logShowcase('❌ SalonWebPreferencesService: Exception - $e');
      return ApiResponse.error('Error updating web preferences: $e');
    }
  }

  /// Delete web preferences for the current salon
  static Future<ApiResponse<String>> deleteWebPreferences() async {
    try {
      DebugLogger.logShowcase('🌐 SalonWebPreferencesService: Deleting web preferences');

      final response = await ApiService.delete<String>(
        _baseUrl,
        fromJson: (data) => data?.toString() ?? 'Web preferences deleted successfully',
      );

      if (response.success) {
        DebugLogger.logShowcase('✅ SalonWebPreferencesService: Deleted web preferences');
        return response;
      } else {
        DebugLogger.logShowcase('❌ SalonWebPreferencesService: API error - ${response.error}');
        return response;
      }
    } catch (e) {
      DebugLogger.logShowcase('❌ SalonWebPreferencesService: Exception - $e');
      return ApiResponse.error('Error deleting web preferences: $e');
    }
  }

  /// Get public web preferences by booking URL (no authentication required)
  static Future<ApiResponse<SalonWebPreferences?>> getPublicWebPreferences(String bookingUrl) async {
    try {
      DebugLogger.logShowcase('🌐 SalonWebPreferencesService: Getting public web preferences for URL: $bookingUrl');

      final response = await ApiService.getPublic<SalonWebPreferences?>(
        '$_baseUrl/public/$bookingUrl',
        fromJson: (data) => data != null ? SalonWebPreferences.fromJson(data) : null,
      );

      if (response.success) {
        DebugLogger.logShowcase('✅ SalonWebPreferencesService: Retrieved public web preferences');
        return response;
      } else {
        DebugLogger.logShowcase('❌ SalonWebPreferencesService: API error - ${response.error}');
        return response;
      }
    } catch (e) {
      DebugLogger.logShowcase('❌ SalonWebPreferencesService: Exception - $e');
      return ApiResponse.error('Error getting public web preferences: $e');
    }
  }

  /// Check if web preferences exist for the current salon
  static Future<ApiResponse<bool>> hasWebPreferences() async {
    try {
      DebugLogger.logShowcase('🌐 SalonWebPreferencesService: Checking web preferences existence');

      final response = await ApiService.get<bool>(
        '$_baseUrl/exists',
        fromJson: (data) => data as bool,
      );

      if (response.success) {
        DebugLogger.logShowcase('✅ SalonWebPreferencesService: Web preferences exist: ${response.data}');
        return response;
      } else {
        DebugLogger.logShowcase('❌ SalonWebPreferencesService: API error - ${response.error}');
        return response;
      }
    } catch (e) {
      DebugLogger.logShowcase('❌ SalonWebPreferencesService: Exception - $e');
      return ApiResponse.error('Error checking web preferences: $e');
    }
  }

  /// Upload photos for the website (placeholder for future implementation)
  static Future<ApiResponse<List<String>>> uploadWebsitePhotos(List<File> photos) async {
    try {
      DebugLogger.logShowcase('🌐 SalonWebPreferencesService: Uploading ${photos.length} photos');

      // TODO: Implement actual photo upload to backend
      // For now, simulate upload with placeholder URLs
      await Future.delayed(const Duration(seconds: 2));
      
      final uploadedUrls = photos.map((photo) => 
        'https://example.com/uploads/${DateTime.now().millisecondsSinceEpoch}_${photo.path.split('/').last}'
      ).toList();

      DebugLogger.logShowcase('✅ SalonWebPreferencesService: Uploaded photos (simulated)');
      return ApiResponse.success(uploadedUrls);
    } catch (e) {
      DebugLogger.logShowcase('❌ SalonWebPreferencesService: Photo upload error - $e');
      return ApiResponse.error('Error uploading photos: $e');
    }
  }

  /// Create a SalonWebPreferences object from form data
  static SalonWebPreferences createFromFormData({
    required String salonId,
    required String bookingWebsiteUrl,
    required String businessName,
    required String businessDescription,
    required String businessAddress,
    required String contactPhone,
    required String contactEmail,
    required String facebookLink,
    required String instagramLink,
    required String tiktokLink,
    String? logoUrl,
    String? primaryColor,
    required List<String> websitePhotos,
    required Map<String, Map<String, dynamic>> businessHours,
    required CancellationPolicy cancellationPolicy,
    required BookingAcceptance bookingAcceptance,
    bool onlineBookingEnabled = true,
    List<String> availableServiceIds = const [],
    String bookingPassword = '',
  }) {
    // Normalize bookingWebsiteUrl to be a slug: replace spaces with underscores,
    // remove disallowed chars, and lowercase. This ensures we send only the salon name slug.
    String normalizeSlug(String raw) {
      final s = raw.trim().replaceAll(' ', '_');
      final cleaned = s.replaceAll(RegExp(r'[^\w\-_]'), '');
      return cleaned.toLowerCase();
    }

    final slug = bookingWebsiteUrl.isNotEmpty ? normalizeSlug(bookingWebsiteUrl) : '';

    return SalonWebPreferences(
      salonId: salonId,
      bookingWebsiteSlug: slug,
      businessName: businessName,
      businessDescription: businessDescription,
      businessAddress: businessAddress,
      contactPhone: contactPhone,
      contactEmail: contactEmail,
      facebookLink: facebookLink,
      instagramLink: instagramLink,
      tiktokLink: tiktokLink,
      logoUrl: logoUrl ?? '',
      primaryColor: primaryColor ?? '#6366F1',
      websitePhotos: websitePhotos,
      businessHours: businessHours,
      cancellationPolicy: cancellationPolicy,
      bookingAcceptance: bookingAcceptance,
      onlineBookingEnabled: onlineBookingEnabled,
      availableServiceIds: availableServiceIds,
      bookingPassword: bookingPassword,
    );
  }
}
