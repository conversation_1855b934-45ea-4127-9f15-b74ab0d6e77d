import 'dart:async';
import 'dart:io';

import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/snack_bar_utils.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:provider/provider.dart';

import '../config/api_config.dart';
import '../providers/calendar_provider.dart';
import '../screens/appointments/calendar_screen.dart';
import '../screens/main_layout.dart';
import 'navigation_service.dart';
import 'api_service.dart';
import 'auth/auth_service.dart';

/// Simple notification handler for receiving and displaying notifications
/// All notification logic is handled by the backend
class NotificationHandler {
  static FirebaseMessaging? _messaging;
  static FlutterLocalNotificationsPlugin? _localNotifications;
  static bool _isInitialized = false;

  /// Initialize Firebase messaging and local notifications
  static Future<bool> initialize() async {
    try {
      if (_isInitialized) {
        if (kDebugMode) {
          DebugLogger.logVerbose('🔔 Notification handler already initialized');
        }
        return true;
      }

      if (kDebugMode && ApiConfig.enableVerboseLogging) {
        DebugLogger.logVerbose('🔔 Initializing notification handler...');
      }

      // Initialize Firebase Messaging
      _messaging = FirebaseMessaging.instance;

      // Request permission for notifications
      final settings = await _messaging!.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
      );

      if (settings.authorizationStatus != AuthorizationStatus.authorized) {
        if (kDebugMode) {
          DebugLogger.logVerbose('⚠️ Notification permission denied');
        }
        return false;
      }

      // Initialize local notifications
      await _initializeLocalNotifications();

      // Get FCM token and register with backend
      await _registerFCMToken();

      // Set up message handlers
      _setupMessageHandlers();

      _isInitialized = true;
      if (kDebugMode) {
        DebugLogger.logVerbose('✅ Notification handler initialized successfully');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        DebugLogger.logVerbose('❌ Error initializing notification handler: $e');
      }
      return false;
    }
  }

  /// Initialize local notifications for foreground display
  static Future<void> _initializeLocalNotifications() async {
    _localNotifications = FlutterLocalNotificationsPlugin();

    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications!.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
  }

  /// Get FCM token and register with backend
  static Future<void> _registerFCMToken() async {
    try {
      final token = await _messaging!.getToken();
      if (token != null) {
        if (kDebugMode && ApiConfig.enableVerboseLogging) {
          DebugLogger.logVerbose('📱 FCM Token: ${token.substring(0, 20)}...');
        }

        // Send token to backend
        await _sendTokenToBackend(token);

        // Listen for token refresh
        _messaging!.onTokenRefresh.listen(_sendTokenToBackend);
      }
    } catch (e) {
      if (kDebugMode) {
        DebugLogger.logVerbose('❌ Error registering FCM token: $e');
      }
    }
  }

  /// Send FCM token to backend
  static Future<void> _sendTokenToBackend(String token) async {
    try {
      if (kDebugMode) {
        DebugLogger.logVerbose('🔄 Attempting to register FCM token with backend...');
      }

      final userId = await AuthService.getCurrentUserId();
      if (userId == null) {
        if (kDebugMode) {
          DebugLogger.logVerbose('⚠️ No user ID available, skipping token registration');
          DebugLogger.logVerbose('   This is normal if user is not logged in yet');
          DebugLogger.logVerbose('   Token will be registered after successful login');
        }
        return;
      }

      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        if (kDebugMode) {
          DebugLogger.logVerbose('⚠️ No salon ID available, registering token without salon');
          DebugLogger.logVerbose('   Token will be re-registered when user joins/creates a salon');
        }
        // Continue with registration but without salonId - backend should handle this case
      }

      if (kDebugMode) {
        DebugLogger.logVerbose('🔑 User ID found: $userId');
        DebugLogger.logVerbose('📱 Registering FCM token: ${token.substring(0, 20)}...');
      }

      // Determine platform
      String platformName;
      if (kIsWeb) {
        platformName = 'web';
      } else if (Platform.isIOS) {
        platformName = 'ios';
      } else if (Platform.isAndroid) {
        platformName = 'android';
      } else {
        platformName = 'unknown';
      }

      // Build request body - salonId is optional for new users
      final body = <String, dynamic>{
        'userId': userId,
        'token': token,
        'platform': platformName,
      };

      // Only include salonId if available
      if (salonId != null) {
        body['salonId'] = salonId;
        if (kDebugMode && ApiConfig.enableVerboseLogging) {
          DebugLogger.logVerbose('🏢 Including salon ID in registration: $salonId');
        }
      } else {
        if (kDebugMode && ApiConfig.enableVerboseLogging) {
          DebugLogger.logVerbose('🆕 Registering token for new user without salon');
        }
      }

      final response = await ApiService.post<void>(
        '/api/notifications/fcm-token',
        body: body,
      );

      if (response.success) {
        if (kDebugMode) {
          DebugLogger.logVerbose('✅ FCM token registered successfully with backend');
        }
      } else {
        if (kDebugMode) {
          DebugLogger.logVerbose('❌ Failed to register FCM token: ${response.error}');
          DebugLogger.logVerbose('   Status code: ${response.statusCode}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        DebugLogger.logVerbose('❌ Error sending token to backend: $e');
        DebugLogger.logVerbose('   This might be due to network issues or backend unavailability');
      }
    }
  }

  /// Set up Firebase message handlers
  static void _setupMessageHandlers() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background message taps
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageTap);

    // Handle app launch from terminated state
    _messaging!.getInitialMessage().then((message) {
      if (message != null) {
        _handleMessageTap(message);
      }
    });
  }

  /// Handle messages received while app is in foreground
  static Future<void> _handleForegroundMessage(RemoteMessage message) async {
    try {
      if (kDebugMode) {
        DebugLogger.logVerbose('🔔 Foreground message: ${message.notification?.title}');
      }

      // Show local notification
      await _showLocalNotification(message);

      // Show in-app notification if needed
      _showInAppNotification(message);

      // Refresh calendar data if this notification relates to appointments
      await refreshCalendarFromData(message.data);
    } catch (e) {
      if (kDebugMode) {
        DebugLogger.logVerbose('❌ Error handling foreground message: $e');
      }
    }
  }

  /// Handle message tap (background or terminated)
  static void _handleMessageTap(RemoteMessage message) {
    try {
      if (kDebugMode) {
        DebugLogger.logVerbose('👆 Message tapped: ${message.data}');
      }

      // Refresh calendar data if needed
      unawaited(refreshCalendarFromData(message.data));

      // Navigate based on notification data
      _navigateFromNotification(message.data);
    } catch (e) {
      if (kDebugMode) {
        DebugLogger.logVerbose('❌ Error handling message tap: $e');
      }
    }
  }

  /// Show local notification for foreground messages
  static Future<void> _showLocalNotification(RemoteMessage message) async {
    try {
      const androidDetails = AndroidNotificationDetails(
        'appointment_notifications',
        'Appointment Notifications',
        channelDescription: 'Notifications for appointment updates',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications!.show(
        message.hashCode,
        message.notification?.title ?? 'Animalia',
        message.notification?.body ?? '',
        details,
        payload: message.data.toString(),
      );
      if (kDebugMode) {
        DebugLogger.logVerbose('📲 Local notification displayed: '
            '${message.notification?.title ?? ''}');
      }
    } catch (e) {
      if (kDebugMode) {
        DebugLogger.logVerbose('❌ Error showing local notification: $e');
      }
    }
  }

  /// Show in-app notification overlay
  static void _showInAppNotification(RemoteMessage message) {
    final context = NavigationService.navigatorKey.currentContext;
    if (context == null) return;

    // Show snackbar for in-app notification
    showTopSnackBar(context, 
      SnackBar(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              message.notification?.title ?? 'Notificare',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            if (message.notification?.body != null)
              Text(message.notification!.body!),
          ],
        ),
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'Vezi',
          onPressed: () => _navigateFromNotification(message.data),
        ),
      ),
    );
    if (kDebugMode) {
      DebugLogger.logVerbose('📢 In-app notification shown: '
          '${message.notification?.title ?? ''}');
    }
  }

  /// Refresh calendar provider based on notification data
  @visibleForTesting
  static Future<void> refreshCalendarFromData(Map<String, dynamic> data) async {
    // Backend sends eventType, but some notifications might use type
    final type = data['eventType'] as String? ?? data['type'] as String?;

    if (kDebugMode) {
      DebugLogger.logVerbose('🔄 NOTIFICATION REFRESH: Starting calendar refresh for notification');
      DebugLogger.logVerbose('   Notification type: $type');
      DebugLogger.logVerbose('   Notification data: $data');
    }

    final context = NavigationService.navigatorKey.currentContext;
    if (context == null) {
      if (kDebugMode) {
        DebugLogger.logVerbose('❌ NOTIFICATION REFRESH: NavigationService context is null, cannot refresh calendar');
      }
      return;
    }

    try {
      final provider = Provider.of<CalendarProvider>(context, listen: false);

      if (kDebugMode) {
        DebugLogger.logVerbose('🔄 NOTIFICATION REFRESH: CalendarProvider found, starting refresh for type: $type');
      }

      // Extract appointment dates from notification data (if available)
      final appointmentDates = extractAppointmentDates(data);
      final currentDate = DateTime.now();

      // Create a set of unique dates to refresh (avoid duplicates)
      final datesToRefresh = <DateTime>{};

      // Add appointment-specific dates if found
      datesToRefresh.addAll(appointmentDates);

      // Always refresh the current week to ensure the currently viewed calendar is updated
      final today = DateTime(currentDate.year, currentDate.month, currentDate.day);
      final weekStart = today.subtract(Duration(days: today.weekday - 1)); // Monday

      // Add the entire current week (Monday to Sunday)
      for (int i = 0; i < 7; i++) {
        final weekDay = weekStart.add(Duration(days: i));
        datesToRefresh.add(weekDay);
      }

      // Also add next week to catch appointments that might be scheduled for next week
      for (int i = 7; i < 14; i++) {
        final nextWeekDay = weekStart.add(Duration(days: i));
        datesToRefresh.add(nextWeekDay);
      }

      // Refresh each unique date
      for (final date in datesToRefresh) {
        await provider.fetchAppointmentsForDate(date, forceRefresh: true);
        if (kDebugMode) {
          DebugLogger.logVerbose('✅ Calendar refreshed for date: ${date.toIso8601String().split('T')[0]}');
        }
      }

      // Also refresh blocked times for the dates to ensure complete calendar sync
      for (final date in datesToRefresh) {
        await provider.fetchBlockedTimesForDate(date, forceRefresh: true);
      }

      if (kDebugMode) {
        DebugLogger.logVerbose('✅ NOTIFICATION REFRESH: Calendar refresh completed for ${datesToRefresh.length} dates');
        DebugLogger.logVerbose('✅ NOTIFICATION REFRESH: All calendar data should now be updated!');
      }

    } catch (e) {
      if (kDebugMode) {
        DebugLogger.logVerbose('❌ NOTIFICATION REFRESH: Error refreshing calendar from notification: $e');
      }
    }
  }

  /// Check if notification type requires calendar refresh
  @visibleForTesting
  static bool isAppointmentNotification(String? type) {
    if (type == null) return false;

    const appointmentTypes = {
      'appointment_created',
      'appointment_updated',
      'appointment_cancelled',
      'appointment_completed',
      'appointment_reminder',
      'new_appointment',
      'appointment_rescheduled',
      'appointment_scheduled', // Backend event type
      'APPOINTMENT_SCHEDULED', // Backend event type variant
      'APPOINTMENT_RESCHEDULED', // Backend event type variant
    };

    return appointmentTypes.contains(type);
  }

  /// Extract appointment dates from notification data
  @visibleForTesting
  static List<DateTime> extractAppointmentDates(Map<String, dynamic> data) {
    final dates = <DateTime>[];

    // Try different possible date fields in the notification data
    final dateFields = [
      'appointmentTime',
      'startTime',
      'appointmentDate',
      'newDate',
      'oldDate',
    ];

    for (final field in dateFields) {
      final dateStr = data[field] as String?;
      if (dateStr != null && dateStr.isNotEmpty) {
        try {
          final date = DateTime.parse(dateStr);
          // Normalize to date only (remove time component for date comparison)
          final normalizedDate = DateTime(date.year, date.month, date.day);
          if (!dates.any((d) => d.isAtSameMomentAs(normalizedDate))) {
            dates.add(normalizedDate);
          }
        } catch (e) {
          if (kDebugMode) {
            DebugLogger.logVerbose('⚠️ Failed to parse date from field $field: $dateStr');
          }
        }
      }
    }

    return dates;
  }

  /// Normalize notification type to a consistent format
  static String? _normalizeNotificationType(String? rawType) {
    if (rawType == null) return null;

    // Convert to lowercase for consistent comparison
    final normalizedType = rawType.toLowerCase().trim();

    // Map various backend notification types to consistent frontend types
    switch (normalizedType) {
      case 'appointment_scheduled':
      case 'appointment_created':
      case 'new_appointment':
        return 'appointment_created';

      case 'appointment_rescheduled':
      case 'appointment_updated':
        return 'appointment_updated';

      case 'appointment_cancelled':
      case 'appointment_canceled': // Handle both spellings
        return 'appointment_cancelled';

      case 'appointment_completed':
        return 'appointment_completed';

      case 'appointment_reminder':
        return 'appointment_reminder';

      case 'client_message':
        return 'client_message';

      // Handle uppercase variants from backend
      case 'appointment_scheduled':
      case 'appointment_rescheduled':
        return normalizedType;

      default:
        // Return the original type if no mapping found
        return rawType;
    }
  }

  /// Navigate to appropriate screen based on notification data
  static void _navigateFromNotification(Map<String, dynamic> data) {
    final context = NavigationService.navigatorKey.currentContext;
    if (context == null) return;

    try {
      final rawType = (data['type'] ?? data['eventType']) as String?;
      final type = _normalizeNotificationType(rawType);
      final appointmentId = data['appointmentId'] as String?;
      final clientId = data['clientId'] as String?;
      final startTimeStr =
          (data['appointmentTime'] ?? data['startTime']) as String?;
      DateTime? startTime;
      if (startTimeStr != null && startTimeStr.isNotEmpty) {
        try {
          startTime = DateTime.parse(startTimeStr);
        } catch (_) {}
      }

      switch (type) {
        case 'appointment_created':
        case 'appointment_updated':
        case 'appointment_cancelled':
        case 'appointment_completed':
        case 'appointment_reminder':
        case 'appointment_scheduled':
        case 'appointment_rescheduled':
          if (appointmentId != null) {
            if (kDebugMode) {
              DebugLogger.logVerbose('➡️ Navigate to appointment details: $appointmentId');
            }
            // Use MainLayout to ensure calendar tab is shown
            MainLayout.openAppointmentInCalendar(appointmentId);
          } else {
            // Fallback to just opening the calendar screen
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (_) => CalendarScreen(initialDate: startTime),
              ),
            );
          }
          break;

        case 'client_message':
          if (clientId != null) {
            if (kDebugMode) {
              DebugLogger.logVerbose('➡️ Navigate to client details: $clientId');
            }
            Navigator.of(context).pushNamed(
              '/client-details',
              arguments: clientId,
            );
          }
          break;

        default:
          // Navigate to dashboard for unknown types
          if (kDebugMode) {
            DebugLogger.logVerbose('➡️ Navigate to dashboard (unknown type: $type)');
          }
          Navigator.of(context).pushNamed('/dashboard');
      }
    } catch (e) {
      if (kDebugMode) {
        DebugLogger.logVerbose('❌ Error navigating from notification: $e');
      }
    }
  }

  /// Handle notification tap from local notifications
  static void _onNotificationTapped(NotificationResponse response) {
    try {
      if (kDebugMode) {
        DebugLogger.logVerbose('👆 Local notification tapped: ${response.payload}');
      }

      // Parse payload and navigate
      if (response.payload != null) {
        // Parse the payload data and navigate accordingly
        // This would contain the same data as Firebase message
      }
    } catch (e) {
      if (kDebugMode) {
        DebugLogger.logVerbose('❌ Error handling notification tap: $e');
      }
    }
  }

  /// Get current FCM token
  static Future<String?> getCurrentToken() async {
    try {
      return await _messaging?.getToken();
    } catch (e) {
      if (kDebugMode) {
        DebugLogger.logVerbose('❌ Error getting FCM token: $e');
      }
      return null;
    }
  }

  /// Check if notifications are enabled
  static Future<bool> areNotificationsEnabled() async {
    try {
      final settings = await _messaging?.getNotificationSettings();
      return settings?.authorizationStatus == AuthorizationStatus.authorized;
    } catch (e) {
      if (kDebugMode) {
        DebugLogger.logVerbose('❌ Error checking notification status: $e');
      }
      return false;
    }
  }

  /// Request notification permissions
  static Future<bool> requestPermissions() async {
    try {
      final settings = await _messaging?.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );

      return settings?.authorizationStatus == AuthorizationStatus.authorized;
    } catch (e) {
      if (kDebugMode) {
        DebugLogger.logVerbose('❌ Error requesting notification permissions: $e');
      }
      return false;
    }
  }

  /// Unregister FCM token (for logout)
  static Future<void> unregisterToken() async {
    try {
      final token = await _messaging?.getToken();
      if (token == null) return;

      if (kDebugMode) {
        DebugLogger.logVerbose('📤 Unregistering FCM token...');
      }

      final response = await ApiService.delete<void>(
        '/api/users/fcm-token',
        body: {'token': token},
      );

      if (response.success) {
        if (kDebugMode) {
          DebugLogger.logVerbose('✅ FCM token unregistered successfully');
        }
      } else {
        if (kDebugMode) {
          DebugLogger.logVerbose('❌ Failed to unregister FCM token: ${response.error}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        DebugLogger.logVerbose('❌ Error unregistering FCM token: $e');
      }
    }

    _isInitialized = false;
  }


  /// Get current FCM token
  static Future<String?> getFcmToken() async {
    try {
      return await _messaging?.getToken();
    } catch (e) {
      if (kDebugMode) {
        DebugLogger.logVerbose('❌ Error getting FCM token: $e');
      }
      return null;
    }
  }

  /// Manually register FCM token (call after login)
  static Future<void> registerTokenAfterLogin() async {
    if (!_isInitialized) {
      if (kDebugMode) {
        DebugLogger.logVerbose('⚠️ NotificationHandler not initialized, attempting to initialize first...');
      }

      // Try to initialize if not already done
      final initSuccess = await initialize();
      if (!initSuccess) {
        if (kDebugMode) {
          DebugLogger.logVerbose('❌ Failed to initialize NotificationHandler, cannot register token');
        }
        return;
      }
    }

    try {
      final token = await _messaging?.getToken();
      if (token != null) {
        if (kDebugMode) {
          DebugLogger.logVerbose('🔄 Manually registering FCM token after login...');
        }
        await _sendTokenToBackend(token);
      } else {
        if (kDebugMode) {
          DebugLogger.logVerbose('⚠️ No FCM token available for registration');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        DebugLogger.logVerbose('❌ Error in manual token registration: $e');
      }
    }
  }

  /// Re-register FCM token when salon changes (call after salon switch/join/create)
  static Future<void> registerTokenAfterSalonChange({String? reason}) async {
    if (!_isInitialized) {
      if (kDebugMode) {
        DebugLogger.logVerbose('⚠️ NotificationHandler not initialized, cannot re-register token for salon change');
      }
      return;
    }

    try {
      final token = await _messaging?.getToken();
      if (token != null) {
        if (kDebugMode) {
          DebugLogger.logVerbose('🔄 Re-registering FCM token after salon change${reason != null ? ' - $reason' : ''}...');
        }
        await _sendTokenToBackend(token);
      } else {
        if (kDebugMode) {
          DebugLogger.logVerbose('⚠️ No FCM token available for salon change re-registration');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        DebugLogger.logVerbose('❌ Error in salon change token re-registration: $e');
      }
    }
  }

  /// Refresh calendar when app comes to foreground (useful after background notifications)
  static Future<void> refreshCalendarOnForeground() async {
    final context = NavigationService.navigatorKey.currentContext;
    if (context == null) return;

    try {
      final provider = Provider.of<CalendarProvider>(context, listen: false);

      if (kDebugMode) {
        DebugLogger.logVerbose('🔄 Refreshing calendar on app foreground...');
      }

      // Refresh current date to ensure latest appointment data
      final currentDate = DateTime.now();
      final today = DateTime(currentDate.year, currentDate.month, currentDate.day);

      await provider.fetchAppointmentsForDate(today, forceRefresh: true);
      await provider.fetchBlockedTimesForDate(today, forceRefresh: true);

      if (kDebugMode) {
        DebugLogger.logVerbose('✅ Calendar refreshed on foreground');
      }

    } catch (e) {
      if (kDebugMode) {
        DebugLogger.logVerbose('❌ Error refreshing calendar on foreground: $e');
      }
    }
  }
}



/// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  if (kDebugMode) {
    DebugLogger.logVerbose('🔔 Background message: ${message.notification?.title}');
  }

  // For appointment-related notifications, we'll refresh calendar when app comes to foreground
  // The actual refresh will happen in _handleMessageTap when user taps the notification
  // or in _handleForegroundMessage when app is brought to foreground

  // Background messages are automatically handled by the system
  // No additional processing needed here as calendar refresh will happen when app becomes active
}
