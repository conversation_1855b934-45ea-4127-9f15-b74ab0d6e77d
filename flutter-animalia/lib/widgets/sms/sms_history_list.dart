import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../models/sms_log.dart';
import '../../models/appointment.dart';
import '../../services/sms_logs_service.dart';
import '../../providers/calendar_provider.dart';
import '../../utils/debug_logger.dart';
import '../../utils/genie_animation.dart';
import '../../l10n/app_localizations.dart';
import '../dialogs/appointment_details_dialog.dart';

/// Reusable widget showing SMS history with appointment navigation
class SmsHistoryList extends StatefulWidget {
  const SmsHistoryList({super.key});

  @override
  State<SmsHistoryList> createState() => _SmsHistoryListState();
}

class _SmsHistoryListState extends State<SmsHistoryList> {
  final ScrollController _scrollController = ScrollController();
  final List<SmsLog> _smsLogs = [];
  bool _isLoading = false;
  bool _hasMore = true;
  int _currentPage = 0;
  final int _pageSize = 10;
  String? _error;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadSmsLogs();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoading && _hasMore) {
        _loadMoreSmsLogs();
      }
    }
  }

  Future<void> _loadSmsLogs() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _error = null;
      _currentPage = 0;
      _smsLogs.clear();
      _hasMore = true;
    });

    try {
      final response = await SmsLogsService.getSmsLogs(
        page: _currentPage,
        size: _pageSize,
      );

      if (response.success && response.data != null) {
        DebugLogger.logVerbose('SMS Logs loaded: ${response.data!.content.length} items, total: ${response.data!.totalElements}');
        setState(() {
          _smsLogs.addAll(response.data!.content);
          _hasMore = !response.data!.last;
          _currentPage++;
        });
      } else {
        DebugLogger.logVerbose('SMS Logs error: ${response.error}');
        setState(() {
          _error = response.error ?? 'Eroare la încărcarea SMS-urilor';
        });
      }
    } catch (e) {
      DebugLogger.logVerbose('SMS Logs exception: $e');
      setState(() {
        _error = 'Eroare la încărcarea SMS-urilor: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreSmsLogs() async {
    if (_isLoading || !_hasMore) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await SmsLogsService.getSmsLogs(
        page: _currentPage,
        size: _pageSize,
      );

      if (response.success && response.data != null) {
        setState(() {
          _smsLogs.addAll(response.data!.content);
          _hasMore = !response.data!.last;
          _currentPage++;
        });
      }
    } catch (e) {
      DebugLogger.logVerbose('Error loading more SMS logs: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Open appointment details when message has an associated appointment
  Future<void> _openAppointmentDetails(SmsLog smsLog) async {
    // Normalize phone number for comparison (remove spaces, dashes, etc.)
    String normalizePhone(String phone) {
      return phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    }

    // Show loading indicator from the start
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);

      Appointment? appointment;

      // STEP 1: Try to find by appointmentId if available
      if (smsLog.appointmentId != null) {
        try {
          appointment = calendarProvider.appointments.firstWhere(
            (apt) => apt.id == smsLog.appointmentId,
          );
          DebugLogger.logVerbose('✅ Appointment found by ID in loaded data');
        } catch (e) {
          DebugLogger.logVerbose('📅 Appointment not in loaded data, fetching from server...');
        }

        // If not found, load from server
        if (appointment == null && smsLog.appointmentDate != null) {
          try {
            await calendarProvider.fetchAppointmentsForDate(
              smsLog.appointmentDate!,
              forceRefresh: true,
            );

            // Give provider time to process and update the appointments list
            await Future.delayed(const Duration(milliseconds: 300));

            // Try again after loading
            try {
              appointment = calendarProvider.appointments.firstWhere(
                (apt) => apt.id == smsLog.appointmentId,
              );
              DebugLogger.logVerbose('✅ Appointment loaded from server by ID');
            } catch (e) {
              DebugLogger.logVerbose('❌ Appointment still not found after loading');
            }
          } catch (loadError) {
            DebugLogger.logVerbose('❌ Error loading appointments: $loadError');
          }
        }
      }

      // STEP 2: If appointment not found by ID, try to find by phone number
      if (appointment == null && smsLog.phoneNumber.isNotEmpty) {
        DebugLogger.logVerbose('🔍 Trying to find appointment by phone number: ${smsLog.phoneNumber}');

        // Determine which date to search
        DateTime searchDate = smsLog.appointmentDate ?? smsLog.sentAt;

        // Load appointments for the search date
        try {
          await calendarProvider.fetchAppointmentsForDate(
            searchDate,
            forceRefresh: true,
          );
          await Future.delayed(const Duration(milliseconds: 300));

          final normalizedSmsPhone = normalizePhone(smsLog.phoneNumber);

          // Try to find appointment by phone number
          try {
            appointment = calendarProvider.appointments.firstWhere(
              (apt) {
                if (apt.clientPhone.isEmpty) return false;
                final aptPhone = normalizePhone(apt.clientPhone);

                // Check if phones match and dates are close (within 7 days)
                final dateMatch = apt.startTime.difference(searchDate).abs().inDays <= 7;
                final phoneMatch = aptPhone.contains(normalizedSmsPhone.substring(normalizedSmsPhone.length > 6 ? normalizedSmsPhone.length - 6 : 0)) ||
                                   normalizedSmsPhone.contains(aptPhone.substring(aptPhone.length > 6 ? aptPhone.length - 6 : 0));

                return phoneMatch && dateMatch;
              },
            );

            if (appointment != null) {
              DebugLogger.logVerbose('✅ Appointment found by phone number match');
            }
          } catch (e) {
            // Not found, will continue to next step
          }
        } catch (e) {
          DebugLogger.logVerbose('❌ Could not find appointment by phone: $e');
        }

        // STEP 3: Try searching in nearby dates (±3 days)
        if (appointment == null) {
          DebugLogger.logVerbose('🔍 Searching in nearby dates...');

          for (int dayOffset in [-3, -2, -1, 1, 2, 3]) {
            final nearbyDate = searchDate.add(Duration(days: dayOffset));

            try {
              await calendarProvider.fetchAppointmentsForDate(
                nearbyDate,
                forceRefresh: false, // Use cache if available
              );
              await Future.delayed(const Duration(milliseconds: 100));

              final normalizedSmsPhone = normalizePhone(smsLog.phoneNumber);

              try {
                appointment = calendarProvider.appointments.firstWhere(
                  (apt) {
                    if (apt.clientPhone.isEmpty) return false;
                    final aptPhone = normalizePhone(apt.clientPhone);

                    // Match last 6 digits of phone number
                    final phoneMatch = aptPhone.contains(normalizedSmsPhone.substring(normalizedSmsPhone.length > 6 ? normalizedSmsPhone.length - 6 : 0)) ||
                                       normalizedSmsPhone.contains(aptPhone.substring(aptPhone.length > 6 ? aptPhone.length - 6 : 0));

                    return phoneMatch && apt.startTime.year == nearbyDate.year &&
                           apt.startTime.month == nearbyDate.month &&
                           apt.startTime.day == nearbyDate.day;
                  },
                );

                if (appointment != null) {
                  DebugLogger.logVerbose('✅ Appointment found in nearby date: $nearbyDate');
                  break;
                }
              } catch (e) {
                // Continue searching
              }
            } catch (e) {
              // Continue searching
            }
          }
        }
      }

      // Close loading dialog
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (appointment != null && mounted) {
        GenieAnimation.showGenieDialog(
          context: context,
          dialog: AppointmentDetailsDialog(appointment: appointment),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Programarea nu a fost găsită. Asigură-te că programarea există în calendar.'),
            backgroundColor: Theme.of(context).colorScheme.error,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ Error loading appointment: $e');

      // Close loading dialog if still open
      if (mounted && Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Eroare la încărcarea programării: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      return _buildErrorWidget();
    }

    if (_smsLogs.isEmpty && !_isLoading) {
      return _buildEmptyWidget();
    }

    return RefreshIndicator(
      onRefresh: _loadSmsLogs,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        itemCount: _smsLogs.length + (_hasMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _smsLogs.length) {
            return _buildLoadingIndicator();
          }

          final smsLog = _smsLogs[index];
          return _buildSmsLogItem(smsLog);
        },
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.red.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.error, color: Colors.red),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                _error!,
                style: TextStyle(color: Colors.red[700]),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.sms_outlined,
            size: 48,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'Niciun SMS trimis',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'SMS-urile trimise vor apărea aici',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      alignment: Alignment.center,
      child: const CircularProgressIndicator(),
    );
  }

  Widget _buildSmsLogItem(SmsLog smsLog) {
    final dateFormat = DateFormat('dd.MM.yyyy HH:mm');
    final isDark = Theme.of(context).brightness == Brightness.dark;

    // Determine if message is from client (incoming) or from salon (outgoing)
    final bool isFromClient = smsLog.direction == SmsDirection.incoming;

    return GestureDetector(
      onTap: () => _openAppointmentDetails(smsLog),
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          // Use chat-style colors: white for received (from client), green for sent (from salon)
          gradient: LinearGradient(
            colors: isFromClient
              ? isDark
                ? [
                    const Color(0xFF2A2A2A),
                    const Color(0xFF1F1F1F),
                  ]
                : [
                    Colors.white,
                    const Color(0xFFFAFAFA),
                  ]
              : isDark
                ? [
                    Theme.of(context).colorScheme.primary.withOpacity(0.8),
                    Theme.of(context).colorScheme.primary.withOpacity(0.6),
                  ]
                : [
                    const Color(0xFFDCF8C6), // WhatsApp sent message green
                    const Color(0xFFD0F4B8),
                  ],
          ),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isFromClient
              ? (isDark ? Colors.grey[700]! : Colors.grey[300]!)
              : (isDark
                  ? Theme.of(context).colorScheme.primary.withOpacity(0.5)
                  : const Color(0xFFB8E7A0)),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // First row: Badges only
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: smsLog.getTypeColor().withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        smsLog.getTypeIcon(),
                        size: 14,
                        color: smsLog.getTypeColor(),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        smsLog.messageType.displayName,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: smsLog.getTypeColor(),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                if (smsLog.isWhatsApp)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                    decoration: BoxDecoration(
                      color: const Color(0xFF25D366).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.chat_bubble,
                          size: 14,
                          color: const Color(0xFF25D366),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'WhatsApp',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: const Color(0xFF25D366),
                          ),
                        ),
                      ],
                    ),
                  ),
                if (smsLog.isWhatsApp) const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: isFromClient
                      ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                      : (isDark
                          ? Colors.white.withOpacity(0.2)
                          : const Color(0xFF075E54).withOpacity(0.15)),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isFromClient
                        ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)
                        : (isDark
                            ? Colors.white.withOpacity(0.3)
                            : const Color(0xFF075E54).withOpacity(0.3)),
                      width: 0.5,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.sms,
                        size: 12,
                        color: isFromClient
                          ? Theme.of(context).colorScheme.primary
                          : (isDark ? Colors.white70 : const Color(0xFF075E54)),
                      ),
                      const SizedBox(width: 2),
                      Text(
                        smsLog.smsUnitsDisplay,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                          color: isFromClient
                            ? Theme.of(context).colorScheme.primary
                            : (isDark ? Colors.white70 : const Color(0xFF075E54)),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // Second row: Date
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 14,
                  color: isFromClient
                    ? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6)
                    : (isDark ? Colors.white70 : const Color(0xFF303030).withOpacity(0.7)),
                ),
                const SizedBox(width: 4),
                Text(
                  dateFormat.format(smsLog.sentAt),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: isFromClient
                      ? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6)
                      : (isDark ? Colors.white70 : const Color(0xFF303030).withOpacity(0.7)),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // Third row: Client name and phone
            Row(
              children: [
                Icon(
                  Icons.person,
                  size: 16,
                  color: isFromClient
                    ? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6)
                    : (isDark ? Colors.white70 : const Color(0xFF303030).withOpacity(0.7)),
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    smsLog.displayName,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: isFromClient
                        ? Theme.of(context).colorScheme.onSurface
                        : (isDark ? Colors.white : const Color(0xFF303030)),
                    ),
                  ),
                ),
                Icon(
                  Icons.phone,
                  size: 16,
                  color: isFromClient
                    ? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6)
                    : (isDark ? Colors.white70 : const Color(0xFF303030).withOpacity(0.7)),
                ),
                const SizedBox(width: 4),
                Text(
                  smsLog.formattedPhoneNumber,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: isFromClient
                      ? Theme.of(context).colorScheme.onSurface
                      : (isDark ? Colors.white : const Color(0xFF303030)),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // Message content
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isFromClient
                  ? Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5)
                  : (isDark
                      ? Colors.white.withOpacity(0.1)
                      : const Color(0xFF075E54).withOpacity(0.08)),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    smsLog.displayMessageContent,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontStyle: FontStyle.italic,
                      color: isFromClient
                        ? Theme.of(context).colorScheme.onSurface
                        : (isDark ? Colors.white : const Color(0xFF303030)),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        smsLog.characterCountDisplay,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontSize: 10,
                          color: isFromClient
                            ? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5)
                            : (isDark ? Colors.white60 : const Color(0xFF303030).withOpacity(0.6)),
                        ),
                      ),
                      if (smsLog.smsUnits > 1)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                          decoration: BoxDecoration(
                            color: Colors.orange.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(
                              color: Colors.orange.withValues(alpha: 0.3),
                              width: 0.5,
                            ),
                          ),
                          child: Text(
                            'Mesaj lung',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              fontSize: 9,
                              fontWeight: FontWeight.w500,
                              color: Colors.orange[700],
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

