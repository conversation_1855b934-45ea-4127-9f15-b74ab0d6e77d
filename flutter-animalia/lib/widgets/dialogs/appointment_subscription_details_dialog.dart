import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/appointment_subscription.dart';
import '../../services/appointment_subscription_service.dart';
import '../../utils/snack_bar_utils.dart';

class AppointmentSubscriptionDetailsDialog extends StatefulWidget {
  final AppointmentSubscription subscription;
  final String salonId;
  final VoidCallback? onSubscriptionChanged;

  const AppointmentSubscriptionDetailsDialog({
    super.key,
    required this.subscription,
    required this.salonId,
    this.onSubscriptionChanged,
  });

  @override
  State<AppointmentSubscriptionDetailsDialog> createState() => _AppointmentSubscriptionDetailsDialogState();
}

class _AppointmentSubscriptionDetailsDialogState extends State<AppointmentSubscriptionDetailsDialog> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final subscription = widget.subscription;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: theme.colorScheme.primaryContainer,
                borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.repeat,
                    color: theme.colorScheme.onPrimaryContainer,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Detalii Abonament Recurent',
                      style: theme.textTheme.titleLarge?.copyWith(
                        color: theme.colorScheme.onPrimaryContainer,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.close,
                      color: theme.colorScheme.onPrimaryContainer,
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Status and Progress
                    _buildStatusSection(theme, subscription),
                    const SizedBox(height: 20),

                    // Frequency Details
                    _buildFrequencySection(theme, subscription),
                    const SizedBox(height: 20),

                    // Time Details
                    _buildTimeSection(theme, subscription),
                    const SizedBox(height: 20),

                    // Progress Details
                    _buildProgressSection(theme, subscription),

                    // Notes
                    if (subscription.notes != null && subscription.notes!.isNotEmpty) ...[
                      const SizedBox(height: 20),
                      _buildNotesSection(theme, subscription),
                    ],
                  ],
                ),
              ),
            ),

            // Actions
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                borderRadius: const BorderRadius.vertical(bottom: Radius.circular(16)),
              ),
              child: _buildActionButtons(theme, subscription),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSection(ThemeData theme, AppointmentSubscription subscription) {
    final statusColor = _getStatusColor(subscription.status);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            _getStatusIcon(subscription.status),
            color: statusColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Status: ${subscription.status}',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: statusColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: subscription.progressPercentage,
                  backgroundColor: statusColor.withOpacity(0.2),
                  valueColor: AlwaysStoppedAnimation<Color>(statusColor),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFrequencySection(ThemeData theme, AppointmentSubscription subscription) {
    return _buildInfoCard(
      theme,
      'Frecvența',
      Icons.schedule,
      [
        _buildInfoRow('Interval', subscription.frequencyDescription),
        _buildInfoRow('Total programări', '${subscription.totalRepetitions}'),
      ],
    );
  }

  Widget _buildTimeSection(ThemeData theme, AppointmentSubscription subscription) {
    return _buildInfoCard(
      theme,
      'Program',
      Icons.access_time,
      [
        _buildInfoRow('Ora de început', DateFormat('HH:mm').format(subscription.startTime)),
        _buildInfoRow('Ora de sfârșit', DateFormat('HH:mm').format(subscription.endTime)),
        _buildInfoRow('Durată', '${subscription.endTime.difference(subscription.startTime).inMinutes} minute'),
      ],
    );
  }

  Widget _buildProgressSection(ThemeData theme, AppointmentSubscription subscription) {
    final completed = subscription.totalRepetitions - subscription.remainingRepetitions;
    
    return _buildInfoCard(
      theme,
      'Progres',
      Icons.trending_up,
      [
        _buildInfoRow('Programări completate', '$completed'),
        _buildInfoRow('Programări rămase', '${subscription.remainingRepetitions}'),
        _buildInfoRow('Progres', '${(subscription.progressPercentage * 100).toStringAsFixed(1)}%'),
      ],
    );
  }

  Widget _buildNotesSection(ThemeData theme, AppointmentSubscription subscription) {
    return _buildInfoCard(
      theme,
      'Observații',
      Icons.note_alt,
      [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceVariant.withOpacity(0.5),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            subscription.notes!,
            style: theme.textTheme.bodyMedium,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoCard(ThemeData theme, String title, IconData icon, List<Widget> children) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: theme.colorScheme.outline.withOpacity(0.2)),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 20, color: theme.colorScheme.primary),
              const SizedBox(width: 8),
              Text(
                title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(ThemeData theme, AppointmentSubscription subscription) {
    return Row(
      children: [
        if (!subscription.isCancelled && !subscription.isCompleted) ...[
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _isLoading ? null : () => _cancelSubscription(),
              icon: const Icon(Icons.cancel),
              label: const Text('Anulează'),
              style: OutlinedButton.styleFrom(
                foregroundColor: theme.colorScheme.error,
                side: BorderSide(color: theme.colorScheme.error),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return Colors.green;
      case 'paused':
        return Colors.orange;
      case 'cancelled':
        return Colors.red;
      case 'completed':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return Icons.check_circle;
      case 'paused':
        return Icons.pause_circle;
      case 'cancelled':
        return Icons.cancel;
      case 'completed':
        return Icons.task_alt;
      default:
        return Icons.help;
    }
  }

  Future<void> _pauseSubscription() async {
    setState(() => _isLoading = true);
    
    try {
      await AppointmentSubscriptionService.pauseSubscription(
        widget.salonId,
        widget.subscription.id,
      );
      
      if (mounted) {
        showTopSnackBar(context, SnackBar(
          content: Text('Abonamentul a fost pus în pauză'),
          backgroundColor: Theme.of(context).colorScheme.primary,
        ));
        Navigator.of(context).pop();
        widget.onSubscriptionChanged?.call();
      }
    } catch (e) {
      if (mounted) {
        showTopSnackBar(context, SnackBar(
          content: Text('Eroare la punerea în pauză: $e'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ));
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _resumeSubscription() async {
    setState(() => _isLoading = true);
    
    try {
      await AppointmentSubscriptionService.resumeSubscription(
        widget.salonId,
        widget.subscription.id,
      );
      
      if (mounted) {
        showTopSnackBar(context, SnackBar(
          content: Text('Abonamentul a fost reactivat'),
          backgroundColor: Theme.of(context).colorScheme.primary,
        ));
        Navigator.of(context).pop();
        widget.onSubscriptionChanged?.call();
      }
    } catch (e) {
      if (mounted) {
        showTopSnackBar(context, SnackBar(
          content: Text('Eroare la reactivare: $e'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ));
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _cancelSubscription() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmare anulare'),
        content: const Text(
          'Sigur doriți să anulați acest abonament? Programările viitoare vor fi de asemenea anulate.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Anulează'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Confirmă'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() => _isLoading = true);
    
    try {
      await AppointmentSubscriptionService.cancelSubscription(
        widget.salonId,
        widget.subscription.id,
        cancelFutureAppointments: true,
      );
      
      if (mounted) {
        showTopSnackBar(context, SnackBar(
          content: Text('Abonamentul a fost anulat'),
          backgroundColor: Theme.of(context).colorScheme.primary,
        ));
        Navigator.of(context).pop();
        widget.onSubscriptionChanged?.call();
      }
    } catch (e) {
      if (mounted) {
        showTopSnackBar(context, SnackBar(
          content: Text('Eroare la anulare: $e'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ));
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
