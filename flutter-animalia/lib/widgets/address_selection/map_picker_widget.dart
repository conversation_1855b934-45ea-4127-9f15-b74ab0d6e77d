import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Simple map picker widget with a centered pin for selecting a location.
class MapPickerWidget extends StatefulWidget {
  final LatLng? initialLocation;
  final ValueChanged<LatLng> onLocationPicked;
  final bool enableCurrentLocationButton;

  const MapPickerWidget({
    Key? key,
    this.initialLocation,
    required this.onLocationPicked,
    this.enableCurrentLocationButton = true,
  }) : super(key: key);

  @override
  State<MapPickerWidget> createState() => _MapPickerWidgetState();
}

class _MapPickerWidgetState extends State<MapPickerWidget> {
  late LatLng _currentLatLng;
  GoogleMapController? _controller;
  bool _loadingLocation = true;

  @override
  void initState() {
    super.initState();
    _currentLatLng = widget.initialLocation ?? const LatLng(44.4268, 26.1025); // Bucharest default
    _initLocation();
  }

  Future<void> _initLocation() async {
    try {
      final permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        setState(() => _loadingLocation = false);
        return;
      }
      final position = await Geolocator.getCurrentPosition();
      if (!mounted) return;
      setState(() {
        _currentLatLng = LatLng(position.latitude, position.longitude);
        _loadingLocation = false;
      });
      _controller?.animateCamera(
        CameraUpdate.newLatLngZoom(_currentLatLng, 16),
      );
    } catch (_) {
      // ignore any errors and keep default location
      if (mounted) {
        setState(() => _loadingLocation = false);
      }
    }
  }

  void _onCameraIdle() {
    widget.onLocationPicked(_currentLatLng);
  }

  Future<void> _centerToCurrentLocation() async {
    try {
      final permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        return;
      }
      final position = await Geolocator.getCurrentPosition();
      final target = LatLng(position.latitude, position.longitude);
      _controller?.animateCamera(
        CameraUpdate.newLatLngZoom(target, 16),
      );
      setState(() {
        _currentLatLng = target;
      });
    } catch (_) {
      // ignore
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        AnimatedOpacity(
          opacity: _loadingLocation ? 0.3 : 1,
          duration: const Duration(milliseconds: 300),
          child: GoogleMap(
            initialCameraPosition:
                CameraPosition(target: _currentLatLng, zoom: 15),
            myLocationEnabled: true,
            myLocationButtonEnabled: true,
            onMapCreated: (c) => _controller = c,
            onCameraMove: (pos) => _currentLatLng = pos.target,
            onCameraIdle: _onCameraIdle,
          ),
        ),
        const Icon(Icons.location_pin, size: 40, color: Colors.red),
        if (_loadingLocation)
          const Positioned.fill(
            child: Center(child: CircularProgressIndicator()),
          ),
        if (widget.enableCurrentLocationButton)
          Positioned(
            bottom: 16,
            right: 16,
            child: FloatingActionButton(
              heroTag: "map_widget_location_fab",
              mini: true,
              onPressed: _centerToCurrentLocation,
              child: const Icon(Icons.my_location),
            ),
          ),
      ],
    );
  }
}
