import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../utils/formatters/phone_number_utils.dart';
import '../../l10n/app_localizations.dart';
import '../common/standard_form_field.dart';
import '../common/address_action_widget.dart';
import '../../models/client.dart';
import '../../config/theme/app_theme.dart';
import 'appointment_form_data.dart';
import '../../models/pet.dart';
import '../../providers/calendar_provider.dart';
import 'package:provider/provider.dart';

class ClientSelectionWidget extends StatefulWidget {
  final AppointmentFormData formData;
  final bool isLoadingClients;
  final Function(bool) onClientTypeChanged;
  final Function(Client) onClientSelected;
  final Function(String) onClientNameChanged;
  final Function(String) onClientPhoneChanged;
  final Function(String?) onClientSecondaryPhoneChanged;
  // New pet-related callbacks for integrated form
  final Function(String) onPetNameChanged;
  final Function(String) onPetBreedChanged;
  final Function(String) onPetSpeciesChanged;

  const ClientSelectionWidget({
    super.key,
    required this.formData,
    this.isLoadingClients = false,
    required this.onClientTypeChanged,
    required this.onClientSelected,
    required this.onClientNameChanged,
    required this.onClientPhoneChanged,
    required this.onClientSecondaryPhoneChanged,
    required this.onPetNameChanged,
    required this.onPetBreedChanged,
    required this.onPetSpeciesChanged,
  });

  @override
  State<ClientSelectionWidget> createState() => _ClientSelectionWidgetState();
}

class _ClientSelectionWidgetState extends State<ClientSelectionWidget> {
  List<String> _currentBreeds = [];
  bool _isLoadingBreeds = false;

  // Multiple phone numbers support
  final List<TextEditingController> _phoneControllers = [];
  final List<String> _initialCountryCodes = [];
  int _phoneFieldsCount = 1;
  int _primaryPhoneIndex = 0;
  static const int _maxPhoneFields = 3; // Limit to 3 for appointment form
  static const String _lastCountryCodeKey = 'last_client_country_code';

  // Phone search functionality
  List<Client> _matchingClients = [];
  bool _isSearchingClients = false;
  Timer? _phoneSearchDebounce;
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();
  final FocusNode _firstPhoneFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();

    // Initialize phone controllers
    final phones = <String>[];
    if (widget.formData.clientPhone.isNotEmpty) {
      phones.add(widget.formData.clientPhone);
    }
    if (widget.formData.clientSecondaryPhone != null && widget.formData.clientSecondaryPhone!.isNotEmpty) {
      phones.add(widget.formData.clientSecondaryPhone!);
    }
    
    _phoneFieldsCount = phones.isNotEmpty ? phones.length : 1;
    
    for (var i = 0; i < _phoneFieldsCount; i++) {
      _phoneControllers.add(TextEditingController());
      if (i < phones.length) {
        _phoneControllers[i].text = phones[i];
        _initialCountryCodes.add(_extractCountryCode(phones[i]));
      } else {
        _initialCountryCodes.add('RO');
      }
    }
    
    if (phones.isEmpty) {
      _phoneControllers.add(TextEditingController());
      _initialCountryCodes.add('RO');
      _loadLastCountryCode();
    }

    // Listen to focus changes to hide overlay
    _firstPhoneFocusNode.addListener(() {
      if (!_firstPhoneFocusNode.hasFocus) {
        _hideClientSuggestions();
      }
    });

    // Load breeds for the current pet species if available
    if (widget.formData.petSpecies.isNotEmpty) {
      _refreshBreedsForSpecies(widget.formData.petSpecies);
    } else {
      // Default to dog breeds if no species is set
      _refreshBreedsForSpecies('dog');
    }
  }

  @override
  void dispose() {
    _phoneSearchDebounce?.cancel();
    _hideClientSuggestions();
    _firstPhoneFocusNode.dispose();
    for (var controller in _phoneControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  // Extract country code from phone number (e.g., +40 -> RO)
  String _extractCountryCode(String phoneNumber) {
    if (phoneNumber.startsWith('+40')) return 'RO';
    if (phoneNumber.startsWith('+1')) return 'US';
    if (phoneNumber.startsWith('+44')) return 'GB';
    if (phoneNumber.startsWith('+49')) return 'DE';
    if (phoneNumber.startsWith('+33')) return 'FR';
    if (phoneNumber.startsWith('+34')) return 'ES';
    if (phoneNumber.startsWith('+39')) return 'IT';
    // Add more country codes as needed
    return 'RO'; // Default to Romania
  }

  Future<void> _loadLastCountryCode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedCode = prefs.getString(_lastCountryCodeKey);
      if (savedCode != null && savedCode.isNotEmpty && _initialCountryCodes.isEmpty) {
        setState(() {
          _initialCountryCodes.add(savedCode);
        });
      } else if (_initialCountryCodes.isEmpty) {
        _initialCountryCodes.add('RO');
      }
    } catch (e) {
      if (_initialCountryCodes.isEmpty) {
        _initialCountryCodes.add('RO');
      }
    }
  }

  // Save last used country code to SharedPreferences
  Future<void> _saveLastCountryCode(String countryCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastCountryCodeKey, countryCode);
    } catch (e) {
      // Ignore errors
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 16),
        if (widget.formData.isExistingClient)
          _buildExistingClientDropdown(context)
        else
          _buildNewClientFields(),
      ],
    );
  }


  Widget _buildExistingClientDropdown(BuildContext context) {
    if (widget.isLoadingClients) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Theme.of(context).colorScheme.outline),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            SizedBox(width: 12),
            Text(context.tr('new_appointment.loading_clients')),
          ],
        ),
      );
    }

    return _buildClientSearchButton(context);
  }

  Widget _buildClientSearchButton(BuildContext context) {
    final hasClientSelected = widget.formData.clientId.isNotEmpty &&
                              !widget.formData.clientId.startsWith('new-');

    return InkWell(
      onTap: () => _navigateToClientSearch(context),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Theme.of(context).colorScheme.outline),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Client',
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  SizedBox(height: 4),
                  if (hasClientSelected) ...[
                    // Client name
                    Text(
                      widget.formData.clientName,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    SizedBox(height: 4),
                    // Primary phone number
                    if (widget.formData.clientPhone.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 2),
                        child: Row(
                          children: [
                            Icon(
                              Icons.phone,
                              size: 16,
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                            SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                widget.formData.clientPhone,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    // Secondary phone numbers (if available)
                    ...widget.formData.secondaryPhoneNumbers.asMap().entries.map((entry) {
                      final index = entry.key;
                      final phone = entry.value;
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 2),
                        child: Row(
                          children: [
                            Icon(
                              Icons.phone_outlined,
                              size: 16,
                              color: Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.7),
                            ),
                            SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                'Telefon ${index + 2}: $phone',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.7),
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                    // Address (if available)
                    if (widget.formData.clientAddress.isNotEmpty) ...[
                      SizedBox(height: 4),
                      AddressActionWidget(
                        address: widget.formData.clientAddress,
                        textStyle: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        iconColor: Theme.of(context).colorScheme.primary,
                        padding: EdgeInsets.zero,
                      ),
                    ],
                  ] else
                    Text(
                      context.tr('new_appointment.select_client'),
                      style: TextStyle(
                        fontSize: 16,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                ],
              ),
            ),
             Icon(Icons.search, color: Theme.of(context).colorScheme.onSurface),
          ],
        ),
      ),
    );
  }

  void _navigateToClientSearch(BuildContext context) async {
    final result = await Navigator.of(context).pushNamed(
      '/client-search',
      arguments: widget.formData.availableClients,
    );

    if (result != null) {
      if (result is Client) {
        widget.onClientSelected(result);
      } else if (result == 'new_client') {
        // Switch to new client mode
        widget.onClientTypeChanged(false);
      }
    }
  }

  Widget _buildNewClientFields() {
    return Column(
      children: [
        // 1. Phone numbers (required)
        _buildPhoneNumberFields(),
        const SizedBox(height: 16),

        // 2. Pet name second (required)
        TextFormField(
          decoration: InputDecoration(
            labelText: context.tr('new_appointment.pet_name_required'),
            border: OutlineInputBorder(),
            hintText: context.tr('new_appointment.pet_name_placeholder'),
          ),
          initialValue: widget.formData.petName,
          onChanged: widget.onPetNameChanged,
          textCapitalization: TextCapitalization.words,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return context.tr('new_appointment.pet_name_validation');
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // 3. Pet breed third (required)
        _buildPetBreedField(),
        const SizedBox(height: 16),

        // 4. Client name last (optional)
        TextFormField(
          decoration: InputDecoration(
            labelText: context.tr('new_appointment.client_name_optional'),
            border: OutlineInputBorder(),
            helperText: context.tr('new_appointment.client_name_helper'),
          ),
          initialValue: widget.formData.clientName,
          onChanged: widget.onClientNameChanged,
          textCapitalization: TextCapitalization.words,
        ),
      ],
    );
  }

  Widget _buildPhoneNumberFields() {
    return Column(
      children: [
        if (_phoneFieldsCount > 1) ...[
          Container(
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.only(bottom: 12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Theme.of(context).colorScheme.primary.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Theme.of(context).colorScheme.primary, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Selectează numărul principal pentru SMS-urile automate',
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
        ...List.generate(_phoneFieldsCount, (index) {
          final phoneField = Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (_phoneFieldsCount > 1)
                Padding(
                  padding: const EdgeInsets.only(top: 18, right: 8),
                  child: Radio<int>(
                    value: index,
                    groupValue: _primaryPhoneIndex,
                    onChanged: (int? value) {
                      if (value != null) {
                        setState(() {
                          _primaryPhoneIndex = value;
                        });
                        _updatePhoneNumbers();
                      }
                    },
                    activeColor: Theme.of(context).colorScheme.primary,
                  ),
                ),
              Expanded(
                child: IntlPhoneField(
                  focusNode: index == 0 ? _firstPhoneFocusNode : null,
                  decoration: InputDecoration(
                    labelText: index == 0
                        ? 'Telefon client *'
                        : 'Telefon secundar (opțional)',
                    hintText: '731 234 567',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    prefixIcon: const Icon(Icons.phone_outlined),
                    contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                    // Add "Add secondary number" button as suffix for the first field
                    suffixIcon: index == 0 && _phoneFieldsCount < _maxPhoneFields
                        ? Padding(
                            padding: const EdgeInsets.only(right: 4),
                            child: IconButton(
                              icon: Icon(Icons.add_circle_outline,
                                color: Theme.of(context).colorScheme.primary),
                              tooltip: 'Adaugă număr secundar',
                              onPressed: _addPhoneField,
                              constraints: BoxConstraints(),
                              padding: EdgeInsets.all(8),
                            ),
                          )
                        : null,
                    helperText: index == _primaryPhoneIndex && _phoneFieldsCount > 1
                        ? '📱 Principal - pentru SMS automate'
                        : index == 0 && _phoneFieldsCount < _maxPhoneFields
                        ? 'Apasă + pentru a adăuga un număr secundar'
                        : null,
                    helperStyle: TextStyle(
                      color: index == _primaryPhoneIndex && _phoneFieldsCount > 1
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.onSurfaceVariant,
                      fontWeight: index == _primaryPhoneIndex && _phoneFieldsCount > 1
                          ? FontWeight.w600
                          : FontWeight.normal,
                      fontSize: 12,
                    ),
                  ),
                  initialCountryCode: _initialCountryCodes.length > index
                      ? _initialCountryCodes[index]
                      : 'RO',
                  dropdownIconPosition: IconPosition.trailing,
                  flagsButtonPadding: const EdgeInsets.symmetric(horizontal: 8.0),
                  dropdownTextStyle: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                  showDropdownIcon: true,
                  disableLengthCheck: false,
                  keyboardType: TextInputType.phone,
                  onChanged: (phone) {
                    setState(() {
                      _phoneControllers[index].text = phone.completeNumber;
                    });
                    _updatePhoneNumbers();
                    if (index == 0) {
                      _saveLastCountryCode(phone.countryISOCode);
                      _checkForExistingClient(phone.completeNumber);
                    }
                  },
                  validator: (value) {
                    if (index == 0 && (value == null || value.number.isEmpty)) {
                      return 'Numărul de telefon este obligatoriu';
                    }
                    if (value != null && value.number.isNotEmpty && value.number.length < 6) {
                      return 'Numărul de telefon nu este valid';
                    }
                    return null;
                  },
                  initialValue: _phoneControllers[index].text.isNotEmpty
                      ? _phoneControllers[index].text.replaceAll(RegExp(r'[^\d]'), '')
                      : null,
                ),
              ),
              if (index > 0)
                Padding(
                  padding: const EdgeInsets.only(left: 8, top: 8),
                  child: IconButton(
                    icon: Icon(Icons.remove_circle, color: Theme.of(context).colorScheme.error),
                    onPressed: () => _removePhoneField(index),
                    tooltip: 'Șterge numărul de telefon',
                  ),
                ),
            ],
          );

          // Wrap the first phone field with CompositedTransformTarget for proper overlay positioning
          return Padding(
            padding: EdgeInsets.only(bottom: index < _phoneFieldsCount - 1 ? 12 : 0),
            child: index == 0
                ? CompositedTransformTarget(
                    link: _layerLink,
                    child: phoneField,
                  )
                : phoneField,
          );
        }),
      ],
    );
  }

  void _addPhoneField() {
    if (_phoneFieldsCount < _maxPhoneFields) {
      setState(() {
        _phoneControllers.add(TextEditingController());
        _initialCountryCodes.add(_initialCountryCodes.isNotEmpty ? _initialCountryCodes.last : 'RO');
        _phoneFieldsCount++;
      });
    }
  }

  void _removePhoneField(int index) {
    if (_phoneFieldsCount > 1 && index < _phoneControllers.length) {
      setState(() {
        if (index == _primaryPhoneIndex) {
          _primaryPhoneIndex = 0;
        } else if (index < _primaryPhoneIndex) {
          _primaryPhoneIndex--;
        }
        _phoneControllers[index].dispose();
        _phoneControllers.removeAt(index);
        _initialCountryCodes.removeAt(index);
        _phoneFieldsCount--;
      });
      _updatePhoneNumbers();
    }
  }

  void _updatePhoneNumbers() {
    final phoneNumbers = <String>[];
    for (int i = 0; i < _phoneControllers.length; i++) {
      final phone = _phoneControllers[i].text.trim();
      if (phone.isNotEmpty) {
        phoneNumbers.add(phone);
      }
    }
    
    String primaryPhone = '';
    String? secondaryPhone;
    
    if (phoneNumbers.isNotEmpty) {
      if (_primaryPhoneIndex < phoneNumbers.length) {
        primaryPhone = phoneNumbers[_primaryPhoneIndex];
        final otherPhones = List<String>.from(phoneNumbers);
        otherPhones.removeAt(_primaryPhoneIndex);
        secondaryPhone = otherPhones.isNotEmpty ? otherPhones.first : null;
      } else {
        primaryPhone = phoneNumbers.first;
        secondaryPhone = phoneNumbers.length > 1 ? phoneNumbers[1] : null;
      }
    }
    
    widget.onClientPhoneChanged(primaryPhone);
    widget.onClientSecondaryPhoneChanged(secondaryPhone);
  }

  // Check if phone number matches an existing client and show suggestion
  void _checkForExistingClient(String phoneNumber) {
    if (phoneNumber.isEmpty) {
      _hideClientSuggestions();
      return;
    }

    // Trigger the backend search with debouncing
    _showClientSuggestions(phoneNumber);
  }

  Widget _buildPetBreedField() {
    return Autocomplete<String>(
      optionsBuilder: (TextEditingValue textEditingValue) {
        // Use breeds from backend with fallback to static data
        final suggestions = _currentBreeds.isNotEmpty
            ? _currentBreeds
            : ['Metis', 'Necunoscut']; // Simple fallback
        if (textEditingValue.text.isEmpty) {
          return suggestions;
        }
        return suggestions.where((option) =>
            option.toLowerCase().contains(textEditingValue.text.toLowerCase()));
      },
      onSelected: (String selection) {
        widget.onPetBreedChanged(selection);
      },
      fieldViewBuilder: (context, controller, focusNode, onEditingComplete) {
        // Sync the autocomplete controller with our breed value
        if (controller.text != widget.formData.petBreed) {
          controller.text = widget.formData.petBreed;
        }

        return TextFormField(
          controller: controller,
          focusNode: focusNode,
          onEditingComplete: onEditingComplete,
          onChanged: (value) {
            widget.onPetBreedChanged(value);
          },
          decoration: InputDecoration(
            labelText: context.tr('new_appointment.breed_required'),
            hintText: _isLoadingBreeds
                ? context.tr('new_appointment.breed_loading')
                : context.tr('new_appointment.breed_placeholder'),
            prefixIcon: Icon(Icons.search),
            suffixIcon: _isLoadingBreeds
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : IconButton(
                    icon: Icon(Icons.refresh),
                    onPressed: () => _refreshBreedsForSpecies(widget.formData.petSpecies),
                    tooltip: context.tr('new_appointment.breed_refresh_tooltip'),
                  ),
            border: OutlineInputBorder(),
          ),
          textCapitalization: TextCapitalization.words,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return context.tr('new_appointment.breed_validation');
            }
            return null;
          },
        );
      },
      optionsViewBuilder: (context, onSelected, options) {
        return Align(
          alignment: Alignment.topLeft,
          child: Material(
            elevation: 4.0,
            borderRadius: BorderRadius.circular(8),
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxHeight: 200),
              child: ListView.builder(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: options.length,
                itemBuilder: (context, index) {
                  final option = options.elementAt(index);
                  return InkWell(
                    onTap: () => onSelected(option),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      child: Text(option),
                    ),
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> _refreshBreedsForSpecies(String species) async {
    if (species.isEmpty) return;

    setState(() => _isLoadingBreeds = true);

    try {
      final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
      final breeds = await calendarProvider.getBreedsForSpecies(species);

      if (mounted) {
        setState(() {
          _currentBreeds = breeds;
          _isLoadingBreeds = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _currentBreeds = ['Metis', 'Necunoscut']; // Fallback
          _isLoadingBreeds = false;
        });
      }
    }
  }

  void _showClientSuggestions(String query) {
    if (query.isEmpty) {
      setState(() {
        _matchingClients = [];
        _isSearchingClients = false;
      });
      return;
    }

    _isSearchingClients = true;

    // Debounce the search
    _phoneSearchDebounce?.cancel();
    _phoneSearchDebounce = Timer(Duration(milliseconds: 300), () async {
      try {
        final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
        final clients = await calendarProvider.searchClientsByPhone(query);

        setState(() {
          _matchingClients = clients;
          _isSearchingClients = false;
        });

        // Show overlay if there are matching clients
        if (clients.isNotEmpty) {
          _showClientSuggestionsOverlay();
        } else {
          _hideClientSuggestions();
        }
      } catch (e) {
        setState(() {
          _matchingClients = [];
          _isSearchingClients = false;
        });
      }
    });
  }

  void _showClientSuggestionsOverlay() {
    if (_overlayEntry != null) {
      return;
    }

    final overlay = Overlay.of(context);
    if (overlay == null) {
      return;
    }

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        width: MediaQuery.of(context).size.width - 32, // Account for screen padding
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0, 72), // Position below the phone field (field height + small gap)
          child: Material(
            elevation: 8.0,
            borderRadius: BorderRadius.circular(8),
            child: ConstrainedBox(
              constraints: BoxConstraints(maxHeight: 300),
              child: ListView.separated(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: _matchingClients.length,
                separatorBuilder: (context, index) => Divider(height: 1, thickness: 1, color: Colors.grey.shade300),
                itemBuilder: (context, index) {
                  final client = _matchingClients[index];
                  return InkWell(
                    onTap: () {
                      widget.onClientSelected(client);
                      _hideClientSuggestions();
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            client.name,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            client.phone,
                            style: TextStyle(
                              fontSize: 14,
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );

    overlay.insert(_overlayEntry!);
  }

  void _hideClientSuggestions() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
}
