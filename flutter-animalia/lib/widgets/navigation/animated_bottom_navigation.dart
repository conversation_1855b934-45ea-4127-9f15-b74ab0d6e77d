import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:showcaseview/showcaseview.dart';

import '../../config/theme/app_theme.dart';

/// Enhanced bottom navigation bar with smooth animations and iOS-style design
class AnimatedBottomNavigation extends StatefulWidget {
  final int currentIndex;
  final List<AnimatedBottomNavigationItem> items;
  final ValueChanged<int> onTap;
  final Color? backgroundColor;
  final double height;
  final Duration animationDuration;
  final Curve animationCurve;

  const AnimatedBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.items,
    required this.onTap,
    this.backgroundColor,
    this.height = 100.0,
    this.animationDuration = const Duration(milliseconds: 250),
    this.animationCurve = Curves.easeInOutCubic,
  });

  @override
  State<AnimatedBottomNavigation> createState() => _AnimatedBottomNavigationState();
}

class _AnimatedBottomNavigationState extends State<AnimatedBottomNavigation>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late Animation<double> _slideAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _slideController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: widget.animationCurve,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didUpdateWidget(AnimatedBottomNavigation oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentIndex != widget.currentIndex) {
      _slideController.forward().then((_) {
        _slideController.reset();
      });
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  void _handleTap(int index) {
    if (index != widget.currentIndex) {
      // Trigger haptic feedback
      HapticFeedback.lightImpact();
      
      // Scale animation for tap feedback
      _scaleController.forward().then((_) {
        _scaleController.reverse();
      });
      
      widget.onTap(index);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: colorScheme.outline.withOpacity(0.1),
            width: 0.5,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, -4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: colorScheme.shadow.withOpacity(0.04),
            blurRadius: 6,
            offset: const Offset(0, -1),
            spreadRadius: 0,
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: Stack(
          children: [
            // Animated sliding indicator
            _buildSlidingIndicator(colorScheme),

            // Navigation items
            Row(
              children: widget.items.asMap().entries.map((entry) {
                final index = entry.key;
                final item = entry.value;
                final isSelected = index == widget.currentIndex;

                return Expanded(
                  child: item.showcaseKey != null
                      ? Showcase(
                          key: item.showcaseKey!,
                          title: 'Navigare',
                          description: 'Apasă aici pentru a accesa ${item.label}',
                          child: _buildNavigationItem(
                            item,
                            isSelected,
                            index,
                            colorScheme,
                          ),
                        )
                      : _buildNavigationItem(
                          item,
                          isSelected,
                          index,
                          colorScheme,
                        ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSlidingIndicator(ColorScheme colorScheme) {
    return AnimatedBuilder(
      animation: _slideAnimation,
      builder: (context, child) {
        final itemWidth = MediaQuery.of(context).size.width / widget.items.length;
        final indicatorPosition = widget.currentIndex * itemWidth;

        return AnimatedPositioned(
          duration: widget.animationDuration,
          curve: widget.animationCurve,
          top: 8,
          left: indicatorPosition + (itemWidth * 0.3),
          child: Container(
            width: itemWidth * 0.4,
            height: 3,
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              borderRadius: BorderRadius.circular(2),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).primaryColor.withOpacity(0.3),
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildNavigationItem(
    AnimatedBottomNavigationItem item,
    bool isSelected,
    int index,
    ColorScheme colorScheme,
  ) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        final scale = index == widget.currentIndex ? _scaleAnimation.value : 1.0;
        
        return Transform.scale(
          scale: scale,
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => _handleTap(index),
              borderRadius: BorderRadius.circular(12),
              child: SizedBox(
                height: widget.height,
                child: Column(
                  children: [
                    const SizedBox(height: 8), // Space for indicator

                    // Icon area - uses Expanded to prevent overflow
                    Expanded(
                      flex: 2,
                      child: Center(
                        child: Stack(
                          clipBehavior: Clip.none,
                          alignment: Alignment.center,
                          children: [
                            AnimatedContainer(
                              duration: const Duration(milliseconds: 200),
                              padding: EdgeInsets.all(isSelected ? 1 : 0),
                              decoration: BoxDecoration(
                                color: isSelected
                                    ? Theme.of(context).primaryColor.withOpacity(0.1)
                                    : Colors.transparent,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: AnimatedSwitcher(
                                duration: const Duration(milliseconds: 200),
                                child: Icon(
                                  isSelected ? item.activeIcon : item.icon,
                                  key: ValueKey('${item.label}_$isSelected'),
                                  size: 24,
                                  color: isSelected
                                      ? Theme.of(context).primaryColor
                                      : colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ),
                            if (item.badge != null)
                              Positioned(
                                right: -2,
                                top: -2,
                                child: item.badge!,
                              ),
                          ],
                        ),
                      ),
                    ),

                    // Label area - uses Expanded to prevent overflow
                    Expanded(
                      flex: 1,
                      child: Center(
                        child: AnimatedDefaultTextStyle(
                          duration: const Duration(milliseconds: 200),
                          style: TextStyle(
                            fontSize: isSelected ? 11 : 10,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                            color: isSelected
                                ? Theme.of(context).primaryColor
                                : colorScheme.onSurfaceVariant,
                            height: 1.0,
                          ),
                          child: Text(
                            item.label,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Navigation item model for the animated bottom navigation
class AnimatedBottomNavigationItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;
  final Widget? badge;
  final GlobalKey? showcaseKey;

  const AnimatedBottomNavigationItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
    this.badge,
    this.showcaseKey,
  });
}
