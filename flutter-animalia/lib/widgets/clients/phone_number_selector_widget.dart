oti import 'package:flutter/material.dart';
import '../../config/theme/app_theme.dart';
import '../../l10n/app_localizations.dart';
import '../../models/client.dart';
import '../../services/client/client_service.dart';
import '../../utils/formatters/phone_number_utils.dart';
import '../../utils/snack_bar_utils.dart';

/// Widget for selecting primary phone number for automated SMS
class PhoneNumberSelectorWidget extends StatefulWidget {
  final Client client;
  final Function(Client)? onPhoneUpdated;

  const PhoneNumberSelectorWidget({
    super.key,
    required this.client,
    this.onPhoneUpdated,
  });

  @override
  State<PhoneNumberSelectorWidget> createState() => _PhoneNumberSelectorWidgetState();
}

class _PhoneNumberSelectorWidgetState extends State<PhoneNumberSelectorWidget> {
  bool _isUpdating = false;

  List<String> _getPhoneNumbers() {
    if (widget.client.phone.isEmpty) return [];
    return widget.client.phone
        .split(',')
        .map((p) => p.trim())
        .where((p) => p.isNotEmpty)
        .toList();
  }

  Future<void> _setPrimaryPhone(String phoneNumber) async {
    setState(() {
      _isUpdating = true;
    });

    try {
      final response = await ClientService.setPrimaryPhone(
        widget.client.id,
        phoneNumber,
      );

      if (response.success && response.data != null) {
        if (mounted) {
          SnackBarUtils.showSuccess(
            context,
            AppLocalizations.of(context)?.translate('primary_phone_updated') ??
            'Număr principal actualizat cu succes',
          );

          // Call callback with updated client
          widget.onPhoneUpdated?.call(response.data!);
        }
      } else {
        if (mounted) {
          SnackBarUtils.showError(
            context,
            response.error ?? 'Eroare la actualizarea numărului principal',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        SnackBarUtils.showError(
          context,
          'Eroare: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final phoneNumbers = _getPhoneNumbers();

    if (phoneNumbers.isEmpty) {
      return const SizedBox.shrink();
    }

    // If only one phone number, don't show selector
    if (phoneNumbers.length == 1) {
      return const SizedBox.shrink();
    }

    final primaryPhone = phoneNumbers.first;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.phone_android,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  AppLocalizations.of(context)?.translate('phone_numbers') ??
                  'Numere de telefon',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              AppLocalizations.of(context)?.translate('select_primary_phone_desc') ??
              'Selectați numărul pe care se trimit SMS-urile automate',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 16),
            if (_isUpdating)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: CircularProgressIndicator(),
                ),
              )
            else
              ...phoneNumbers.map((phone) {
                final isPrimary = phone == primaryPhone;
                final formattedPhone = PhoneNumberUtils.formatToRomanianStandard(phone);

                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: InkWell(
                    onTap: isPrimary ? null : () => _setPrimaryPhone(phone),
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: isPrimary
                            ? AppTheme.primaryColor.withOpacity(0.1)
                            : Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isPrimary
                              ? AppTheme.primaryColor
                              : Colors.grey[300]!,
                          width: isPrimary ? 2 : 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            isPrimary ? Icons.check_circle : Icons.radio_button_unchecked,
                            color: isPrimary ? AppTheme.primaryColor : Colors.grey[400],
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  formattedPhone,
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: isPrimary ? FontWeight.bold : FontWeight.normal,
                                    color: isPrimary ? AppTheme.primaryColor : Colors.black87,
                                  ),
                                ),
                                if (isPrimary)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 4),
                                    child: Text(
                                      AppLocalizations.of(context)?.translate('primary_for_sms') ??
                                      'Principal - pentru SMS automate',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: AppTheme.primaryColor,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                          if (!isPrimary)
                            Text(
                              AppLocalizations.of(context)?.translate('tap_to_select') ??
                              'Apasă pentru a selecta',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                );
              }).toList(),
          ],
        ),
      ),
    );
  }
}

