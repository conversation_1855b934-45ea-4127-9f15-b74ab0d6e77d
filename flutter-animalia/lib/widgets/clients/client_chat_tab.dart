import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../models/client.dart';
import '../../models/sms_log.dart';
import '../../models/appointment.dart';
import '../../services/sms_logs_service.dart';
import '../../providers/calendar_provider.dart';
import '../../utils/debug_logger.dart';
import '../../utils/genie_animation.dart';
import '../../l10n/app_localizations.dart';
import '../dialogs/appointment_details_dialog.dart';

/// Chat tab showing SMS/WhatsApp messages in a modern chat interface
class ClientChatTab extends StatefulWidget {
  final Client client;

  const ClientChatTab({
    super.key,
    required this.client,
  });

  @override
  State<ClientChatTab> createState() => _ClientChatTabState();
}

class _ClientChatTabState extends State<ClientChatTab> {
  final ScrollController _scrollController = ScrollController();
  final List<SmsLog> _messages = [];
  bool _isLoading = false;
  bool _hasMore = true;
  int _currentPage = 0;
  final int _pageSize = 20;
  String? _error;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadMessages();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoading && _hasMore) {
        _loadMoreMessages();
      }
    }
  }

  Future<void> _loadMessages() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _error = null;
      _currentPage = 0;
      _messages.clear();
      _hasMore = true;
    });

    try {
      final response = await SmsLogsService.getSmsLogsForClient(
        widget.client.id,
        page: _currentPage,
        size: _pageSize,
      );

      if (response.success && response.data != null) {
        DebugLogger.logVerbose('Client messages loaded: ${response.data!.content.length} items, total: ${response.data!.totalElements}');
        setState(() {
          _messages.addAll(response.data!.content);
          _hasMore = !response.data!.last;
          _currentPage++;
        });
      } else {
        DebugLogger.logVerbose('Client messages error: ${response.error}');
        setState(() {
          _error = response.error ?? AppLocalizations.of(context).translate('chat.error_loading_messages');
        });
      }
    } catch (e) {
      DebugLogger.logVerbose('Client messages exception: $e');
      setState(() {
        _error = AppLocalizations.of(context).translate('chat.error_loading_messages_details', params: {'error': e.toString()});
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreMessages() async {
    if (_isLoading || !_hasMore) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await SmsLogsService.getSmsLogsForClient(
        widget.client.id,
        page: _currentPage,
        size: _pageSize,
      );

      if (response.success && response.data != null) {
        setState(() {
          _messages.addAll(response.data!.content);
          _hasMore = !response.data!.last;
          _currentPage++;
        });
      }
    } catch (e) {
      DebugLogger.logVerbose('Error loading more client messages: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Open appointment details when message has an associated appointment
  Future<void> _openAppointmentDetails(SmsLog smsLog) async {
    if (smsLog.appointmentId == null) {
      // No appointment associated with this message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context).translate('chat.message_not_associated')),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
      return;
    }

    try {
      // Get the appointment from CalendarProvider's appointments list
      final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);

      // Search for the appointment in the loaded appointments
      Appointment? appointment;
      try {
        appointment = calendarProvider.appointments.firstWhere(
          (apt) => apt.id == smsLog.appointmentId,
        );
      } catch (e) {
        // Appointment not found in current loaded list
        appointment = null;
      }

      // If not found in loaded appointments, try to load it from the calendar service
      if (appointment == null) {
        DebugLogger.logVerbose('📅 Appointment not found in loaded data, fetching from server...');

        // Show loading indicator
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => Center(
            child: CircularProgressIndicator(),
          ),
        );

        // Fetch the appointment date from the SMS log
        if (smsLog.appointmentDate != null) {
          await calendarProvider.fetchAppointmentsForDate(
            smsLog.appointmentDate!,
            forceRefresh: true,
          );

          // Search again in the newly loaded appointments
          try {
            appointment = calendarProvider.appointments.firstWhere(
              (apt) => apt.id == smsLog.appointmentId,
            );
          } catch (e) {
            appointment = null;
          }
        }

        // Close loading dialog
        if (mounted) {
          Navigator.of(context).pop();
        }
      }

      if (appointment != null && mounted) {
        // Show appointment details dialog
        GenieAnimation.showGenieDialog(
          context: context,
          dialog: AppointmentDetailsDialog(appointment: appointment),
        );
      } else if (mounted) {
        // Appointment not found
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Programarea nu a fost găsită'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ Error loading appointment: $e');

      // Close loading dialog if still open
      if (mounted && Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).translate('chat.error_loading_appointment', params: {'error': e.toString()})),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: isDark
            ? [
                const Color(0xFF1A1A1A),
                const Color(0xFF0D0D0D),
              ]
            : [
                const Color(0xFFECE5DD),
                const Color(0xFFD9D3CC),
              ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Modern Header with gradient
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDark
                  ? [
                      Theme.of(context).colorScheme.primary.withOpacity(0.3),
                      Theme.of(context).colorScheme.primary.withOpacity(0.15),
                    ]
                  : [
                      Theme.of(context).colorScheme.primary.withOpacity(0.15),
                      Theme.of(context).colorScheme.primary.withOpacity(0.05),
                    ],
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.chat_bubble_rounded,
                      color: Theme.of(context).colorScheme.primary,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppLocalizations.of(context).translate('chat.title'),
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w700,
                            letterSpacing: 0.5,
                          ),
                        ),
                        Text(
                          AppLocalizations.of(context).translate('chat.conversation_with', params: {'name': widget.client.name}),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: Icon(Icons.refresh_rounded),
                    onPressed: _loadMessages,
                    tooltip: AppLocalizations.of(context).translate('chat.refresh_tooltip'),
                    style: IconButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Content
          Expanded(
            child: _error != null
                ? _buildErrorWidget()
                : _messages.isEmpty && !_isLoading
                    ? _buildEmptyWidget()
                    : _buildMessagesList(),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.red.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Colors.red.withOpacity(0.3),
              width: 2,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.error_outline, color: Colors.red, size: 48),
              const SizedBox(height: 12),
              Text(
                _error!,
                style: TextStyle(
                  color: Colors.red[700],
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.chat_bubble_outline_rounded,
                size: 64,
                color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              AppLocalizations.of(context).translate('chat.no_messages'),
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              AppLocalizations.of(context).translate('chat.no_messages_description'),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessagesList() {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
      reverse: false, // Newest messages at bottom like a real chat
      itemCount: _messages.length + (_hasMore && _isLoading ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == _messages.length) {
          return _buildLoadingIndicator();
        }

        final message = _messages[index];
        return _buildChatBubble(message);
      },
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      alignment: Alignment.center,
      child: CircularProgressIndicator(strokeWidth: 2),
    );
  }

  Widget _buildChatBubble(SmsLog smsLog) {
    final dateFormat = DateFormat('HH:mm');
    final dateFormatFull = DateFormat('dd MMM, HH:mm');
    final isDark = Theme.of(context).brightness == Brightness.dark;

    // Determine if message is sent by user (business) or received from client
    // Use direction field to determine message direction
    // INCOMING (from client) = left side, OUTGOING (from salon) = right side
    final bool isFromClient = smsLog.direction == SmsDirection.incoming;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: Row(
        mainAxisAlignment: isFromClient ? MainAxisAlignment.start : MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // Avatar for messages from client (left side)
          if (isFromClient) ...[
            _buildAvatar(isClient: true),
            const SizedBox(width: 8),
          ],

          // Message bubble
          Flexible(
            child: GestureDetector(
              onTap: () => _openAppointmentDetails(smsLog),
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.75,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: isFromClient
                      ? isDark
                        ? [
                            const Color(0xFF2A2A2A),
                            const Color(0xFF1F1F1F),
                          ]
                        : [
                            Colors.white,
                            const Color(0xFFFAFAFA),
                          ]
                      : isDark
                        ? [
                            Theme.of(context).colorScheme.primary.withOpacity(0.8),
                            Theme.of(context).colorScheme.primary.withOpacity(0.6),
                          ]
                        : [
                            const Color(0xFFDCF8C6), // WhatsApp sent message green
                            const Color(0xFFD0F4B8),
                          ],
                  ),
                  borderRadius: BorderRadius.only(
                    topLeft: const Radius.circular(18),
                    topRight: const Radius.circular(18),
                    bottomLeft: Radius.circular(isFromClient ? 4 : 18),
                    bottomRight: Radius.circular(isFromClient ? 18 : 4),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.08),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Message type badge and WhatsApp indicator
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Message type badge
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                            decoration: BoxDecoration(
                              color: smsLog.getTypeColor().withOpacity(0.2),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: smsLog.getTypeColor().withOpacity(0.4),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  smsLog.getTypeIcon(),
                                  size: 12,
                                  color: smsLog.getTypeColor(),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  smsLog.messageType.displayName,
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                    color: smsLog.getTypeColor(),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // WhatsApp indicator
                          if (smsLog.isWhatsApp) ...[
                            const SizedBox(width: 6),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
                              decoration: BoxDecoration(
                                color: const Color(0xFF25D366).withOpacity(0.15),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.chat_bubble,
                                    size: 12,
                                    color: const Color(0xFF25D366),
                                  ),
                                  const SizedBox(width: 3),
                                  Text(
                                    'WhatsApp',
                                    style: TextStyle(
                                      fontSize: 9,
                                      fontWeight: FontWeight.w600,
                                      color: const Color(0xFF25D366),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ],
                      ),

                      const SizedBox(height: 8),

                      // Pet name if available
                      if (smsLog.petName != null) ...[
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.pets,
                              size: 14,
                              color: isFromClient
                                ? Theme.of(context).colorScheme.primary
                                : (isDark ? Colors.white70 : const Color(0xFF075E54)),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              smsLog.petName!,
                              style: TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w600,
                                color: isFromClient
                                  ? Theme.of(context).colorScheme.onSurface
                                  : (isDark ? Colors.white : const Color(0xFF075E54)),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 6),
                      ],

                      // Message content
                      Text(
                        smsLog.displayMessageContent,
                        style: TextStyle(
                          fontSize: 14,
                          height: 1.4,
                          color: isFromClient
                            ? Theme.of(context).colorScheme.onSurface
                            : (isDark ? Colors.white : const Color(0xFF303030)),
                        ),
                      ),

                      const SizedBox(height: 6),

                      // Bottom row: Time, SMS units, and status
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          // SMS units indicator
                          if (!smsLog.isWhatsApp) ...[
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
                              decoration: BoxDecoration(
                                color: isFromClient
                                  ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                                  : (isDark
                                      ? Colors.white.withOpacity(0.2)
                                      : const Color(0xFF075E54).withOpacity(0.15)),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.sms,
                                    size: 10,
                                    color: isFromClient
                                      ? Theme.of(context).colorScheme.primary
                                      : (isDark ? Colors.white70 : const Color(0xFF075E54)),
                                  ),
                                  const SizedBox(width: 3),
                                  Text(
                                    '${smsLog.smsUnits}',
                                    style: TextStyle(
                                      fontSize: 9,
                                      fontWeight: FontWeight.w600,
                                      color: isFromClient
                                        ? Theme.of(context).colorScheme.primary
                                        : (isDark ? Colors.white70 : const Color(0xFF075E54)),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 6),
                          ],

                          // Time
                          Text(
                            dateFormat.format(smsLog.sentAt),
                            style: TextStyle(
                              fontSize: 11,
                              color: isFromClient
                                ? Theme.of(context).colorScheme.onSurface.withOpacity(0.5)
                                : (isDark ? Colors.white60 : const Color(0xFF075E54).withOpacity(0.6)),
                            ),
                          ),

                          // Status indicator for sent messages (from salon)
                          if (!isFromClient) ...[
                            const SizedBox(width: 4),
                            Icon(
                              smsLog.status == SmsStatus.sent
                                  ? Icons.done_all
                                  : smsLog.status == SmsStatus.failed
                                      ? Icons.error_outline
                                      : Icons.access_time,
                              size: 14,
                              color: smsLog.status == SmsStatus.sent
                                  ? (isDark ? Colors.white70 : const Color(0xFF075E54))
                                  : smsLog.status == SmsStatus.failed
                                      ? Colors.red
                                      : Colors.orange,
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Avatar for messages from salon (right side)
          if (!isFromClient) ...[
            const SizedBox(width: 8),
            _buildAvatar(isClient: false),
          ],
        ],
      ),
    );
  }

  Widget _buildAvatar({required bool isClient}) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: isClient
            ? Theme.of(context).colorScheme.secondary.withOpacity(0.2)
            : Theme.of(context).colorScheme.primary.withOpacity(0.2),
        shape: BoxShape.circle,
        border: Border.all(
          color: isClient
              ? Theme.of(context).colorScheme.secondary.withOpacity(0.3)
              : Theme.of(context).colorScheme.primary.withOpacity(0.3),
          width: 2,
        ),
      ),
      child: Icon(
        isClient ? Icons.person : Icons.store,
        size: 18,
        color: isClient
            ? Theme.of(context).colorScheme.secondary
            : Theme.of(context).colorScheme.primary,
      ),
    );
  }
}
