import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../config/theme/app_theme.dart';
import '../../providers/calendar_provider.dart';

class CalendarSearchFilter extends StatefulWidget {
  const CalendarSearchFilter({super.key});

  @override
  State<CalendarSearchFilter> createState() => _CalendarSearchFilterState();
}

class _CalendarSearchFilterState extends State<CalendarSearchFilter> {
  final TextEditingController _searchController = TextEditingController();
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    final provider = Provider.of<CalendarProvider>(context, listen: false);
    _searchController.text = provider.searchQuery;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        // Use FutureBuilder to handle async service durations
        return FutureBuilder<Map<String, int>>(
          future: provider.getServiceDurations(),
          builder: (context, snapshot) {
            final services = snapshot.hasData ? snapshot.data!.keys.toList() : <String>[];
            final statuses = ['confirmed', 'pending', 'canceled'];

            return _buildFilterWidget(context, provider, services, statuses);
          },
        );
      },
    );
  }

  Widget _buildFilterWidget(BuildContext context, CalendarProvider provider, List<String> services, List<String> statuses) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Column(
        children: [
          // Compact search bar
          Container(
            height: 48,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(24),
              border: Border.all(color: Colors.grey.shade300),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Search icon
                 Padding(
                  padding: EdgeInsets.only(left: 16, right: 8),
                  child: Icon(
                    Icons.search,
                    color: Theme.of(context).primaryColor,
                    size: 20,
                  ),
                ),
                // Search field
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    textCapitalization: TextCapitalization.sentences,
                    decoration: const InputDecoration(
                      hintText: 'Caută client, animal sau serviciu...',
                      hintStyle: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(vertical: 14),
                    ),
                    style: const TextStyle(fontSize: 14),
                    onChanged: (value) {
                      setState(() {}); // Trigger rebuild for clear button
                      provider.setSearchQuery(value);
                    },
                  ),
                ),
                // Clear button (only show when there's text)
                if (_searchController.text.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(right: 4),
                    child: IconButton(
                      icon: const Icon(
                        Icons.clear,
                        color: Colors.grey,
                        size: 18,
                      ),
                      onPressed: () {
                        _searchController.clear();
                        provider.setSearchQuery('');
                        setState(() {});
                      },
                      padding: const EdgeInsets.all(4),
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                    ),
                  ),
                // Filter button
                Container(
                  margin: const EdgeInsets.only(right: 4),
                  child: IconButton(
                    icon: Icon(
                      _isExpanded ? Icons.filter_list : Icons.tune,
                      color: _hasActiveFilters(provider)
                          ? Theme.of(context).primaryColor
                          : Colors.grey.shade600,
                      size: 20,
                    ),
                    onPressed: () {
                      setState(() {
                        _isExpanded = !_isExpanded;
                      });
                    },
                    padding: const EdgeInsets.all(4),
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Compact filters (collapsible)
          if (_isExpanded)
            Container(
              margin: const EdgeInsets.only(top: 8),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Column(
                children: [
                  // Filters in a row for compact layout
                  Row(
                    children: [
                      // Service filter
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Serviciu',
                              style: TextStyle(
                                fontWeight: FontWeight.w500,
                                fontSize: 12,
                                color: Colors.grey.shade700,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Container(
                              height: 36,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.grey.shade300),
                              ),
                              child: DropdownButtonFormField<String>(
                                value: provider.selectedServiceFilter.isEmpty
                                    ? null
                                    : provider.selectedServiceFilter,
                                decoration: const InputDecoration(
                                  border: InputBorder.none,
                                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                ),
                                hint: const Text('Toate', style: TextStyle(fontSize: 12)),
                                isExpanded: true,
                                style: const TextStyle(fontSize: 12, color: Colors.black),
                                items: [
                                  const DropdownMenuItem<String>(
                                    value: '',
                                    child: Text('Toate serviciile'),
                                  ),
                                  ...services.map((service) {
                                    return DropdownMenuItem<String>(
                                      value: service,
                                      child: Text(
                                        service,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    );
                                  }),
                                ],
                                onChanged: (value) {
                                  provider.setServiceFilter(value ?? '');
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 12),
                      // Status filter
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Status',
                              style: TextStyle(
                                fontWeight: FontWeight.w500,
                                fontSize: 12,
                                color: Colors.grey.shade700,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Container(
                              height: 36,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.grey.shade300),
                              ),
                              child: DropdownButtonFormField<String>(
                                value: provider.selectedStatusFilter.isEmpty
                                    ? null
                                    : provider.selectedStatusFilter,
                                decoration: const InputDecoration(
                                  border: InputBorder.none,
                                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                ),
                                hint: const Text('Toate', style: TextStyle(fontSize: 12)),
                                isExpanded: true,
                                style: const TextStyle(fontSize: 12, color: Colors.black),
                                items: [
                                  const DropdownMenuItem<String>(
                                    value: '',
                                    child: Text('Toate statusurile'),
                                  ),
                                  ...statuses.map((status) {
                                    String displayName;
                                    switch (status) {
                                      case 'confirmed':
                                        displayName = 'Confirmat';
                                        break;
                                      case 'pending':
                                        displayName = 'În așteptare';
                                        break;
                                      case 'canceled':
                                        displayName = 'Anulat';
                                        break;
                                      default:
                                        displayName = status;
                                    }
                                    return DropdownMenuItem<String>(
                                      value: status,
                                      child: Text(displayName),
                                    );
                                  }),
                                ],
                                onChanged: (value) {
                                  provider.setStatusFilter(value ?? '');
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  // Clear filters button
                  if (_hasActiveFilters(provider))
                    Padding(
                      padding: const EdgeInsets.only(top: 12),
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: TextButton.icon(
                          onPressed: () {
                            _searchController.clear();
                            provider.clearFilters();
                            setState(() {});
                          },
                          icon: const Icon(Icons.clear_all, size: 16),
                          label: const Text(
                            'Șterge filtrele',
                            style: TextStyle(fontSize: 12),
                          ),
                          style: TextButton.styleFrom(
                            foregroundColor: Theme.of(context).primaryColor,
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            minimumSize: const Size(0, 32),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  bool _hasActiveFilters(CalendarProvider provider) {
    return provider.searchQuery.isNotEmpty ||
           provider.selectedServiceFilter.isNotEmpty ||
           provider.selectedStatusFilter.isNotEmpty;
  }
}
