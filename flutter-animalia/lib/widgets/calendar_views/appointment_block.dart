import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../config/theme/app_theme.dart';
import '../../models/appointment.dart';
import '../../providers/appointment_settings_provider.dart';
import '../../providers/calendar_provider.dart';
import '../../providers/theme_provider.dart';
import '../../services/appointment/appointment_service.dart';
import '../../services/auth/auth_service.dart';
import '../../services/ui_notification_service.dart';
import '../common/subscription_indicator.dart';
import '../../l10n/app_localizations.dart';

class AppointmentBlock extends StatefulWidget {
  final Appointment appointment;
  final double height;
  final VoidCallback? onTap;
  final bool isCompact;
  final String? salonId; // For subscription indicator

  const AppointmentBlock({
    super.key,
    required this.appointment,
    required this.height,
    this.onTap,
    this.isCompact = false,
    this.salonId,
  });

  @override
  State<AppointmentBlock> createState() => _AppointmentBlockState();
}

class _AppointmentBlockState extends State<AppointmentBlock> {
  bool _hovered = false;
  String? _currentSalonId;
  Timer? _countdownTimer;
  int? _remainingSeconds;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _loadSalonId();
    _startCountdownTimer();
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadSalonId() async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (mounted) {
        setState(() {
          _currentSalonId = salonId;
        });
      }
    } catch (e) {
      // Silently handle error - subscription indicator just won't show
    }
  }

  void _startCountdownTimer() {
    // Only start timer for PLACEHOLDER appointments
    if (!widget.appointment.isPlaceholder) return;

    // Initialize remaining seconds
    _remainingSeconds = widget.appointment.placeholderTimeRemaining;

    // Start a timer that updates every second
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      final remaining = widget.appointment.placeholderTimeRemaining;
      if (remaining == null || remaining <= 0) {
        // Placeholder expired
        timer.cancel();
        setState(() {
          _remainingSeconds = null;
        });
      } else {
        setState(() {
          _remainingSeconds = remaining;
        });
      }
    });
  }

  String? get _effectiveSalonId => widget.salonId ?? _currentSalonId;

  /// Remove any stray currency symbols from the appointment time range
  String _formattedTimeRange() {
    return widget.appointment.timeRange.replaceAll('\$', '');
  }

  TextDecoration _getCancellationDecoration() {
    // Apply strike-through to all cancelled appointments regardless of theme
    return _isAppointmentCanceled()
        ? TextDecoration.lineThrough
        : TextDecoration.none;
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        final appointmentColor = _getAppointmentColor(provider);
        final bool darkCancelled =
            _isAppointmentCanceled() && Theme.of(context).brightness == Brightness.dark;
        final staffDisplayName = _getStaffDisplayName(provider);
        final pet = provider.petsCache[widget.appointment.petId];
        final hasMedicalConditions = false;

        // Check if this appointment is highlighted (newly created)
        final isHighlighted = provider.highlightedAppointmentId == widget.appointment.id;

        return MouseRegion(
          onEnter: (_) => setState(() => _hovered = true),
          onExit: (_) => setState(() => _hovered = false),
          child: GestureDetector(
            onTap: widget.onTap,
            child: Stack(
              children: [
                // Modern card design with professional styling
                // Wrap the whole card in a ClipRRect so boxShadow is clipped cleanly to the same radius
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  clipBehavior: Clip.hardEdge,
                  child: Container(
                    height: widget.height,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      // Gradient background for depth
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          appointmentColor,
                          appointmentColor.withValues(alpha: 0.85),
                          appointmentColor.withValues(alpha: 0.95),
                        ],
                        stops: const [0.0, 0.6, 1.0],
                      ),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isHighlighted
                            ? Colors.greenAccent.withValues(alpha: 0.8)
                            : (_hovered
                                ? Colors.white.withValues(alpha: 0.4)
                                : appointmentColor.withValues(alpha: 0.3)),
                        width: isHighlighted ? 2.5 : (_hovered ? 1.5 : 0.5),
                      ),
                      // Minimal, soft ambient shadow to avoid heavy corner artifacts
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: _hovered ? 0.06 : 0.04),
                          blurRadius: _hovered ? 8 : 5,
                          offset: Offset(0, _hovered ? 3 : 2),
                          spreadRadius: 0,
                        ),
                        if (isHighlighted)
                          BoxShadow(
                            color: Colors.greenAccent.withValues(alpha: 0.22),
                            blurRadius: 10,
                            spreadRadius: 1,
                            offset: const Offset(0, 2),
                          ),
                      ],
                    ),
                    child: Stack(
                      children: [
                        // Subtle inner gradient for sophistication (matches clipped radius)
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.white.withValues(alpha: 0.06),
                                Colors.transparent,
                                Colors.black.withValues(alpha: 0.03),
                              ],
                              stops: const [0.0, 0.5, 1.0],
                            ),
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        // Content with refined padding
                        Padding(
                          padding: const EdgeInsets.fromLTRB(12, 8, 12, 8),
                          child: SizedBox(
                            height: widget.height - 16,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // Ultra-compact display for very small blocks
                                if (widget.height <= 25) ...[
                                  Expanded(
                                    child: _buildUltraCompactInfo(
                                        context, staffDisplayName),
                                  ),
                                ] else ...[
                                  // Time range with refined typography
                                  if (!widget.isCompact && widget.height > 60) ...[
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                      decoration: BoxDecoration(
                                        color: Colors.black.withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: _buildTimeRangeWithCompletion(),
                                    ),
                                    const SizedBox(height: 6),
                                  ],

                                  // Main content area with enhanced styling
                                  Expanded(
                                    child: widget.isCompact
                                      ? _buildCompactContent(hasMedicalConditions)
                                      : _buildDayViewInfo(context, staffDisplayName, hasMedicalConditions),
                                  ),

                                  // Bottom elements with refined styling
                                  if (widget.height > 45) ...[
                                    // Cancelled status with modern design
                                    if (_isAppointmentCanceled()) ...[
                                      Container(
                                        margin: const EdgeInsets.only(top: 4),
                                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                        decoration: BoxDecoration(
                                          color: Colors.red.withValues(alpha: 0.2),
                                          borderRadius: BorderRadius.circular(6),
                                          border: Border.all(
                                            color: Colors.red.withValues(alpha: 0.4),
                                            width: 1,
                                          ),
                                        ),
                                        child: Text(
                                          AppLocalizations.of(context)!.translate('calendar.appointments.cancelled').toUpperCase(),
                                          style: TextStyle(
                                            color: Colors.red.shade300,
                                            fontSize: 9,
                                            fontWeight: FontWeight.w700,
                                            letterSpacing: 0.8,
                                          ),
                                        ),
                                      ),
                                    ],
                                    // Modern paid indicator
                                    if (!widget.isCompact && widget.appointment.isPaid) ...[
                                      Container(
                                        margin: const EdgeInsets.only(top: 4),
                                        padding: const EdgeInsets.all(4),
                                        decoration: BoxDecoration(
                                          color: Colors.green.withValues(alpha: 0.2),
                                          borderRadius: BorderRadius.circular(6),
                                          border: Border.all(
                                            color: Colors.green.withValues(alpha: 0.4),
                                            width: 1,
                                          ),
                                        ),
                                        child: Icon(
                                          Icons.check_circle_rounded,
                                          color: Colors.green.shade300,
                                          size: 12,
                                        ),
                                      ),
                                    ],
                                  ]
                                ],
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Enhanced overlay for cancelled appointments
                if (darkCancelled)
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.6),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.grey.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Center(
                        child: Icon(
                          Icons.cancel_rounded,
                          color: Colors.white.withValues(alpha: 0.7),
                          size: 24,
                        ),
                      ),
                    ),
                  ),

                // Enhanced completion time overlay
                if (widget.appointment.shouldShowCompletionShadow)
                  _buildCompletionOverlay(),

                // Subtle shimmer effect for hovered state
                if (_hovered)
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.white.withValues(alpha: 0.1),
                            Colors.transparent,
                            Colors.white.withValues(alpha: 0.05),
                          ],
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          )
        );
      },
    );
  }

  Color _getAppointmentColor(CalendarProvider provider) {
    // Improvement 4: Enhanced color coding with appointment settings
    final baseColor = _getBaseStaffColor(provider);
    final isDark = Theme.of(context).brightness == Brightness.dark;

    // First check for special statuses
    switch (widget.appointment.status.toUpperCase()) {
      case 'CANCELED':
      case 'ANULAT':
      case 'CANCELLED':
        return isDark
            ? const Color(0xFF424242) // Dark gray for cancelled
            : const Color(0xFF9E9E9E); // Light gray for cancelled

      case 'PLACEHOLDER':
        // Greyed out appearance for placeholder appointments
        return isDark
            ? const Color(0xFF616161) // Medium gray for placeholder (dark mode)
            : const Color(0xFFBDBDBD); // Light gray for placeholder (light mode)

      case 'REQUESTED':
        // Distinct color for requested appointments awaiting approval
        return isDark
            ? const Color(0xFF5E35B1) // Deep purple for requested (dark mode)
            : const Color(0xFF9575CD); // Light purple for requested (light mode)
    }

    // Use appointment settings for color determination
    final settingsProvider = context.read<AppointmentSettingsProvider>();
    final isFinalized = widget.appointment.status.toLowerCase() == 'finalizat' ||
                       widget.appointment.status.toLowerCase() == 'completed';

    final colorInfo = settingsProvider.getAppointmentColorInfo(
      widget.appointment.startTime,
      widget.appointment.endTime,
      isFinalized,
    );

    switch (colorInfo.type) {
      case AppointmentColorType.current:
        return Colors.red.shade600; // Current appointments: RED
      case AppointmentColorType.completed:
        // Check if appointment was delayed (completed after scheduled end time)
        if (widget.appointment.isDelayed) {
          return Colors.green.shade800; // Delayed completion: DARKER GREEN
        }
        return Colors.green.shade600; // Completed appointments: GREEN
      case AppointmentColorType.overdue:
        return Colors.red.shade800; // Overdue appointments: DARKER RED
      case AppointmentColorType.scheduled:
        // Apply status-based modifications for future appointments
        switch (widget.appointment.status.toLowerCase()) {
          case 'pending':
          case 'in asteptare':
            return baseColor.withAlpha((baseColor.alpha * 0.6).round());
          case 'confirmed':
          case 'confirmat':
            return baseColor;
          case 'finalizat':
          case 'completed':
            return Colors.green.shade600; // Manually finalized appointments
          case 'rescheduled':
            return baseColor;
          default:
            return baseColor;
        }
    }
  }

  Color _getBaseStaffColor(CalendarProvider provider) {
    // Get staff-based color with elegant fallbacks
    if (widget.appointment.groomerId != null) {
      try {
        final staff = provider.availableStaff.firstWhere(
          (s) => s.id == widget.appointment.groomerId,
        );
        return _getElegantStaffColor(provider.getStaffColor(staff.id));
      } catch (e) {
        // Fallback to name-based lookup
        try {
          final staff = provider.availableStaff.firstWhere(
            (s) => s.name == widget.appointment.assignedGroomer,
          );
          return _getElegantStaffColor(provider.getStaffColor(staff.id));
        } catch (e) {
          return _getElegantStaffColor(AppColors.logoBrown);
        }
      }
    } else {
      // Name-based lookup for backward compatibility
      try {
        final staff = provider.availableStaff.firstWhere(
          (s) => s.name == widget.appointment.assignedGroomer,
        );
        return _getElegantStaffColor(provider.getStaffColor(staff.id));
      } catch (e) {
        return _getElegantStaffColor(Theme.of(context).colorScheme.primary);
      }
    }
  }

  Color _getElegantStaffColor(Color originalColor) {
    // Slight transparency for a modern look
    return originalColor.withAlpha((originalColor.alpha * 0.9).round());
  }

  Color _darkenColor(Color color, double amount) {
    final hsl = HSLColor.fromColor(color);
    return hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0)).toColor();
  }

  Color _lightenColor(Color color, double amount) {
    final hsl = HSLColor.fromColor(color);
    return hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0)).toColor();
  }

  // Helper method to check if appointment is canceled
  bool _isAppointmentCanceled() {
    final status = widget.appointment.status.toLowerCase();
    return status == 'canceled' || status == 'anulat' || status == 'cancelled';
  }

  /// Build the completion time difference overlay
  Widget _buildCompletionOverlay() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          color: widget.appointment.completionOverlayColor,
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  /// Build time range with completion difference inline
  Widget _buildTimeRangeWithCompletion() {
    final timeRange = _formattedTimeRange();
    final completionDiff = widget.appointment.completionTimeDifferenceCompact;

    return RichText(
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      text: TextSpan(
        style: TextStyle(
          color: _getCancelledAppointmentTextColor(context),
          fontSize: 12,
          fontWeight: FontWeight.bold,
          decoration: _getCancellationDecoration(),
        ),
        children: [
          TextSpan(text: timeRange),
          if (completionDiff.isNotEmpty) ...[
            TextSpan(
              text: ' ($completionDiff)',
              style: TextStyle(
                color: widget.appointment.completionTimeDifferenceColor,
                fontSize: 9,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ],
      ),
    );
  }



  // Helper method to get theme-aware cancelled text color
  Color _getCancelledTextColor(BuildContext context) {
    if (Theme.of(context).brightness == Brightness.dark) {
      return Theme.of(context).colorScheme.error.withAlpha(
          (Theme.of(context).colorScheme.error.alpha * 0.9).round());
    } else {
      return Colors.grey.shade700;
    }
  }

  // Helper method to get theme-aware cancelled appointment text color
  Color _getCancelledAppointmentTextColor(BuildContext context, {bool isSecondary = false}) {
    if (_isAppointmentCanceled()) {
      if (Theme.of(context).brightness == Brightness.dark) {
        return isSecondary
            ? Theme.of(context).colorScheme.onSurfaceVariant.withAlpha(
                (Theme.of(context).colorScheme.onSurfaceVariant.alpha * 0.7).round())
            : Theme.of(context).colorScheme.onSurfaceVariant;
      } else {
        return isSecondary ? Colors.grey.shade500 : Colors.grey.shade600;
      }
    }
    return isSecondary ? Colors.white70 : Colors.white;
  }

  /// Build client and pet information display
  Widget _buildClientPetInfo(bool isCompact, bool hasMedicalConditions) {
    final petName = widget.appointment.petName.isNotEmpty
        ? widget.appointment.petName
        : AppLocalizations.of(context)!.translate('calendar.appointments.unknown_pet');
    final breed = widget.appointment.petSpecies.isNotEmpty ? widget.appointment.petSpecies : '';

    if (isCompact) {
      // Compact format with smart truncation
      return
        Column(
          children: [
            Row(
        children: [
          Expanded(
            child: Text(
              petName,
              style: TextStyle(
                color: hasMedicalConditions
                    ? Colors.red.shade500
                    : _getCancelledAppointmentTextColor(context),
                fontSize: 12,
                fontWeight: FontWeight.w600,
                decoration: _getCancellationDecoration(),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          // Subscription indicator for compact view
          if (widget.appointment.hasSubscription && _effectiveSalonId != null) ...[
            const SizedBox(width: 4),
            CompactSubscriptionIndicator(
              appointment: widget.appointment,
              salonId: _effectiveSalonId!,
              color: _getCancelledAppointmentTextColor(context),
            ),
          ],
        ],
      ),
    Row(
        children: [
          Expanded(
            child: Text(
              breed,
              style: TextStyle(
                color: _getCancelledAppointmentTextColor(context),
                fontSize: 10,
                fontWeight: FontWeight.w600,
                decoration: _getCancellationDecoration(),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      )]);
    } else {
      // Non-compact format: Better spacing and readability
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Pet name with breed (secondary)
          if (petName.isNotEmpty) ...[
            const SizedBox(height: 1),
            Text(
              breed.isNotEmpty ? '🐾 $petName ($breed)' : '🐾 $petName',
              style: TextStyle(
                color: _getCancelledAppointmentTextColor(context, isSecondary: true),
                fontSize: 10,
                fontWeight: FontWeight.w500,
                decoration: _getCancellationDecoration(),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      );
    }
  }

  /// Build ultra-compact display for very small appointment blocks
  Widget _buildUltraCompactInfo(BuildContext context, String staffName) {
    final petName = widget.appointment.petName.isNotEmpty
        ? widget.appointment.petName
        : AppLocalizations.of(context)!.translate('calendar.appointments.pet');
    final breed = widget.appointment.petSpecies.isNotEmpty ? widget.appointment.petSpecies : '';
    final serviceName = widget.appointment.services.isNotEmpty
        ? widget.appointment.services.join(', ')
        : (widget.appointment.service.isNotEmpty
            ? widget.appointment.service
            : AppLocalizations.of(context)!.translate('calendar.appointments.service'));
    final staffShort = staffName.isNotEmpty
        ? staffName.split(' ').map((e) => e.isNotEmpty ? e[0] : '').join()
        : '';

    // Smart truncation for ultra-compact display
    final shortPetName = petName.length > 6 ? '${petName.substring(0, 6)}' : petName;
    final shortBreed = breed.length > 8 ? '${breed.substring(0, 8)}' : breed;
    final petLine = breed.isNotEmpty ? '$shortPetName-$shortBreed' : shortPetName;

    // Further truncate if still too long
    final shortPet = petLine.length > 12 ? '${petLine.substring(0, 12)}...' : petLine;
    final shortService = serviceName.length > 8 ? '${serviceName.substring(0, 8)}...' : serviceName;
    final shortStaff = staffShort.length > 3 ? staffShort.substring(0, 3) : staffShort;

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: FittedBox(
        fit: BoxFit.scaleDown,
        alignment: Alignment.centerLeft,
        child: Text(
          '$shortPet | $shortService ${shortStaff.isNotEmpty ? '| $shortStaff' : ''}',
          style: TextStyle(
            color: _getCancelledAppointmentTextColor(context),
            fontSize: 8,
            fontWeight: FontWeight.w600,
            decoration: _getCancellationDecoration(),
          ),
        ),
      ),
    );
  }

  /// Build service information display
  Widget _buildServiceInfo(bool isCompact) {
    final serviceName = widget.appointment.services.isNotEmpty
        ? widget.appointment.services.join(', ')
        : (widget.appointment.service.isNotEmpty
            ? widget.appointment.service
            : AppLocalizations.of(context)!.translate('calendar.appointments.general_service'));

    return Text(
      serviceName,
      style: TextStyle(
        color: _getCancelledAppointmentTextColor(context, isSecondary: true),
        fontSize: isCompact ? 10 : 12,
        fontWeight: FontWeight.w500,
        fontStyle: FontStyle.italic,
        decoration: _getCancellationDecoration(),
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  String _getStaffDisplayName(CalendarProvider provider) {
    final staff = widget.appointment.groomerId != null
        ? provider.availableStaff
            .where((s) => s.id == widget.appointment.groomerId)
            .firstOrNull
        : provider.availableStaff
            .where((s) => s.name == widget.appointment.assignedGroomer)
            .firstOrNull;
    return staff?.displayName ?? widget.appointment.assignedGroomer;
  }

  /// Build compact content for small appointment blocks
  Widget _buildCompactContent(bool hasMedicalConditions) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildClientPetInfo(true, hasMedicalConditions),
          if (widget.height > 35) ...[
            const SizedBox(height: 20),
            _buildServiceInfo(true),
          ],
        ],
      ),
    );
  }

  Widget _buildDayViewInfo(BuildContext context, String staffName, bool hasMedicalConditions) {
    final breed = widget.appointment.petSpecies.isNotEmpty
        ? widget.appointment.petSpecies
        : '';
    // Use SingleChildScrollView to prevent overflow
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Pet name and breed with subscription indicator (highest priority)
          Row(
            children: [
              Expanded(
                child: Text(
                  widget.appointment.petName,
                  style: TextStyle(
                    color: hasMedicalConditions
                        ? Colors.red.shade500
                        : _getCancelledAppointmentTextColor(context),
                    fontSize: widget.height <= 40 ? 12 : 16,
                    fontWeight: FontWeight.bold,
                    decoration: _getCancellationDecoration(),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              // Subscription indicator (always visible if appointment has subscription)
              if (widget.appointment.hasSubscription && _effectiveSalonId != null) ...[
                const SizedBox(width: 4),
                CompactSubscriptionIndicator(
                  appointment: widget.appointment,
                  salonId: _effectiveSalonId!,
                  color: _getCancelledAppointmentTextColor(context),
                ),
              ],
            ],
          ),
          SizedBox(height: 2),
          Row(
            children: [
              Expanded(
                child: Text(
                  breed,
                  style: TextStyle(
                    color: _getCancelledAppointmentTextColor(context),
                    fontSize: widget.height <= 40 ? 10 : 14,
                    fontWeight: FontWeight.bold,
                    decoration: _getCancellationDecoration(),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),

          // Service (second priority)
          if (widget.height > 25) ...[
            const SizedBox(height: 16),
            Text(
              widget.appointment.services.isNotEmpty
                  ? widget.appointment.services.join(', ')
                  : widget.appointment.service,
              style: TextStyle(
                color: _getCancelledAppointmentTextColor(context, isSecondary: true),
                fontSize: widget.height <= 40 ? 11 : 13,
                fontWeight: FontWeight.w500,
                fontStyle: FontStyle.italic,
                decoration: _getCancellationDecoration(),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }

}

