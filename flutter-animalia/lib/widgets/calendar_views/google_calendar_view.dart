import 'dart:math' as math;

import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:showcaseview/showcaseview.dart';

import '../../config/theme/app_theme.dart';
import '../../l10n/app_localizations.dart';
import '../../models/appointment.dart';
import '../../models/salon_subscription.dart';
import '../../models/user_role.dart';
import '../../providers/calendar_provider.dart';
import '../../providers/subscription_provider.dart';
import '../../services/auth/auth_service.dart';
import '../../services/staff_service.dart';
import '../../services/subscription_limit_service.dart';
import '../../services/appointment_subscription_service.dart';
import '../../services/tour_keys.dart';
import '../../services/ui_notification_service.dart';
import '../../utils/genie_animation.dart';
import '../common/custom_bottom_sheet.dart';
import '../dialogs/appointment_details_dialog.dart';
import '../dialogs/block_time_details_sheet.dart';
import '../../services/subscription/revenue_cat_paywall_service.dart';
import 'animated_staff_column.dart';
import 'day_view.dart';
import 'month_view.dart';
import 'week_view.dart';
import '../../utils/weekday_helper.dart';

enum CalendarViewMode { day, week, month }

class GoogleCalendarView extends StatefulWidget {
  final CalendarViewMode currentViewMode;
  final DateTime selectedDate;
  final Function(DateTime, String?)? onTimeSlotTap; // Added staff ID parameter
  final ValueChanged<CalendarViewMode>? onViewModeChange;
  final ValueChanged<DateTime>? onDateChanged;
  final VoidCallback? onRefreshPressed;
  final VoidCallback? onSettingsPressed;

  const GoogleCalendarView({
    Key? key,
    required this.currentViewMode,
    required this.selectedDate,
    this.onTimeSlotTap,
    this.onViewModeChange,
    this.onDateChanged,
    this.onRefreshPressed,
    this.onSettingsPressed,
  }) : super(key: key);

  @override
  State<GoogleCalendarView> createState() => _GoogleCalendarViewState();
}

enum _SwipeDirection { next, previous }

class _GoogleCalendarViewState extends State<GoogleCalendarView> {
  late DateTime _selectedDate;
  _SwipeDirection _swipeDirection = _SwipeDirection.next;
  DateTime? _lastNavigationTime; // Prevent rapid navigation

  // Cache for subscription discount data
  final Map<String, double> _subscriptionDiscountCache = {};

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.selectedDate;
    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDataForCurrentView();
      _preloadSubscriptionDiscounts();
    });
  }

  @override
  void didUpdateWidget(GoogleCalendarView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentViewMode != widget.currentViewMode ||
        oldWidget.selectedDate != widget.selectedDate) {
      if (oldWidget.selectedDate != widget.selectedDate) {
        _selectedDate = widget.selectedDate;
      }
      // Defer data loading to avoid build-time state updates
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _loadDataForCurrentView();
        _preloadSubscriptionDiscounts();
      });
    }
  }

  @override
  void dispose() {
    _subscriptionDiscountCache.clear();
    super.dispose();
  }

  void _loadDataForCurrentView() {
    final provider = Provider.of<CalendarProvider>(context, listen: false);

    switch (widget.currentViewMode) {
      case CalendarViewMode.day:
        provider.fetchAppointmentsForDate(_selectedDate);
        provider.fetchBlockedTimesForDate(_selectedDate);
        break;
      case CalendarViewMode.week:
        _loadWeekData(provider);
        break;
      case CalendarViewMode.month:
        _loadMonthData(provider);
        break;
    }
  }

  void _loadWeekData(CalendarProvider provider) {
    final weekStart = _getWeekStart(_selectedDate);
    DebugLogger.logVerbose('📅 Loading week data with optimized single API calls for week starting: ${weekStart.toString().split(' ')[0]}');

    // Use optimized methods that make single API calls for the entire week
    provider.fetchAppointmentsForWeek(weekStart);
    provider.fetchBlockedTimesForWeek(weekStart);
  }

  void _loadMonthData(CalendarProvider provider) {
    final monthStart = DateTime(_selectedDate.year, _selectedDate.month, 1);
    final monthEnd = DateTime(_selectedDate.year, _selectedDate.month + 1, 0);
    DebugLogger.logVerbose('📅 Loading month data with optimized single API calls for month: ${monthStart.toString().split(' ')[0]} to ${monthEnd.toString().split(' ')[0]}');

    // Use optimized methods that make single API calls for the entire month
    provider.fetchAppointmentsForDateRange(monthStart, monthEnd);
    provider.fetchBlockedTimesForDateRange(monthStart, monthEnd);
  }

  DateTime _getWeekStart(DateTime date) {
    return date.subtract(Duration(days: date.weekday - 1));
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<CalendarProvider>();

    Map<DateTime, List<Appointment>>? monthAppointments;
    _RevenueMetrics? revenueMetrics;

    if (widget.currentViewMode == CalendarViewMode.month) {
      monthAppointments = _groupAppointmentsByMonth(provider);
      revenueMetrics =
          _calculateRevenueMetrics(monthAppointments, _selectedDate);
    }

    return Column(
      children: [
        // Professional Apple Calendar-style header
        _buildHeader(),
        if (widget.currentViewMode == CalendarViewMode.month &&
            revenueMetrics != null &&
            provider.showRevenueSummary)
          _CompactMonthRevenueSummary(
            metrics: revenueMetrics,
            selectedMonth: _selectedDate,
            highRevenueColor: _getHighRevenueColor(context),
            lowRevenueColor: _getLowRevenueColor(context),
            formatRevenue: _formatRevenue,
            getLocalizedMonthShort: _getLocalizedMonthShort,
          ),
        Expanded(
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 350), // Smooth professional duration
            switchInCurve: Curves.easeOutCubic,
            switchOutCurve: Curves.easeInCubic,
            transitionBuilder: (child, animation) {
              // Professional slide + fade + scale animation
              final offsetAnimation = Tween<Offset>(
                begin: _swipeDirection == _SwipeDirection.next
                    ? const Offset(0.15, 0) // Subtle slide from right
                    : const Offset(-0.15, 0), // Subtle slide from left
                end: Offset.zero,
              ).animate(CurvedAnimation(
                parent: animation,
                curve: Curves.easeOutCubic,
              ));

              final fadeAnimation = Tween<double>(
                begin: 0.0,
                end: 1.0,
              ).animate(CurvedAnimation(
                parent: animation,
                curve: const Interval(0.0, 0.65, curve: Curves.easeOut),
              ));

              final scaleAnimation = Tween<double>(
                begin: 0.95,
                end: 1.0,
              ).animate(CurvedAnimation(
                parent: animation,
                curve: Curves.easeOutCubic,
              ));

              return FadeTransition(
                opacity: fadeAnimation,
                child: SlideTransition(
                  position: offsetAnimation,
                  child: ScaleTransition(
                    scale: scaleAnimation,
                    child: child,
                  ),
                ),
              );
            },
            child: Container(
              color: Theme.of(context).colorScheme.surface,
              child: _buildCalendarContent(
                key: ValueKey(
                    '${widget.currentViewMode}-${_selectedDate.toIso8601String()}'),
                provider: provider,
                monthAppointments: monthAppointments,
                revenueMetrics: revenueMetrics,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildViewModeDropdown(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    // Map view modes to their translation keys
    final viewModeLabels = {
      CalendarViewMode.day: l10n.t('calendar.view_mode_day_short'),
      CalendarViewMode.week: l10n.t('calendar.view_mode_week_short'),
      CalendarViewMode.month: l10n.t('calendar.view_mode_month_short'),
    };

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline,
          width: 1,
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<CalendarViewMode>(
          value: widget.currentViewMode,
          icon: Icon(
            Icons.keyboard_arrow_down,
            color: Theme.of(context).colorScheme.onSurface,
            size: 16,
          ),
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurface,
            fontSize: 12,
            fontWeight: FontWeight.normal,
          ),
          dropdownColor: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          items: CalendarViewMode.values.map((CalendarViewMode mode) {
            return DropdownMenuItem<CalendarViewMode>(
              value: mode,
              child: Text(
                viewModeLabels[mode]!,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface,
                  fontSize: 12,
                  fontWeight: mode == widget.currentViewMode
                      ? FontWeight.bold
                      : FontWeight.normal,
                ),
              ),
            );
          }).toList(),
          onChanged: (CalendarViewMode? newMode) {
            if (newMode != null) {
              widget.onViewModeChange?.call(newMode);
            }
          },
        ),
      ),
    );
  }

  Widget _buildHeader() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 400; // Threshold for small screens

    if (isSmallScreen) {
      return _buildCompactHeader();
    } else {
      return _buildStandardHeader();
    }
  }

  Widget _buildStandardHeader() {
    final l10n = AppLocalizations.of(context);

    return Container(
      height: 56,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withOpacity(0.1),
            offset: const Offset(0, 1),
            blurRadius: 3,
          ),
        ],
      ),
      child: Row(
        children: [
          Showcase(
            key: CalendarTourKeys.viewToggleKey,
            title: l10n.t('calendar.view_toggle'),
            description: l10n.t('calendar.view_toggle_description'),
            child: _buildViewModeDropdown(context),
          ),
          Expanded(
            child: Row(
              children: [
                IconButton(
                  onPressed: _goToPrevious,
                  icon: Icon(Icons.chevron_left,
                      color: Theme.of(context).colorScheme.onSurface,
                      size: 30),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(minWidth: 38, minHeight: 38),
                ),
                Expanded(
                  child: Showcase(
                    key: CalendarTourKeys.todayButtonKey,
                    title: l10n.t('calendar.navigation'),
                    description: l10n.t('calendar.navigation_description'),
                    child: GestureDetector(
                      onTap: _goToToday,
                      child: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 250),
                        switchInCurve: Curves.easeInOut,
                        switchOutCurve: Curves.easeInOut,
                        transitionBuilder: (child, animation) {
                          return FadeTransition(
                            opacity: animation,
                            child: ScaleTransition(
                              scale: Tween<double>(begin: 0.92, end: 1.0).animate(
                                CurvedAnimation(
                                  parent: animation,
                                  curve: Curves.easeOutCubic,
                                ),
                              ),
                              child: child,
                            ),
                          );
                        },
                        child: Text(
                          _getNavigationRangeText(),
                          key: ValueKey(_getNavigationRangeText()),
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurface,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                ),
                IconButton(
                  onPressed: _goToNext,
                  icon: Icon(Icons.chevron_right,
                      color: Theme.of(context).colorScheme.onSurface,
                      size: 30),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(minWidth: 38, minHeight: 38),
                ),
              ],
            ),
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 8),
            child: Showcase(
              key: CalendarTourKeys.newSlotKey,
              title: l10n.t('calendar.new_slot_button'),
              description: l10n.t('calendar.new_slot_description'),
              child: ElevatedButton.icon(
                onPressed: () => _addSlotForStaff(),
                icon: Icon(
                  Icons.add,
                  size: 20,
                  color: Theme.of(context).colorScheme.onPrimary,
                ),
                label: Text(
                  l10n.t('calendar.new_slot'),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  elevation: 2,
                  shadowColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                  minimumSize: const Size(0, 36),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactHeader() {
    final l10n = AppLocalizations.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withOpacity(0.1),
            offset: const Offset(0, 1),
            blurRadius: 3,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // First row: View dropdown and Slot nou button
          SizedBox(
            height: 40,
            child: Row(
              children: [
                Showcase(
                  key: CalendarTourKeys.viewToggleKey,
                  title: l10n.t('calendar.view_toggle'),
                  description: l10n.t('calendar.view_toggle_description'),
                  child: _buildViewModeDropdown(context),
                ),
                const Spacer(),
                Showcase(
                  key: CalendarTourKeys.newSlotKey,
                  title: l10n.t('calendar.new_slot_button'),
                  description: l10n.t('calendar.new_slot_description'),
                  child: ElevatedButton(
                    onPressed: () => _addSlotForStaff(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                      elevation: 1,
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      minimumSize: const Size(0, 32),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.add,
                          size: 16,
                          color: Theme.of(context).colorScheme.onPrimary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          l10n.t('calendar.new_slot_button'),
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).colorScheme.onPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 4),
          // Second row: Navigation arrows and date
          SizedBox(
            height: 36,
            child: Row(
              children: [
                IconButton(
                  onPressed: _goToPrevious,
                  icon: Icon(Icons.chevron_left,
                      color: Theme.of(context).colorScheme.onSurface,
                      size: 24),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                ),
                Expanded(
                  child: Showcase(
                    key: CalendarTourKeys.todayButtonKey,
                    title: l10n.t('calendar.navigation'),
                    description: l10n.t('calendar.navigation_description'),
                    child: GestureDetector(
                      onTap: _goToToday,
                      child: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 250),
                        switchInCurve: Curves.easeInOut,
                        switchOutCurve: Curves.easeInOut,
                        transitionBuilder: (child, animation) {
                          return FadeTransition(
                            opacity: animation,
                            child: ScaleTransition(
                              scale: Tween<double>(begin: 0.92, end: 1.0).animate(
                                CurvedAnimation(
                                  parent: animation,
                                  curve: Curves.easeOutCubic,
                                ),
                              ),
                              child: child,
                            ),
                          );
                        },
                        child: Text(
                          _getNavigationRangeText(),
                          key: ValueKey(_getNavigationRangeText()),
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurface,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  ),
                ),
                IconButton(
                  onPressed: _goToNext,
                  icon: Icon(Icons.chevron_right,
                      color: Theme.of(context).colorScheme.onSurface,
                      size: 24),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _addSlotForStaff() async {
    try {
      // First check subscription limits before attempting to create the slot
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null || salonId.isEmpty) {
        throw Exception('No salon ID found');
      }

      // Check if user can add more staff/slots
      final canAddStaff = await SubscriptionLimitService.canAddStaff(salonId);
      if (!canAddStaff) {
        // Show upgrade prompt instead of creating the slot
        if (mounted) {
          final subscriptionProvider = context.read<SubscriptionProvider>();
          _showSlotUpgradePrompt(context, subscriptionProvider, salonId);
        }
        return;
      }

      final provider = context.read<CalendarProvider>();

      DebugLogger.logVerbose('🔄 GoogleCalendarView: Creating new slot...');

      // Store current staff count for animation purposes
      final initialStaffCount = provider.availableStaff.length;

      // Get the current user as the default staff member to create a slot for
      final currentUserId = await AuthService.getCurrentUserId();
      if (currentUserId == null) {
        throw Exception('No current user found');
      }

      // Find the current user in the staff list
      final currentUserStaff = provider.availableStaff.firstWhere(
        (staff) => staff.id == currentUserId,
        orElse: () => provider.availableStaff.isNotEmpty ? provider.availableStaff.first : throw Exception('No staff available'),
      );

      // Calculate the next slot number for this staff member
      final existingSlots = provider.availableStaff.where((staff) =>
        staff.notes?.contains('SLOT_FOR:${currentUserStaff.id}') == true
      ).toList();
      final nextSlotNumber = existingSlots.length + 1;

      // Create proper slot notes with staff reference and slot number
      final slotNotes = 'SLOT_FOR:${currentUserStaff.id}|SLOT_NUMBER:$nextSlotNumber';

      DebugLogger.logVerbose('🎯 Creating slot $nextSlotNumber for ${currentUserStaff.displayName}');
      DebugLogger.logVerbose('📝 Slot notes: $slotNotes');

      final request = CreateStaffDirectlyRequest.fromInput(
        nickname: "Slot",
        groomerRole: GroomerRole.groomer,
        notes: slotNotes, // Add proper slot metadata
      );

      // Call the staff service
      final response = await StaffService.createStaffDirectlyInCurrentSalon(request);

      if (!response.success) {
        throw Exception(response.error ?? 'Failed to create slot staff');
      }

      DebugLogger.logVerbose('✅ Slot created successfully with ID: ${response.data?.id}');

      // Refresh staff data with animation support
      await _refreshStaffDataWithAnimation(provider, initialStaffCount, response.data!.id);

      // Show success message
      if (mounted) {
        final l10n = AppLocalizations.of(context);
        UINotificationService.showSuccess(
          context: context,
          title: l10n.t('calendar.slot_added'),
          message: l10n.t('calendar.slot_added_message', params: {
            'staffName': currentUserStaff.displayName,
            'slotNumber': nextSlotNumber.toString(),
          }),
        );
      }
    } catch (e) {
      DebugLogger.logError('❌ Error creating slot: $e');
      if (mounted) {
        final l10n = AppLocalizations.of(context);
        UINotificationService.showError(
          context: context,
          title: l10n.t('calendar.slot_could_not_be_added'),
          message: 'Eroare: $e'
        );
      }
    }
  }

  /// Refresh staff data with smooth animation for new slot
  Future<void> _refreshStaffDataWithAnimation(CalendarProvider provider, int initialStaffCount, String newSlotId) async {
    if (!mounted) return;

    const maxAttempts = 3;
    const delayBetweenAttempts = Duration(milliseconds: 800);

    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
      DebugLogger.logVerbose('🔄 GoogleCalendarView: Refreshing staff data with animation (attempt $attempt/$maxAttempts)');

      // Refresh staff data
      await provider.refreshStaffData();

      // Check if new staff appeared and is properly positioned
      final currentStaffCount = provider.availableStaff.length;

      if (currentStaffCount > initialStaffCount) {
        DebugLogger.logVerbose('✅ GoogleCalendarView: New slot detected and positioned ($initialStaffCount -> $currentStaffCount)');

        // Mark the new slot for animation
        SlotAnimationTracker.markAsNew(newSlotId);

        // Auto-select the new slot
        provider.selectStaff(newSlotId);

        // Initialize working hours for the new slot
        await _initializeNewSlotWorkingHours(newSlotId, provider);

        // Force calendar refresh with animation trigger
        await provider.forceCalendarRefresh(reason: 'New slot added with animation');

        DebugLogger.logVerbose('✅ GoogleCalendarView: New slot animation completed successfully');
        break;
      }

      if (attempt < maxAttempts) {
        DebugLogger.logVerbose('⏳ GoogleCalendarView: Slot not detected yet, waiting before retry...');
        await Future.delayed(delayBetweenAttempts);
      } else {
        DebugLogger.logVerbose('⚠️ GoogleCalendarView: Slot refresh completed but positioning may need adjustment');
      }
    }
  }

  /// Show upgrade prompt for slot limits
  void _showSlotUpgradePrompt(BuildContext context, SubscriptionProvider subscriptionProvider, String salonId) {
    final l10n = AppLocalizations.of(context);
    final currentTier = subscriptionProvider.currentTier;

    String title;
    String message;
    SubscriptionTier targetTier;

    switch (currentTier) {
      case SubscriptionTier.free:
        title = l10n.t('calendar.slot_limit_reached');
        message = l10n.t('calendar.slot_limit_free_plan');
        targetTier = SubscriptionTier.freelancer;
        break;
      case SubscriptionTier.freelancer:
        title = l10n.t('calendar.slot_limit_reached');
        message = l10n.t('calendar.slot_limit_freelancer_plan');
        targetTier = SubscriptionTier.team;
        break;
      case SubscriptionTier.team:
        title = l10n.t('calendar.slot_limit_reached');
        message = l10n.t('calendar.slot_limit_team_plan');
        targetTier = SubscriptionTier.enterprise;
        break;
      case SubscriptionTier.enterprise:
        // This shouldn't happen for Enterprise tier, but handle gracefully
        title = l10n.t('calendar.limit_reached');
        message = l10n.t('calendar.slot_limit_enterprise_plan');
        targetTier = SubscriptionTier.enterprise;
        break;
      case null:
        // No subscription
        title = l10n.t('calendar.slot_limit_reached');
        message = l10n.t('calendar.slot_limit_no_subscription');
        targetTier = SubscriptionTier.freelancer;
        break;
    }

    // Show new RevenueCat paywall with tabs
    RevenueCatPaywallService.showPaywall(
      context: context,
      defaultTier: targetTier,
      salonId: salonId,
    );
  }

  /// Refresh staff data with retry mechanism to ensure new slot appears and auto-select it
  Future<void> _refreshStaffDataWithRetryAndAutoSelect(CalendarProvider calendarProvider) async {
    if (!mounted) return;

    const maxAttempts = 3;
    const delayBetweenAttempts = Duration(milliseconds: 800);

    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
      DebugLogger.logVerbose('🔄 GoogleCalendarView: Refreshing staff data (attempt $attempt/$maxAttempts)');

      // Get staff count before refresh
      final staffCountBefore = calendarProvider.availableStaff.length;

      // Refresh staff data
      await calendarProvider.refreshStaffData();

      // Check if new staff appeared
      final staffCountAfter = calendarProvider.availableStaff.length;

      if (staffCountAfter > staffCountBefore) {
        DebugLogger.logVerbose('✅ GoogleCalendarView: New slot detected ($staffCountBefore -> $staffCountAfter)');

        // Auto-select the new slot
        await _autoSelectNewSlot(calendarProvider, staffCountBefore);
        break;
      }

      if (attempt < maxAttempts) {
        DebugLogger.logVerbose('⏳ GoogleCalendarView: No new slot detected, waiting before retry...');
        await Future.delayed(delayBetweenAttempts);
      } else {
        DebugLogger.logVerbose('⚠️ GoogleCalendarView: Staff refresh completed but no new slot detected after $maxAttempts attempts');
      }
    }
  }

  /// Auto-select newly added slot
  Future<void> _autoSelectNewSlot(CalendarProvider calendarProvider, int previousStaffCount) async {
    try {
      final currentStaff = calendarProvider.availableStaff;

      if (currentStaff.length > previousStaffCount) {
        // Get the newly added slot (assuming it's the last one in the list)
        final newSlot = currentStaff.last;

        DebugLogger.logVerbose('🎯 GoogleCalendarView: Auto-selecting new slot: ${newSlot.displayName} (${newSlot.id})');

        // Select the new slot
        calendarProvider.selectStaff(newSlot.id);

        // Initialize working hours for the new slot
        await _initializeNewSlotWorkingHours(newSlot.id, calendarProvider);

        // Force calendar refresh to ensure new slot appears in calendar view
        await calendarProvider.forceCalendarRefresh(reason: 'New slot added and selected');

        DebugLogger.logVerbose('✅ GoogleCalendarView: New slot auto-selected and initialized');
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ GoogleCalendarView: Error auto-selecting new slot: $e');
    }
  }

  /// Initialize working hours for newly added slot
  Future<void> _initializeNewSlotWorkingHours(String slotId, CalendarProvider calendarProvider) async {
    try {
      DebugLogger.logVerbose('🔧 GoogleCalendarView: Initializing working hours for new slot: $slotId');

      // Force refresh staff working hours cache for the new slot
      await calendarProvider.refreshStaffWorkingHours(reason: 'New slot added from calendar view');

      DebugLogger.logVerbose('✅ GoogleCalendarView: Working hours initialized for new slot');
    } catch (e) {
      DebugLogger.logVerbose('❌ GoogleCalendarView: Error initializing working hours for new slot: $e');
    }
  }

  /// Preload subscription discount data for all recurring appointments
  Future<void> _preloadSubscriptionDiscounts() async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) return;

      final provider = Provider.of<CalendarProvider>(context, listen: false);

      // Get all appointments for the current view
      Map<DateTime, List<Appointment>> appointmentsByDay;
      switch (widget.currentViewMode) {
        case CalendarViewMode.day:
          appointmentsByDay = {_selectedDate: provider.getFilteredAppointmentsForDate(_selectedDate)};
          break;
        case CalendarViewMode.week:
          appointmentsByDay = _groupAppointmentsByDay(provider);
          break;
        case CalendarViewMode.month:
          appointmentsByDay = _groupAppointmentsByMonth(provider);
          break;
      }

      // Collect all recurring appointments
      final recurringAppointments = <Appointment>[];
      appointmentsByDay.forEach((date, appointments) {
        recurringAppointments.addAll(
          appointments.where((apt) => apt.isRecurring && apt.subscriptionId != null)
        );
      });

      // Fetch subscription data for each and cache the effective price
      for (final appointment in recurringAppointments) {
        if (_subscriptionDiscountCache.containsKey(appointment.id)) continue;

        try {
          final subscription = await AppointmentSubscriptionService.getSubscriptionByAppointmentId(
            salonId,
            appointment.id,
          );

          if (subscription != null &&
              subscription.paymentModel == 'UPFRONT_WITH_DISCOUNT' &&
              subscription.discountPercentage != null &&
              subscription.discountPercentage! > 0) {
            // Calculate and cache discounted price
            final originalPrice = subscription.appointmentPrice;
            final discountPercentage = subscription.discountPercentage!;
            final discountedPrice = originalPrice * (1 - discountPercentage / 100);
            _subscriptionDiscountCache[appointment.id] = discountedPrice;
          } else {
            // Cache original price if no discount
            _subscriptionDiscountCache[appointment.id] = appointment.totalPrice;
          }
        } catch (e) {
          // On error, use original price
          _subscriptionDiscountCache[appointment.id] = appointment.totalPrice;
        }
      }

      // Trigger rebuild to update revenue with cached discounts
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      DebugLogger.logError('Error preloading subscription discounts: $e');
    }
  }

  /// Get effective price for an appointment using cached discount data
  double _getEffectivePriceFromCache(Appointment appointment) {
    // Check cache first
    if (_subscriptionDiscountCache.containsKey(appointment.id)) {
      return _subscriptionDiscountCache[appointment.id]!;
    }
    // Fall back to original price
    return appointment.totalPrice;
  }

  Widget _buildCalendarContent({
    Key? key,
    required CalendarProvider provider,
    Map<DateTime, List<Appointment>>? monthAppointments,
    _RevenueMetrics? revenueMetrics,
  }) {
    return KeyedSubtree(
      key: key,
      child: Builder(
        builder: (context) {
          // Show loading indicator while staff is being loaded initially
          if (provider.isLoadingStaff && provider.availableStaff.isEmpty) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          // Show empty state when:
          // 1. No groomers are selected, OR
          // 2. There was an error loading staff, OR
          // 3. Available staff is empty but no error (edge case)
          // BUT NOT when staff is still loading (prevents flash)
          if (provider.selectedStaff.isEmpty ||
              provider.staffError != null ||
              (provider.availableStaff.isEmpty &&
                  provider.staffError == null &&
                  !provider.isLoadingStaff)) {
            return _buildEmptyStaffState(provider, context);
          }

          switch (widget.currentViewMode) {
            case CalendarViewMode.day:
              return _buildDayView(provider);
            case CalendarViewMode.week:
              return _buildWeekView(provider);
            case CalendarViewMode.month:
              final appointmentsByDay =
                  monthAppointments ?? _groupAppointmentsByMonth(provider);
              final metrics = revenueMetrics ??
                  _calculateRevenueMetrics(appointmentsByDay, _selectedDate);
              return _buildMonthView(
                provider,
                appointmentsByDay,
                metrics,
              );
          }
        },
      ),
    );
  }

  Widget _buildEmptyStaffState(CalendarProvider provider, BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(40),
              ),
              child: Icon(
                Icons.people_outline,
                size: 40,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 24),

            // Title
            Text(
              provider.availableStaff.isEmpty
                  ? l10n.t('calendar.no_groomer_in_team')
                  : l10n.t('calendar.no_groomer_selected'),
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),

            // Description
            Text(
              provider.availableStaff.isEmpty
                  ? l10n.t('calendar.no_groomers_in_team_description')
                  : l10n.t('calendar.no_groomers_selected_description'),
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),

            // Action buttons
            Column(
              children: [
                if (provider.availableStaff.isNotEmpty) ...[
                  // Primary button - Select staff (when staff available)
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () {
                        // Open the drawer
                        widget.onSettingsPressed?.call();
                      },
                      icon: const Icon(Icons.people),
                      label: Text(l10n.t('calendar.check_staff')),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Theme.of(context).colorScheme.onPrimary,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),

                  // Secondary button - Select all
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton.icon(
                      onPressed: () {
                        provider.selectAllStaff();
                      },
                      icon: const Icon(Icons.done_all),
                      label: Text(l10n.t('calendar.check_all_staff', params: {
                        'count': provider.availableStaff.length.toString(),
                      })),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.primary,
                        side: BorderSide(color: Theme.of(context).colorScheme.primary),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                ] else ...[
                  // Button for when no staff available
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () {
                        // Open the drawer to show team management options
                        widget.onSettingsPressed?.call();
                      },
                      icon: const Icon(Icons.group_add),
                      label: Text(l10n.t('calendar.manage_team')),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Theme.of(context).colorScheme.onPrimary,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDayView(CalendarProvider provider) {
    // Get fresh business hours each time to ensure updates are reflected
    final businessHours = provider.getBusinessHours();
    DebugLogger.logVerbose('📅 DayView business hours: openTime=${businessHours['openTime']}, closeTime=${businessHours['closeTime']}');

    return Column(
      children: [
        Expanded(
          child: DayView(
            key: DayView.globalKey,
            selectedDate: _selectedDate,
            appointments: provider.getFilteredAppointmentsForDate(_selectedDate),
            onAppointmentTap: _showAppointmentDetails,
            onBlockTap: _showBlockTimeDetails,
            onTimeSlotTap: widget.onTimeSlotTap,
            businessHours: businessHours,
          ),
        ),
        if (provider.showRevenueSummary)
          _buildCompactRevenueSummary(provider, CalendarViewMode.day),
      ],
    );
  }

  Widget _buildWeekView(CalendarProvider provider) {
    // Get fresh business hours each time to ensure updates are reflected
    final businessHours = provider.getBusinessHours();

    return Column(
      children: [
        Expanded(
          child: WeekView(
            key: WeekView.globalKey,
            selectedWeek: _selectedDate,
            appointmentsByDay: _groupAppointmentsByDay(provider),
            onAppointmentTap: _showAppointmentDetails,
            onBlockTap: _showBlockTimeDetails,
            onTimeSlotTap: widget.onTimeSlotTap,
            onDayTap: _selectDay,
            businessHours: businessHours,
            onSwipeToNext: () => _changeDate(next: true),
            onSwipeToPrevious: () => _changeDate(next: false),
          ),
        ),
        if (provider.showRevenueSummary)
          _buildCompactRevenueSummary(provider, CalendarViewMode.week),
      ],
    );
  }

  Widget _buildMonthView(
    CalendarProvider provider,
    Map<DateTime, List<Appointment>> appointmentsByDay,
    _RevenueMetrics metrics,
  ) {
    return MonthView(
      selectedMonth: _selectedDate,
      appointmentsByDay: appointmentsByDay,
      onAppointmentTap: _showAppointmentDetails,
      onDayTap: _selectDay,
      onSwipeToNext: () => _changeDate(next: true),
      onSwipeToPrevious: () => _changeDate(next: false),
    );
  }

  Map<DateTime, List<Appointment>> _groupAppointmentsByDay(
      CalendarProvider provider) {
    final Map<DateTime, List<Appointment>> grouped = {};

    // For week view, get filtered appointments for each day from the cache
    final weekStart = _getWeekStart(_selectedDate);
    for (int i = 0; i < 7; i++) {
      final day = weekStart.add(Duration(days: i));
      final dayKey = DateTime(day.year, day.month, day.day);

      // Get filtered appointments for this specific day from cache
      grouped[dayKey] = provider.getFilteredAppointmentsForDate(day);
    }

    return grouped;
  }

  Map<DateTime, List<Appointment>> _groupAppointmentsByMonth(
      CalendarProvider provider) {
    final Map<DateTime, List<Appointment>> grouped = {};

    // For month view, get appointments for the entire month
    final firstDayOfMonth = DateTime(_selectedDate.year, _selectedDate.month, 1);
    final lastDayOfMonth = DateTime(_selectedDate.year, _selectedDate.month + 1, 0);

    // Include days from previous/next month that appear in the calendar grid
    final firstDayToShow = firstDayOfMonth.subtract(Duration(days: (firstDayOfMonth.weekday - 1) % 7));
    final lastDayToShow = firstDayToShow.add(const Duration(days: 41)); // 6 weeks = 42 days

    // Load appointments for each day in the visible range
    DateTime currentDay = firstDayToShow;
    while (currentDay.isBefore(lastDayToShow) || currentDay.isAtSameMomentAs(lastDayToShow)) {
      final dayKey = DateTime(currentDay.year, currentDay.month, currentDay.day);
      grouped[dayKey] = provider.getFilteredAppointmentsForDate(currentDay);
      currentDay = currentDay.add(const Duration(days: 1));
    }

    return grouped;
  }

  void _showAppointmentDetails(Appointment appointment) {
    GenieAnimation.showGenieDialog(
      context: context,
      dialog: AppointmentDetailsDialog(appointment: appointment),
    );
  }

  void _showBlockTimeDetails(Map<String, dynamic> block) {
    final l10n = AppLocalizations.of(context);

    CustomBottomSheet.show(
      context: context,
      title: l10n.t('calendar.block_details'),
      isScrollControlled: true,
      child: BlockTimeDetailsSheet(
        block: block,
        isWeekView: widget.currentViewMode == CalendarViewMode.week,
      ),
    );
  }

  void _selectDay(DateTime day) {
    // Switch to day view when a day is selected from week view
    if (widget.currentViewMode != CalendarViewMode.day) {
      widget.onViewModeChange?.call(CalendarViewMode.day);
    }

    setState(() {
      _selectedDate = day;
    });

    widget.onDateChanged?.call(_selectedDate);

    // Data for the new day will be loaded when the view mode updates
  }

  void _changeDate({required bool next}) {
    // Prevent rapid navigation
    final now = DateTime.now();
    if (_lastNavigationTime != null && 
        now.difference(_lastNavigationTime!).inMilliseconds < 150) {
      return;
    }
    _lastNavigationTime = now;
    
    final maxDate = DateTime.now().add(const Duration(days: 360));
    DateTime newDate;
    switch (widget.currentViewMode) {
      case CalendarViewMode.day:
        newDate = next
            ? _selectedDate.add(const Duration(days: 1))
            : _selectedDate.subtract(const Duration(days: 1));
        break;
      case CalendarViewMode.week:
        newDate = next
            ? _selectedDate.add(const Duration(days: 7))
            : _selectedDate.subtract(const Duration(days: 7));
        break;
      case CalendarViewMode.month:
        newDate = DateTime(
          _selectedDate.year,
          _selectedDate.month + (next ? 1 : -1),
          1,
        );
        break;
    }

    if (next && newDate.isAfter(maxDate)) {
      return;
    }

    setState(() {
      _swipeDirection = next ? _SwipeDirection.next : _SwipeDirection.previous;
      _selectedDate = newDate;
    });

    // Update the current visible date in the provider for Google Calendar refresh
    final provider = Provider.of<CalendarProvider>(context, listen: false);
    provider.setCurrentVisibleDate(_selectedDate);
    DebugLogger.logVerbose('📅 Updated current visible date to: ${_selectedDate.toIso8601String()}');

    widget.onDateChanged?.call(_selectedDate);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDataForCurrentView();
    });
  }

  void _goToPrevious() {
    _changeDate(next: false);
  }

  void _goToNext() {
    _changeDate(next: true);
  }

  void _goToToday() {
    final today = DateTime.now();
    setState(() {
      _swipeDirection = today.isAfter(_selectedDate)
          ? _SwipeDirection.next
          : _SwipeDirection.previous;
      _selectedDate = today;
    });

    widget.onDateChanged?.call(_selectedDate);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDataForCurrentView();
      // Call goToToday which will handle both navigation and scrolling
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (widget.currentViewMode == CalendarViewMode.day) {
          DayView.globalKey.currentState?.goToToday();
        } else if (widget.currentViewMode == CalendarViewMode.week) {
          WeekView.globalKey.currentState?.goToToday();
        }
      });
    });
  }

  // Helper methods for header
  String _getNavigationRangeText() {
    final l10n = AppLocalizations.of(context)!;

    switch (widget.currentViewMode) {
      case CalendarViewMode.day:
        final weekdayShort = WeekdayHelper.getWeekdayShort(l10n, _selectedDate.weekday);
        return '${weekdayShort.toUpperCase()} ${_selectedDate.day}';
      case CalendarViewMode.week:
        final weekStart = _getWeekStart(_selectedDate);
        final weekEnd = weekStart.add(const Duration(days: 6));
        final startWeekday = WeekdayHelper.getWeekdayShort(l10n, weekStart.weekday);
        final endWeekday = WeekdayHelper.getWeekdayShort(l10n, weekEnd.weekday);
        return '${startWeekday.toUpperCase()} ${weekStart.day} - ${endWeekday.toUpperCase()} ${weekEnd.day}';
      case CalendarViewMode.month:
        return DateFormat('MMMM yyyy', 'ro').format(_selectedDate);
    }
  }

  // Method to get month/year text for AppBar
  String getMonthYearText() {
    switch (widget.currentViewMode) {
      case CalendarViewMode.day:
        return DateFormat('MMMM yyyy', 'ro').format(_selectedDate);
      case CalendarViewMode.week:
        final weekStart = _getWeekStart(_selectedDate);
        return DateFormat('MMMM yyyy', 'ro').format(weekStart);
      case CalendarViewMode.month:
        return DateFormat('MMMM yyyy', 'ro').format(_selectedDate);
    }
  }

  /// Get the effective price for an appointment considering subscription discounts
  Future<double> _getEffectivePrice(Appointment appointment) async {
    // If not recurring or no subscription, use total price
    if (!appointment.isRecurring || appointment.subscriptionId == null) {
      return appointment.totalPrice;
    }

    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return appointment.totalPrice;
      }

      final subscription = await AppointmentSubscriptionService.getSubscriptionByAppointmentId(
        salonId,
        appointment.id,
      );

      if (subscription != null &&
          subscription.paymentModel == 'UPFRONT_WITH_DISCOUNT' &&
          subscription.discountPercentage != null &&
          subscription.discountPercentage! > 0) {
        // Calculate discounted price
        final originalPrice = subscription.appointmentPrice;
        final discountPercentage = subscription.discountPercentage!;
        final discountedPrice = originalPrice * (1 - discountPercentage / 100);
        return discountedPrice;
      }

      return appointment.totalPrice;
    } catch (e) {
      // On error, fall back to original price
      return appointment.totalPrice;
    }
  }

  _RevenueMetrics _calculateRevenueMetrics(
    Map<DateTime, List<Appointment>> appointmentsByDay,
    DateTime month,
  ) {
    final revenueByDay = <DateTime, double>{};
    double totalRevenue = 0;
    double highestRevenue = 0;
    double? lowestRevenue;
    DateTime? bestDay;
    DateTime? worstDay;
    int trackedDays = 0;

    appointmentsByDay.forEach((date, appointments) {
      // Calculate revenue for the day using cached discount data
      final revenue = appointments.fold<double>(
        0,
        (previousValue, appointment) {
          // Use cached effective price (with discount if applicable)
          return previousValue + _getEffectivePriceFromCache(appointment);
        },
      );
      revenueByDay[date] = revenue;

      if (date.year == month.year && date.month == month.month) {
        totalRevenue += revenue;
        trackedDays += 1;

        if (revenue > highestRevenue) {
          highestRevenue = revenue;
          bestDay = date;
        }

        if (lowestRevenue == null || revenue < lowestRevenue!) {
          lowestRevenue = revenue;
          worstDay = date;
        }
      }
    });

    final averageDailyRevenue = trackedDays == 0 ? 0 : totalRevenue / trackedDays;

    final computedHighThreshold = highestRevenue == 0
        ? 0
        : averageDailyRevenue == 0
            ? highestRevenue
            : averageDailyRevenue +
                (highestRevenue - averageDailyRevenue) * 0.6;

    final adjustedHighThreshold = highestRevenue == 0
        ? 0
        : math.min(computedHighThreshold, highestRevenue);

    double lowRevenueThreshold = 0;
    if (trackedDays > 0 && averageDailyRevenue > 0) {
      lowRevenueThreshold = averageDailyRevenue * 0.6;
      if (lowestRevenue != null && lowestRevenue! > lowRevenueThreshold) {
        lowRevenueThreshold = lowestRevenue!;
      }
    }

    return _RevenueMetrics(
      revenueByDay: revenueByDay,
      totalRevenue: totalRevenue,
      averageDailyRevenue: averageDailyRevenue.toDouble(),
      highRevenueThreshold: adjustedHighThreshold.toDouble(),
      lowRevenueThreshold: lowRevenueThreshold,
      bestDay: bestDay,
      bestRevenue: highestRevenue,
      worstDay: worstDay,
      worstRevenue: lowestRevenue ?? 0,
      trackedDays: trackedDays,
    );
  }

  Color _getHighRevenueColor(BuildContext context) {
    return Theme.of(context).colorScheme.tertiary;
  }

  Color _getLowRevenueColor(BuildContext context) {
    return Theme.of(context).colorScheme.error;
  }

  Widget _buildCompactRevenueSummary(CalendarProvider provider, CalendarViewMode viewMode) {
    // Calculate revenue for the current period
    Map<DateTime, List<Appointment>> appointmentsByDay;
    DateTime startDate, endDate;
    String periodLabel;

    switch (viewMode) {
      case CalendarViewMode.day:
        appointmentsByDay = {_selectedDate: provider.getFilteredAppointmentsForDate(_selectedDate)};
        startDate = endDate = _selectedDate;
        periodLabel = '${_selectedDate.day} ${_getLocalizedMonthShort(context, _selectedDate)}';
        break;
      case CalendarViewMode.week:
        final weekStart = _getWeekStart(_selectedDate);
        final weekEnd = weekStart.add(const Duration(days: 6));
        appointmentsByDay = _groupAppointmentsByDay(provider);
        startDate = weekStart;
        endDate = weekEnd;
        periodLabel = '${weekStart.day}-${weekEnd.day} ${_getLocalizedMonthShort(context, weekEnd)}';
        break;
      default:
        return const SizedBox.shrink();
    }

    final metrics = _calculateRevenueMetrics(appointmentsByDay, _selectedDate);

    // Don't show if no revenue
    if (metrics.totalRevenue == 0) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.insights_outlined,
            size: 16,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Text(
            '$periodLabel: ',
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          Text(
            _formatRevenue(metrics.totalRevenue),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          if (viewMode == CalendarViewMode.week && metrics.trackedDays > 1) ...[
            Text(
              ' • ',
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
              ),
            ),
            Text(
              '${context.tr('calendar.average')}: ${_formatRevenue(metrics.averageDailyRevenue)}',
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _formatRevenue(double value) {
    if (value == 0) {
      return '0 RON';
    }

    if (value >= 1000) {
      return NumberFormat.compactCurrency(
        locale: 'ro_RO',
        symbol: 'RON',
        decimalDigits: 1,
      ).format(value);
    }

    final hasDecimals = value % 1 != 0;
    return NumberFormat.currency(
      locale: 'ro_RO',
      symbol: 'RON',
      decimalDigits: hasDecimals ? 1 : 0,
    ).format(value);
  }

  String _getLocalizedMonthShort(BuildContext context, DateTime date) {
    final monthKeys = [
      'calendar.months.january_short',
      'calendar.months.february_short', 
      'calendar.months.march_short',
      'calendar.months.april_short',
      'calendar.months.may_short',
      'calendar.months.june_short',
      'calendar.months.july_short',
      'calendar.months.august_short',
      'calendar.months.september_short',
      'calendar.months.october_short',
      'calendar.months.november_short',
      'calendar.months.december_short',
    ];
    return context.tr(monthKeys[date.month - 1]);
  }


}


class _RevenueMetrics {
  final Map<DateTime, double> revenueByDay;
  final double totalRevenue;
  final double averageDailyRevenue;
  final double highRevenueThreshold;
  final double lowRevenueThreshold;
  final DateTime? bestDay;
  final double bestRevenue;
  final DateTime? worstDay;
  final double worstRevenue;
  final int trackedDays;

  const _RevenueMetrics({
    required this.revenueByDay,
    required this.totalRevenue,
    required this.averageDailyRevenue,
    required this.highRevenueThreshold,
    required this.lowRevenueThreshold,
    required this.bestDay,
    required this.bestRevenue,
    required this.worstDay,
    required this.worstRevenue,
    required this.trackedDays,
  });
}

class _CompactMonthRevenueSummary extends StatelessWidget {
  final _RevenueMetrics metrics;
  final DateTime selectedMonth;
  final Color highRevenueColor;
  final Color lowRevenueColor;
  final String Function(double) formatRevenue;
  final String Function(BuildContext, DateTime) getLocalizedMonthShort;

  const _CompactMonthRevenueSummary({
    required this.metrics,
    required this.selectedMonth,
    required this.highRevenueColor,
    required this.lowRevenueColor,
    required this.formatRevenue,
    required this.getLocalizedMonthShort,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 8, 16, 0),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.insights_outlined,
            size: 16,
            color: colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Text(
            '${getLocalizedMonthShort(context, selectedMonth)} ${selectedMonth.year}: ',
            style: TextStyle(
              fontSize: 12,
              color: colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          Text(
            formatRevenue(metrics.totalRevenue),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: colorScheme.primary,
            ),
          ),
          const Spacer(),
          if (metrics.trackedDays > 0) ...[
            Text(
              '${context.tr('calendar.average')}: ${formatRevenue(metrics.averageDailyRevenue)}',
              style: TextStyle(
                fontSize: 11,
                color: colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
            const SizedBox(width: 8),
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: highRevenueColor,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 2),
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: colorScheme.outlineVariant,
                shape: BoxShape.circle,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class _SummaryInfoTile extends StatelessWidget {
  final String title;
  final String value;

  const _SummaryInfoTile({
    required this.title,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.outlineVariant.withOpacity(0.4)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 4),
          FittedBox(
            alignment: Alignment.centerLeft,
            fit: BoxFit.scaleDown,
            child: Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _SummaryQuickChip extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final Color color;

  const _SummaryQuickChip({
    required this.icon,
    required this.label,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    final backgroundColor = color.withOpacity(brightness == Brightness.dark ? 0.22 : 0.12);
    final borderColor = color.withOpacity(brightness == Brightness.dark ? 0.5 : 0.35);
    final labelColor = Theme.of(context).colorScheme.onSurfaceVariant;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 10),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(24),
        border: Border.all(color: borderColor),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(icon, size: 18, color: color),
          const SizedBox(width: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 11,
                  color: labelColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w700,
                  color: color,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _LegendItem extends StatelessWidget {
  final Color color;
  final String label;

  const _LegendItem({
    required this.color,
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 14,
          height: 14,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(7),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }
}
