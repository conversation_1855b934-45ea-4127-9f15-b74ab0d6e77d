import 'package:flutter/material.dart';
import 'package:showcaseview/showcaseview.dart';

import '../../services/onboarding/onboarding_tour_service.dart';
import '../../utils/debug_logger.dart';

/// Custom showcase widget with Animalia branding and styling
class TourShowcaseWidget extends StatelessWidget {
  final GlobalKey showcaseKey;
  final String stepId;
  final Widget child;
  final String? title;
  final String? description;
  final String? targetDescription;
  final VoidCallback? onTargetClick;
  final VoidCallback? onToolTipClick;
  final bool disposeOnTap;
  final ShapeBorder? targetShapeBorder;
  final Color? overlayColor;
  final double? overlayOpacity;

  const TourShowcaseWidget({
    super.key,
    required this.showcaseKey,
    required this.stepId,
    required this.child,
    this.title,
    this.description,
    this.targetDescription,
    this.onTargetClick,
    this.onToolTipClick,
    this.disposeOnTap = true,
    this.targetShapeBorder,
    this.overlayColor,
    this.overlayOpacity,
  });

  @override
  Widget build(BuildContext context) {
    final tourContent = OnboardingTourService.getTourContent();
    final stepContent = tourContent[stepId];

    return Showcase(
      key: showcaseKey,
      title: title ?? stepContent?.title ?? '',
      description: description ?? stepContent?.description ?? '',
      targetShapeBorder: targetShapeBorder ?? const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(12)),
      ),
      overlayColor: overlayColor ?? Colors.black,
      overlayOpacity: overlayOpacity ?? 0.7,
      targetBorderRadius: BorderRadius.circular(12),
      tooltipBorderRadius: BorderRadius.circular(16),
      titleTextStyle: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Theme.of(context).primaryColor,
      ),
      descTextStyle: TextStyle(
        fontSize: 16,
        color: Colors.grey[800],
        height: 1.4,
      ),
      tooltipBackgroundColor: Colors.white,
      textColor: Colors.black87,
      targetPadding: const EdgeInsets.all(8),
      tooltipPadding: const EdgeInsets.all(20),
      showArrow: true,
      disableMovingAnimation: false,
      disposeOnTap: disposeOnTap,
      onTargetClick: () {
        // Prevent target click during showcase to avoid dismissing the tour
        DebugLogger.logShowcase('🎯 TourShowcaseWidget: Target click blocked during showcase for step $stepId');
      },
      onToolTipClick: onToolTipClick,
      child: child,
    );
  }
}

/// Custom showcase widget for full-screen overlays
class TourFullScreenShowcase extends StatelessWidget {
  final GlobalKey showcaseKey;
  final String stepId;
  final Widget child;
  final VoidCallback? onComplete;

  const TourFullScreenShowcase({
    super.key,
    required this.showcaseKey,
    required this.stepId,
    required this.child,
    this.onComplete,
  });

  @override
  Widget build(BuildContext context) {
    final tourContent = OnboardingTourService.getTourContent();
    final stepContent = tourContent[stepId];
    
    return Showcase.withWidget(
      key: showcaseKey,
      height: MediaQuery.of(context).size.height * 0.6,
      width: MediaQuery.of(context).size.width * 0.9,
      targetShapeBorder: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(16)),
      ),
      overlayColor: Colors.black,
      overlayOpacity: 0.8,
      container: _buildCustomTooltip(context, stepContent),
      child: child,
    );
  }

  Widget _buildCustomTooltip(BuildContext context, TourStepContent? content) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with logo
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.pets,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  content?.title ?? 'Animalia Tour',
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Description
          Text(
            content?.description ?? '',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[800],
              height: 1.5,
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextButton(
                onPressed: () {
                  OnboardingTourService.markTourSkipped();
                  ShowCaseWidget.of(context).dismiss();
                },
                child: Text(
                  'Omite turul',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 16,
                  ),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  onComplete?.call();
                  ShowCaseWidget.of(context).next();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  'Continuă',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// Progress indicator for the tour
class TourProgressIndicator extends StatelessWidget {
  final int currentStep;
  final int totalSteps;

  const TourProgressIndicator({
    super.key,
    required this.currentStep,
    required this.totalSteps,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.route,
            size: 16,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 8),
          Text(
            'Pasul $currentStep din $totalSteps',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ],
      ),
    );
  }
}

/// Helper widget to wrap sections that need tour highlighting
class TourSection extends StatelessWidget {
  final String stepId;
  final Widget child;
  final String? customTitle;
  final String? customDescription;
  final VoidCallback? onHighlight;

  const TourSection({
    super.key,
    required this.stepId,
    required this.child,
    this.customTitle,
    this.customDescription,
    this.onHighlight,
  });

  @override
  Widget build(BuildContext context) {
    final showcaseKey = GlobalKey();
    
    return TourShowcaseWidget(
      showcaseKey: showcaseKey,
      stepId: stepId,
      title: customTitle,
      description: customDescription,
      onTargetClick: onHighlight,
      child: child,
    );
  }
}
