/// Fallback translations to prevent app crashes when translation files are missing
class FallbackTranslations {
  static Map<String, String> getTranslations(String languageCode) {
    switch (languageCode) {
      case 'en':
        return _englishTranslations;
      case 'ro':
      default:
        return _romanianTranslations;
    }
  }

  static const Map<String, String> _romanianTranslations = {
    // SMS Template Types
    'sms_templates.appointment_confirmation': 'Confirmare Programare',
    'sms_templates.appointment_cancellation': 'Anulare Programare',
    'sms_templates.appointment_reschedule': 'Reprogramare',
    'sms_templates.reminder': 'Reminder',
    'sms_templates.appointment_completion': 'Finalizare Programare',
    'sms_templates.follow_up': 'Follow-up',
    
    // Schedule Step
    'schedule_step.configure_schedule': 'Configurează Programul',
    'schedule_step.merged_from_staff': 'Programul a fost generat inteligent din programele personalului',
    'schedule_step.loading_schedules': 'Se încarcă programele...',
    'schedule_step.error_loading': '<PERSON><PERSON>re la încărcarea programelor',
    'schedule_step.retry': 'Încearcă din nou',
    'schedule_step.quick_actions': 'Acțiuni rapide',
    'schedule_step.standard_hours': 'Program Standard',
    'schedule_step.extended_hours': 'Program Extins',
    'schedule_step.always_open': 'Mereu Deschis',
    'schedule_step.closed': 'Închis',
    'schedule_step.add_interval': 'Adaugă interval',
    'schedule_step.remove_interval': 'Elimină interval',
    'schedule_step.edit_interval': 'Editează Intervalul',
    'schedule_step.start_time': 'Ora de început',
    'schedule_step.end_time': 'Ora de sfârșit',
    'schedule_step.cancel': 'Anulează',
    'schedule_step.save': 'Salvează',
    'schedule_step.back': 'Înapoi',
    'schedule_step.continue': 'Continuă',

    // Common
    'common.loading': 'Se încarcă...',
    'common.error': 'Eroare',
    'common.success': 'Succes',
    'common.cancel': 'Anulează',
    'common.save': 'Salvează',
    'common.delete': 'Șterge',
    'common.edit': 'Editează',
    'common.add': 'Adaugă',
    'common.ok': 'OK',
    'common.yes': 'Da',
    'common.no': 'Nu',
  };

  static const Map<String, String> _englishTranslations = {
    // SMS Template Types
    'sms_templates.appointment_confirmation': 'Appointment Confirmation',
    'sms_templates.appointment_cancellation': 'Appointment Cancellation',
    'sms_templates.appointment_reschedule': 'Appointment Reschedule',
    'sms_templates.reminder': 'Reminder',
    'sms_templates.appointment_completion': 'Appointment Completion',
    'sms_templates.follow_up': 'Follow-up',
    
    // Schedule Step
    'schedule_step.configure_schedule': 'Configure Schedule',
    'schedule_step.merged_from_staff': 'Schedule intelligently generated from staff schedules',
    'schedule_step.loading_schedules': 'Loading schedules...',
    'schedule_step.error_loading': 'Error loading schedules',
    'schedule_step.retry': 'Retry',
    'schedule_step.quick_actions': 'Quick Actions',
    'schedule_step.standard_hours': 'Standard Hours',
    'schedule_step.extended_hours': 'Extended Hours',
    'schedule_step.always_open': 'Always Open',
    'schedule_step.closed': 'Closed',
    'schedule_step.add_interval': 'Add Interval',
    'schedule_step.remove_interval': 'Remove interval',
    'schedule_step.edit_interval': 'Edit Interval',
    'schedule_step.start_time': 'Start time',
    'schedule_step.end_time': 'End time',
    'schedule_step.cancel': 'Cancel',
    'schedule_step.save': 'Save',
    'schedule_step.back': 'Back',
    'schedule_step.continue': 'Continue',

    // Common
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.cancel': 'Cancel',
    'common.save': 'Save',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.add': 'Add',
    'common.ok': 'OK',
    'common.yes': 'Yes',
    'common.no': 'No',
  };
}