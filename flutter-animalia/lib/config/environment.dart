import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:flutter/foundation.dart';

/// Environment configuration for the Animalia Grooming app
/// Handles different deployment environments and their specific configurations

enum Environment {
  development,
  staging,
  production
}

class EnvironmentConfig {
  // Current environment - automatically detected based on build configuration
  static Environment? _currentEnvironment;

  /// Automatically detect environment based on build configuration
  static Environment _detectEnvironment() {
    // Check for compile-time environment variables first
    // Use const String.fromEnvironment to ensure compile-time evaluation
    const String envFromBuild = String.fromEnvironment('FLUTTER_ENV', defaultValue: '');
    if (envFromBuild.isNotEmpty) {
      switch (envFromBuild.toLowerCase()) {
        case 'production':
        case 'prod':
          return Environment.production;
        case 'staging':
        case 'stage':
          return Environment.staging;
        case 'development':
        case 'dev':
        default:
          return Environment.development;
      }
    }

    // Fallback to debug mode detection
    if (kReleaseMode) {
      return Environment.production;
    } else if (kProfileMode) {
      return Environment.staging;
    } else {
      return Environment.development;
    }
  }

  /// Get the current environment
  static Environment get currentEnvironment {
    _currentEnvironment ??= _detectEnvironment();
    return _currentEnvironment!;
  }

  /// Force set environment (for testing purposes)
  static void setEnvironment(Environment env) {
    _currentEnvironment = env;
  }

  /// Get the API base URL for the current environment
  static String get apiBaseUrl {
    if (kIsWeb && !kDebugMode) {
      return 'https://tova-animalia-programari.onrender.com'; // use staging for web
    }
    switch (currentEnvironment) {
      case Environment.development:
        return 'http://localhost:8081'; // Local development server for E2E testing
      case Environment.staging:
        return 'http://localhost:8080'; // 5 times slower than production but same database

      case Environment.production:
        return 'http://localhost:8080'; // Production server
    }
  }

  /// Get the Firebase configuration for the current environmen6
  static Map<String, String> get firebaseConfig {
    switch (currentEnvironment) {
      case Environment.development:
        return {
          'projectId': 'animalia-de0f1',
          'appId': '1:166674682070:ios:838930ed46df3a1213f977',
          'iosAppId': '1:166674682070:ios:838930ed46df3a1213f977',
          'androidAppId': '1:166674682070:android:bcc1fbe6d20652b613f977',
          'webAppId': '1:166674682070:web:838930ed46df3a1213f977',
          'apiKey': 'AIzaSyDxf3m4Aleb-Z26-LQ_tiOwDDuu_Ka0JnQ',
          'messagingSenderId': '166674682070',
          'authDomain': 'animalia-de0f1.firebaseapp.com',
          'googleClientId': '166674682070-hfhkp61b4itaqs5t332kb4seg2u34o01.apps.googleusercontent.com',
        };
      case Environment.staging:
        return {
          'projectId': 'animalia-de0f1',
          'appId': '1:166674682070:ios:838930ed46df3a1213f977',
          'iosAppId': '1:166674682070:ios:838930ed46df3a1213f977',
          'androidAppId': '1:166674682070:android:bcc1fbe6d20652b613f977',
          'webAppId': '1:166674682070:web:838930ed46df3a1213f977',
          'apiKey': 'AIzaSyBRxNAuAx7Y4QVHM2-plOHOqAUNtb9JXNg',
          'messagingSenderId': '166674682070',
          'authDomain': 'animalia-de0f1.firebaseapp.com',
          'googleClientId': '166674682070-hfhkp61b4itaqs5t332kb4seg2u34o01.apps.googleusercontent.com',
        };
      case Environment.production:
        return {
          'projectId': 'animalia-de0f1',
          'appId': '1:166674682070:ios:838930ed46df3a1213f977',
          'iosAppId': '1:166674682070:ios:838930ed46df3a1213f977',
          'androidAppId': '1:166674682070:android:bcc1fbe6d20652b613f977',
          'webAppId': '1:166674682070:web:838930ed46df3a1213f977',
          'apiKey': 'AIzaSyBRxNAuAx7Y4QVHM2-plOHOqAUNtb9JXNg',
          'messagingSenderId': '166674682070',
          'authDomain': 'animalia-de0f1.firebaseapp.com',
          'googleClientId': '166674682070-hfhkp61b4itaqs5t332kb4seg2u34o01.apps.googleusercontent.com',
        };
    }
  }

  /// Google Maps & Places API key used for location features
  static String get googleMapsApiKey {
    switch (currentEnvironment) {
      case Environment.development:
        return 'AIzaSyCO77ldStnRCjfZ3EThONj8F8X6d3EVWvI';
      case Environment.staging:
        return 'AIzaSyCO77ldStnRCjfZ3EThONj8F8X6d3EVWvI';
      case Environment.production:
        return 'AIzaSyCO77ldStnRCjfZ3EThONj8F8X6d3EVWvI';
    }
  }

  /// Get the app name for the current environment
  static String get appName {
    switch (currentEnvironment) {
      case Environment.development:
        return 'Animalia Programari (Dev)';
      case Environment.staging:
        return 'Animalia Programari (Staging)';
      case Environment.production:
        return 'Animalia Programari';
    }
  }

  /// Get the bundle ID for the current environment
  static String get bundleId {
    switch (currentEnvironment) {
      case Environment.development:
        return 'ro.animaliaprogramari.animalia';
      case Environment.staging:
        return 'ro.animaliaprogramari.animalia';
      case Environment.production:
        return 'ro.animaliaprogramari.animalia';
    }
  }

  /// Check if the current environment is development
  static bool get isDevelopment => currentEnvironment == Environment.development;

  /// Check if the current environment is staging
  static bool get isStaging => currentEnvironment == Environment.staging;

  /// Check if the current environment is production
  static bool get isProduction => currentEnvironment == Environment.production;

  /// Get debug mode status
  static bool get isDebugMode => isDevelopment || isStaging;

  /// Get API timeout duration
  static Duration get apiTimeout {
    switch (currentEnvironment) {
      case Environment.development:
        return const Duration(seconds: 60); // Increased for Android debugging
      case Environment.staging:
        return const Duration(seconds: 30);
      case Environment.production:
        return const Duration(seconds: 15);
    }
  }

  /// Get logging level
  static String get logLevel {
    switch (currentEnvironment) {
      case Environment.development:
        return 'DEBUG';
      case Environment.staging:
        return 'INFO';
      case Environment.production:
        return 'ERROR';
    }
  }

  /// Print current environment configuration (debug mode only)
  static void printConfig() {
    if (isDebugMode) {
      DebugLogger.logInit('🌍 Environment: ${currentEnvironment.name}');
      DebugLogger.logInit('🔗 API Base URL: $apiBaseUrl');
      DebugLogger.logInit('📱 App Name: $appName');
      DebugLogger.logInit('📦 Bundle ID: $bundleId');
      DebugLogger.logInit('⏱️ API Timeout: ${apiTimeout.inSeconds}s');
      DebugLogger.logInit('📝 Log Level: $logLevel');
    }
  }
}

/// Convenience alias for environment checks
class EnvironmentAlias {
  static bool get isProduction => EnvironmentConfig.isProduction;
  static bool get isDevelopment => EnvironmentConfig.isDevelopment;
  static bool get isStaging => EnvironmentConfig.isStaging;
}
