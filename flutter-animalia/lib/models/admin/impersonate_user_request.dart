/// Request model for user impersonation
class ImpersonateUserRequest {
  final String targetUserId;
  final String? reason;

  const ImpersonateUserRequest({
    required this.targetUserId,
    this.reason,
  });

  /// Create ImpersonateUserRequest from JSON
  factory ImpersonateUserRequest.fromJson(Map<String, dynamic> json) {
    return ImpersonateUserRequest(
      targetUserId: json['targetUserId'] as String,
      reason: json['reason'] as String?,
    );
  }

  /// Convert ImpersonateUserRequest to JSON
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'targetUserId': targetUserId,
    };

    if (reason != null && reason!.isNotEmpty) {
      json['reason'] = reason;
    }

    return json;
  }

  /// Create a copy with updated values
  ImpersonateUserRequest copyWith({
    String? targetUserId,
    String? reason,
  }) {
    return ImpersonateUserRequest(
      targetUserId: targetUserId ?? this.targetUserId,
      reason: reason ?? this.reason,
    );
  }

  /// Validate the impersonation request
  bool get isValid {
    return targetUserId.trim().isNotEmpty;
  }

  /// Get validation error message
  String? get validationError {
    if (targetUserId.trim().isEmpty) {
      return 'ID-ul utilizatorului țintă este obligatoriu';
    }
    if (reason != null && reason!.length > 500) {
      return 'Motivul nu poate depăși 500 de caractere';
    }
    return null;
  }

  /// Check if reason is provided
  bool get hasReason {
    return reason != null && reason!.trim().isNotEmpty;
  }

  /// Get formatted reason for display
  String get displayReason {
    if (hasReason) {
      return reason!.trim();
    }
    return 'Fără motiv specificat';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ImpersonateUserRequest &&
          runtimeType == other.runtimeType &&
          targetUserId == other.targetUserId &&
          reason == other.reason;

  @override
  int get hashCode => targetUserId.hashCode ^ reason.hashCode;

  @override
  String toString() {
    return 'ImpersonateUserRequest{targetUserId: $targetUserId, reason: $reason}';
  }
}
