/// Models for online booking flow

/// Represents an available time slot for booking
class TimeSlot {
  final String startTime; // HH:mm format
  final String endTime; // HH:mm format
  final List<String> availableStaffIds;

  TimeSlot({
    required this.startTime,
    required this.endTime,
    required this.availableStaffIds,
  });

  factory TimeSlot.fromJson(Map<String, dynamic> json) {
    return TimeSlot(
      startTime: json['startTime'] as String,
      endTime: json['endTime'] as String,
      availableStaffIds: (json['availableStaffIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'startTime': startTime,
      'endTime': endTime,
      'availableStaffIds': availableStaffIds,
    };
  }

  bool get isAvailable => availableStaffIds.isNotEmpty;
}

/// Response containing available time slots for a specific date
class AvailableTimeSlotsResponse {
  final String date; // yyyy-MM-dd format
  final List<TimeSlot> slots;

  AvailableTimeSlotsResponse({
    required this.date,
    required this.slots,
  });

  factory AvailableTimeSlotsResponse.fromJson(Map<String, dynamic> json) {
    return AvailableTimeSlotsResponse(
      date: json['date'] as String,
      slots: (json['slots'] as List<dynamic>)
          .map((e) => TimeSlot.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date,
      'slots': slots.map((s) => s.toJson()).toList(),
    };
  }

  List<TimeSlot> get availableSlots => slots.where((s) => s.isAvailable).toList();
}

/// Request to create a proto-appointment to reserve a time slot
class CreateProtoAppointmentRequest {
  final String date; // yyyy-MM-dd format
  final String startTime; // HH:mm format
  final String endTime; // HH:mm format
  final List<String> serviceIds;

  CreateProtoAppointmentRequest({
    required this.date,
    required this.startTime,
    required this.endTime,
    required this.serviceIds,
  });

  Map<String, dynamic> toJson() {
    return {
      'date': date,
      'startTime': startTime,
      'endTime': endTime,
      'serviceIds': serviceIds,
    };
  }
}

/// Response after creating a proto-appointment
class ProtoAppointmentResponse {
  final String appointmentId;
  final String expiresAt; // ISO 8601 datetime

  ProtoAppointmentResponse({
    required this.appointmentId,
    required this.expiresAt,
  });

  factory ProtoAppointmentResponse.fromJson(Map<String, dynamic> json) {
    return ProtoAppointmentResponse(
      appointmentId: json['appointmentId'] as String,
      expiresAt: json['expiresAt'] as String,
    );
  }

  DateTime get expirationTime => DateTime.parse(expiresAt);
}

/// Request to finalize a booking
class FinalizeBookingRequest {
  final String appointmentId;
  final String clientPhone;
  final String petName;
  final String petSpecies;
  final String petBreed;
  final String petSize;
  final String? clientName;
  final String? notes;
  final String? password;

  FinalizeBookingRequest({
    required this.appointmentId,
    required this.clientPhone,
    required this.petName,
    required this.petSpecies,
    required this.petBreed,
    required this.petSize,
    this.clientName,
    this.notes,
    this.password,
  });

  Map<String, dynamic> toJson() {
    return {
      'appointmentId': appointmentId,
      'clientPhone': clientPhone,
      'petName': petName,
      'petSpecies': petSpecies,
      'petBreed': petBreed,
      'petSize': petSize,
      if (clientName != null) 'clientName': clientName,
      if (notes != null) 'notes': notes,
      if (password != null) 'password': password,
    };
  }
}

/// Response after finalizing a booking
class BookingResponse {
  final String appointmentId;
  final String status; // SCHEDULED or REQUESTED
  final bool isAutoConfirmed;
  final String message;

  BookingResponse({
    required this.appointmentId,
    required this.status,
    required this.isAutoConfirmed,
    required this.message,
  });

  factory BookingResponse.fromJson(Map<String, dynamic> json) {
    return BookingResponse(
      appointmentId: json['appointmentId'] as String,
      status: json['status'] as String,
      isAutoConfirmed: json['isAutoConfirmed'] as bool,
      message: json['message'] as String,
    );
  }

  bool get needsConfirmation => !isAutoConfirmed;
}

/// Booking flow state to pass between screens
class BookingFlowState {
  final String salonId;
  final List<String> selectedServiceIds;
  final String? selectedDate;
  final String? selectedStartTime;
  final String? selectedEndTime;
  final String? appointmentId;
  final DateTime? appointmentExpiresAt;

  BookingFlowState({
    required this.salonId,
    this.selectedServiceIds = const [],
    this.selectedDate,
    this.selectedStartTime,
    this.selectedEndTime,
    this.appointmentId,
    this.appointmentExpiresAt,
  });

  BookingFlowState copyWith({
    String? salonId,
    List<String>? selectedServiceIds,
    String? selectedDate,
    String? selectedStartTime,
    String? selectedEndTime,
    String? appointmentId,
    DateTime? appointmentExpiresAt,
  }) {
    return BookingFlowState(
      salonId: salonId ?? this.salonId,
      selectedServiceIds: selectedServiceIds ?? this.selectedServiceIds,
      selectedDate: selectedDate ?? this.selectedDate,
      selectedStartTime: selectedStartTime ?? this.selectedStartTime,
      selectedEndTime: selectedEndTime ?? this.selectedEndTime,
      appointmentId: appointmentId ?? this.appointmentId,
      appointmentExpiresAt: appointmentExpiresAt ?? this.appointmentExpiresAt,
    );
  }

  bool get hasSelectedServices => selectedServiceIds.isNotEmpty;
  bool get hasSelectedDateTime => selectedDate != null && selectedStartTime != null && selectedEndTime != null;
  bool get hasProtoAppointment => appointmentId != null;
  bool get isProtoAppointmentExpired => appointmentExpiresAt != null && DateTime.now().isAfter(appointmentExpiresAt!);
}

