class Client {
  final String id;
  final String name;
  final String phone;
  final String? secondaryPhone; // Added secondary phone field
  final String email;
  final String address;
  final DateTime registrationDate;
  final List<String> petIds; // References to pets
  final List<String> petNames;
  final List<String> matchingPetNames;
  final bool matchedViaPetName;
  final List<ClientPet> pets; // Detailed pet information
  final String notes;
  final bool isActive; // New field from API
  final int petCount; // New field from API
  final DateTime? updatedAt; // New field from API

  Client({
    required this.id,
    required this.name,
    required this.phone,
    this.secondaryPhone,
    this.email = '',
    this.address = '',
    required this.registrationDate,
    this.petIds = const [],
    this.petNames = const [],
    this.matchingPetNames = const [],
    this.matchedViaPetName = false,
    this.pets = const [],
    this.notes = '',
    this.isActive = true,
    this.petCount = 0,
    this.updatedAt,
  });

  // Convert from JSON
  factory Client.fromJson(Map<String, dynamic> json) {
    return Client(
      id: json['id']?.toString() ?? json['clientId']?.toString() ?? '',
      name: json['name'] ?? '',
      phone: json['phone'] ?? '',
      secondaryPhone: json['secondaryPhone'],
      email: json['email'] ?? '',
      address: json['address'] ?? '',
      registrationDate: DateTime.parse(json['registrationDate'] ?? json['createdAt'] ?? DateTime.now().toIso8601String()),
      petIds: List<String>.from(json['petIds'] ?? []),
      petNames: List<String>.from(json['petNames'] ?? const []),
      matchingPetNames: List<String>.from(json['matchingPetNames'] ?? const []),
      matchedViaPetName: json['matchedViaPetName'] ?? (json['matchingPetNames'] != null && (json['matchingPetNames'] as List).isNotEmpty),
      pets: (json['pets'] as List<dynamic>?)?.map((petJson) => ClientPet.fromJson(petJson as Map<String, dynamic>)).toList() ?? const [],
      notes: json['notes'] ?? '',
      isActive: json['isActive'] ?? true,
      petCount: json['petCount'] ?? 0,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'secondaryPhone': secondaryPhone,
      'email': email,
      'address': address,
      'registrationDate': registrationDate.toIso8601String(),
      'petIds': petIds,
      'petNames': petNames,
      'matchingPetNames': matchingPetNames,
      'matchedViaPetName': matchedViaPetName,
      'pets': pets.map((pet) => pet.toJson()).toList(),
      'notes': notes,
      'isActive': isActive,
      'petCount': petCount,
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  // Get all phone numbers as a list
  List<String> get allPhoneNumbers {
    final phones = <String>[];
    if (phone.isNotEmpty) phones.add(phone);
    if (secondaryPhone != null && secondaryPhone!.isNotEmpty) {
      // Split secondary phones by comma
      phones.addAll(secondaryPhone!.split(',').map((p) => p.trim()).where((p) => p.isNotEmpty));
    }
    return phones;
  }

  // Get primary phone number (first one)
  String get primaryPhone => phone;

  // Get secondary phone numbers as list
  List<String> get secondaryPhoneNumbers {
    if (secondaryPhone == null || secondaryPhone!.isEmpty) return [];
    return secondaryPhone!.split(',').map((p) => p.trim()).where((p) => p.isNotEmpty).toList();
  }

  // Copy with method for immutable updates
  Client copyWith({
    String? id,
    String? name,
    String? phone,
    String? secondaryPhone,
    String? email,
    String? address,
    DateTime? registrationDate,
    List<String>? petIds,
    List<String>? petNames,
    List<String>? matchingPetNames,
    bool? matchedViaPetName,
    List<ClientPet>? pets,
    String? notes,
    bool? isActive,
    int? petCount,
    DateTime? updatedAt,
  }) {
    return Client(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      secondaryPhone: secondaryPhone ?? this.secondaryPhone,
      email: email ?? this.email,
      address: address ?? this.address,
      registrationDate: registrationDate ?? this.registrationDate,
      petIds: petIds ?? this.petIds,
      petNames: petNames ?? this.petNames,
      matchingPetNames: matchingPetNames ?? this.matchingPetNames,
      matchedViaPetName: matchedViaPetName ?? this.matchedViaPetName,
      pets: pets ?? this.pets,
      notes: notes ?? this.notes,
      isActive: isActive ?? this.isActive,
      petCount: petCount ?? this.petCount,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class ClientPet {
  final String id;
  final String name;
  final String? breed;
  final String? species;
  final String? size;

  const ClientPet({
    required this.id,
    required this.name,
    this.breed,
    this.species,
    this.size,
  });

  factory ClientPet.fromJson(Map<String, dynamic> json) {
    // Handle case where species is string "null" instead of actual null
    String? species = json['species'] as String?;
    if (species == 'null') {
      species = null;
    }

    String? breed = json['breed'] as String?;
    if (breed == 'null') {
      breed = null;
    }

    String? size = json['size'] as String?;
    if (size == 'null') {
      size = null;
    }

    return ClientPet(
      id: json['id'] as String? ?? '',
      name: json['name'] as String? ?? '',
      breed: breed,
      species: species,
      size: size,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'breed': breed,
      'species': species,
      'size': size,
    };
  }
}
