import 'dart:convert';
import 'package:flutter/foundation.dart';

/// Model for salon website management preferences
class SalonWebPreferences {
  final String? id;
  final String salonId;
  
  // Basic Info
  final String bookingWebsiteSlug;
  final String businessName;
  final String businessDescription;
  final String businessAddress;
  final String contactPhone;
  final String contactEmail;
  final String facebookLink;
  final String instagramLink;
  final String tiktokLink;
  
  // Website Customization
  final String logoUrl;
  final String primaryColor;
  final List<String> websitePhotos;
  
  // Business Hours
  final Map<String, Map<String, dynamic>> businessHours;
  
  // Extra Info
  final CancellationPolicy cancellationPolicy;
  
  // Booking Acceptance
  final BookingAcceptance bookingAcceptance;

  // Online Booking Settings
  final bool onlineBookingEnabled;
  final List<String> availableServiceIds;
  final String bookingPassword;

  // Metadata
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const SalonWebPreferences({
    this.id,
    required this.salonId,
    this.bookingWebsiteSlug = '',
    this.businessName = '',
    this.businessDescription = '',
    this.businessAddress = '',
    this.contactPhone = '',
    this.contactEmail = '',
    this.facebookLink = '',
    this.instagramLink = '',
    this.tiktokLink = '',
    this.logoUrl = '',
    this.primaryColor = '#6366F1',
    this.websitePhotos = const [],
    this.businessHours = const {},
    this.cancellationPolicy = CancellationPolicy.HOURS_24,
    this.bookingAcceptance = BookingAcceptance.automatic,
    this.onlineBookingEnabled = true,
    this.availableServiceIds = const [],
    this.bookingPassword = '',
    this.isActive = true,
    this.createdAt,
    this.updatedAt,
  });

  /// Create a copy with updated fields
  SalonWebPreferences copyWith({
    String? id,
    String? salonId,
    String? bookingWebsiteUrl,
    String? businessName,
    String? businessDescription,
    String? businessAddress,
    String? contactPhone,
    String? contactEmail,
    String? facebookLink,
    String? instagramLink,
    String? tiktokLink,
    String? logoUrl,
    String? primaryColor,
    List<String>? websitePhotos,
    Map<String, Map<String, dynamic>>? businessHours,
    CancellationPolicy? cancellationPolicy,
    BookingAcceptance? bookingAcceptance,
    bool? onlineBookingEnabled,
    List<String>? availableServiceIds,
    String? bookingPassword,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SalonWebPreferences(
      id: id ?? this.id,
      salonId: salonId ?? this.salonId,
      bookingWebsiteSlug: bookingWebsiteUrl ?? this.bookingWebsiteSlug,
      businessName: businessName ?? this.businessName,
      businessDescription: businessDescription ?? this.businessDescription,
      businessAddress: businessAddress ?? this.businessAddress,
      contactPhone: contactPhone ?? this.contactPhone,
      contactEmail: contactEmail ?? this.contactEmail,
      facebookLink: facebookLink ?? this.facebookLink,
      instagramLink: instagramLink ?? this.instagramLink,
      tiktokLink: tiktokLink ?? this.tiktokLink,
      logoUrl: logoUrl ?? this.logoUrl,
      primaryColor: primaryColor ?? this.primaryColor,
      websitePhotos: websitePhotos ?? this.websitePhotos,
      businessHours: businessHours ?? this.businessHours,
      cancellationPolicy: cancellationPolicy ?? this.cancellationPolicy,
      bookingAcceptance: bookingAcceptance ?? this.bookingAcceptance,
      onlineBookingEnabled: onlineBookingEnabled ?? this.onlineBookingEnabled,
      availableServiceIds: availableServiceIds ?? this.availableServiceIds,
      bookingPassword: bookingPassword ?? this.bookingPassword,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Create from JSON
  factory SalonWebPreferences.fromJson(Map<String, dynamic> json) {
    // Normalize booking_website_url: server may return full URL or just the slug.
    String rawBooking = '';
    try {
      rawBooking = (json['booking_website_url'] ?? '').toString();
    } catch (e) {
      rawBooking = '';
    }

    String bookingSlug = '';
    if (rawBooking.isNotEmpty) {
      // If it's a full URL, try to extract the last path segment as the slug
      if (rawBooking.startsWith('http')) {
        try {
          final uri = Uri.parse(rawBooking);
          if (uri.pathSegments.isNotEmpty) {
            bookingSlug = uri.pathSegments.last;
          } else {
            bookingSlug = rawBooking;
          }
        } catch (e) {
          // Fallback: remove known prefix
          bookingSlug = rawBooking.replaceFirst('https://animalia-programari.ro/book/', '');
        }
      } else {
        bookingSlug = rawBooking;
      }
    }

    return SalonWebPreferences(
      id: json['id'],
      salonId: json['salon_id'] ?? '',
      bookingWebsiteSlug: bookingSlug,
      businessName: json['business_name'] ?? '',
      businessDescription: json['business_description'] ?? '',
      businessAddress: json['business_address'] ?? '',
      contactPhone: json['contact_phone'] ?? '',
      contactEmail: json['contact_email'] ?? '',
      facebookLink: json['facebook_link'] ?? '',
      instagramLink: json['instagram_link'] ?? '',
      tiktokLink: json['tiktok_link'] ?? '',
      logoUrl: json['logo_url'] ?? '',
      primaryColor: json['primary_color'] ?? '#6366F1',
      websitePhotos: _parsePhotos(json['website_photos']),
      businessHours: _parseBusinessHours(json['business_hours']),
      cancellationPolicy: _parseCancellationPolicy(json['cancellation_policy']),
      bookingAcceptance: _parseBookingAcceptance(json['booking_acceptance']),
      onlineBookingEnabled: json['online_booking_enabled'] ?? false,
      availableServiceIds: _parseServiceIds(json['available_service_ids']),
      bookingPassword: json['booking_password'] ?? '',
      isActive: json['is_active'] ?? true,
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'salon_id': salonId,
      // store only the slug (not the full URL) to avoid broken links being persisted
      'booking_website_url': bookingWebsiteSlug,
      'business_name': businessName,
      'business_description': businessDescription,
      'business_address': businessAddress,
      'contact_phone': contactPhone,
      'contact_email': contactEmail,
      'facebook_link': facebookLink,
      'instagram_link': instagramLink,
      'tiktok_link': tiktokLink,
      'logo_url': logoUrl,
      'primary_color': primaryColor,
      'website_photos': jsonEncode(websitePhotos),
      'business_hours': jsonEncode(businessHours),
      'cancellation_policy': cancellationPolicy.name.toUpperCase(),
      'booking_acceptance': bookingAcceptance.name.toUpperCase(),
      'online_booking_enabled': onlineBookingEnabled,
      'available_service_ids': jsonEncode(availableServiceIds),
      'booking_password': bookingPassword,
      'is_active': isActive,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  // Helper methods for parsing JSON fields
  static List<String> _parsePhotos(dynamic photosJson) {
    if (photosJson == null || photosJson.isEmpty) return [];
    try {
      if (photosJson is String) {
        final List<dynamic> photosList = jsonDecode(photosJson);
        return photosList.cast<String>();
      } else if (photosJson is List) {
        return photosJson.cast<String>();
      }
    } catch (e) {
      // Handle parsing error
    }
    return [];
  }

  static Map<String, Map<String, dynamic>> _parseBusinessHours(dynamic hoursJson) {
    if (hoursJson == null || hoursJson.isEmpty) return {};
    try {
      if (hoursJson is String) {
        final Map<String, dynamic> hoursMap = jsonDecode(hoursJson);
        return hoursMap.map((key, value) => MapEntry(key, Map<String, dynamic>.from(value as Map)));
      } else if (hoursJson is Map) {
        // Recursively convert nested maps to ensure proper type casting
        final result = <String, Map<String, dynamic>>{};
        hoursJson.forEach((key, value) {
          if (value is Map) {
            result[key.toString()] = Map<String, dynamic>.from(value);
          } else {
            result[key.toString()] = {};
          }
        });
        return result;
      }
    } catch (e) {
      // Handle parsing error
    }
    return {};
  }

  static CancellationPolicy _parseCancellationPolicy(dynamic policy) {
    if (policy == null) return CancellationPolicy.HOURS_24;
    final policyStr = policy.toString().toLowerCase();
    switch (policyStr) {
      case 'hours_24':
        return CancellationPolicy.HOURS_24;
      case 'hours_48':
        return CancellationPolicy.HOURS_48;
      case 'hours_72':
        return CancellationPolicy.HOURS_72;
      case 'no_changes':
        return CancellationPolicy.NO_CHANGES;
      default:
        return CancellationPolicy.HOURS_24;
    }
  }

  static BookingAcceptance _parseBookingAcceptance(dynamic acceptance) {
    if (acceptance == null) return BookingAcceptance.automatic;
    final acceptanceStr = acceptance.toString().toLowerCase();
    switch (acceptanceStr) {
      case 'automatic':
        return BookingAcceptance.automatic;
      case 'manual':
        return BookingAcceptance.manual;
      default:
        return BookingAcceptance.automatic;
    }
  }

  static List<String> _parseServiceIds(dynamic serviceIdsJson) {
    if (serviceIdsJson == null || serviceIdsJson.isEmpty) return [];
    try {
      if (serviceIdsJson is String) {
        final List<dynamic> idsList = jsonDecode(serviceIdsJson);
        return idsList.cast<String>();
      } else if (serviceIdsJson is List) {
        return serviceIdsJson.cast<String>();
      }
    } catch (e) {
      // Handle parsing error
    }
    return [];
  }

  @override
  String toString() {
    return 'SalonWebPreferences(id: $id, salonId: $salonId, businessName: $businessName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SalonWebPreferences && other.id == id && other.salonId == salonId;
  }

  @override
  int get hashCode => id.hashCode ^ salonId.hashCode;
}

/// Cancellation policy options
enum CancellationPolicy {
  HOURS_24,
  HOURS_48,
  HOURS_72,
  NO_CHANGES,
}

/// Booking acceptance options
enum BookingAcceptance {
  automatic,
  manual,
}
