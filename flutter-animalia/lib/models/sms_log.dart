import 'package:flutter/material.dart';

/// SMS log entry representing a sent SMS message
class SmsLog {
  final String id;
  final String salonId;
  final String? appointmentId;
  final String? clientId;
  final String phoneNumber;
  final String messageContent;
  final SmsMessageType messageType;
  final SmsDirection direction;
  final String? clientName;
  final String? petName;
  final DateTime? appointmentDate;
  final String? appointmentTime;
  final SmsStatus status;
  final DateTime sentAt;
  final DateTime createdAt;

  const SmsLog({
    required this.id,
    required this.salonId,
    this.appointmentId,
    this.clientId,
    required this.phoneNumber,
    required this.messageContent,
    required this.messageType,
    required this.direction,
    this.clientName,
    this.petName,
    this.appointmentDate,
    this.appointmentTime,
    required this.status,
    required this.sentAt,
    required this.createdAt,
  });

  /// Create SmsLog from JSON
  factory SmsLog.fromJson(Map<String, dynamic> json) {
    return SmsLog(
      id: json['id'] as String? ?? '',
      salonId: json['salonId'] as String? ?? '',
      appointmentId: json['appointmentId'] as String?,
      clientId: json['clientId'] as String?,
      phoneNumber: json['phoneNumber'] as String? ?? '',
      messageContent: json['messageContent'] as String? ?? '',
      messageType: SmsMessageType.fromString(json['messageType'] as String? ?? 'CUSTOM'),
      direction: SmsDirection.fromString(json['direction'] as String? ?? 'OUTGOING'),
      clientName: json['clientName'] as String?,
      petName: json['petName'] as String?,
      appointmentDate: json['appointmentDate'] != null
        ? DateTime.tryParse(json['appointmentDate'] as String)
        : null,
      appointmentTime: json['appointmentTime'] as String?,
      status: SmsStatus.fromString(json['status'] as String? ?? 'SENT'),
      sentAt: DateTime.tryParse(json['sentAt'] as String? ?? '') ?? DateTime.now(),
      createdAt: DateTime.tryParse(json['createdAt'] as String? ?? '') ?? DateTime.now(),
    );
  }

  /// Convert SmsLog to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'salonId': salonId,
      'appointmentId': appointmentId,
      'clientId': clientId,
      'phoneNumber': phoneNumber,
      'messageContent': messageContent,
      'messageType': messageType.value,
      'direction': direction.value,
      'clientName': clientName,
      'petName': petName,
      'appointmentDate': appointmentDate?.toIso8601String(),
      'appointmentTime': appointmentTime,
      'status': status.value,
      'sentAt': sentAt.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
    };
  }

  /// Get display name for client and pet
  String get displayName {
    if (clientName != null && petName != null) {
      return '$clientName - $petName';
    } else if (clientName != null) {
      return clientName!;
    } else if (petName != null) {
      return petName!;
    } else {
      return 'Client necunoscut';
    }
  }

  /// Get formatted phone number for display
  String get formattedPhoneNumber {
    if (phoneNumber.startsWith('+40')) {
      return phoneNumber.replaceFirst('+40', '0');
    }
    return phoneNumber;
  }

  /// Check if this is a WhatsApp message
  bool get isWhatsApp {
    return messageContent.startsWith('[WhatsApp]');
  }

  /// Get message content without the [WhatsApp] prefix
  String get displayMessageContent {
    if (isWhatsApp) {
      return messageContent.replaceFirst('[WhatsApp] ', '');
    }
    return messageContent;
  }

  /// Get color for message type
  Color getTypeColor() {
    return messageType.color;
  }

  /// Get icon for message type
  IconData getTypeIcon() {
    return messageType.icon;
  }

  /// Calculate SMS units based on message length
  /// Simple calculation: (message length - 1) / 160 + 1, minimum 1 for non-empty messages
  int get smsUnits {
    if (messageContent.isEmpty) return 0;
    return ((messageContent.length - 1) / 160).floor() + 1;
  }

  /// Get SMS unit display text
  String get smsUnitsDisplay {
    final units = smsUnits;
    if (units == 1) {
      return '1 SMS';
    } else {
      return '$units SMS-uri';
    }
  }

  /// Get character count display with limit information
  String get characterCountDisplay {
    final length = messageContent.length;
    if (length <= 160) {
      return '$length/160 caractere';
    } else {
      return '$length caractere (${smsUnits} părți)';
    }
  }
}

/// SMS message types
enum SmsMessageType {
  appointmentConfirmation('APPOINTMENT_CONFIRMATION', 'Confirmare', Colors.green, Icons.check_circle),
  appointmentCancellation('APPOINTMENT_CANCELLATION', 'Anulare', Colors.red, Icons.cancel),
  appointmentReschedule('APPOINTMENT_RESCHEDULE', 'Reprogramare', Colors.orange, Icons.schedule),
  reminder('REMINDER', 'Reminder', Colors.blue, Icons.notifications),
  followUp('FOLLOW_UP', 'Follow-up', Colors.purple, Icons.feedback),
  completion('APPOINTMENT_COMPLETION', 'Finalizare', Colors.teal, Icons.done_all),
  custom('CUSTOM', 'Personalizat', Colors.grey, Icons.message),
  reply('REPLY', 'Raspuns', Colors.amberAccent, Icons.reply);

  const SmsMessageType(this.value, this.displayName, this.color, this.icon);

  final String value;
  final String displayName;
  final Color color;
  final IconData icon;

  static SmsMessageType fromString(String value) {
    return SmsMessageType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => SmsMessageType.custom,
    );
  }
}

/// SMS directions
enum SmsDirection {
  incoming('INCOMING', 'Primire', Colors.blue, Icons.inbox),
  outgoing('OUTGOING', 'Trimitere', Colors.green, Icons.outbox);

  const SmsDirection(this.value, this.displayName, this.color, this.icon);

  final String value;
  final String displayName;
  final Color color;
  final IconData icon;

  static SmsDirection fromString(String value) {
    return SmsDirection.values.firstWhere(
      (direction) => direction.value == value,
      orElse: () => SmsDirection.outgoing,
    );
  }
}

/// SMS status
enum SmsStatus {
  sent('SENT', 'Trimis', Colors.green, Icons.check),
  failed('FAILED', 'Eșuat', Colors.red, Icons.error),
  pending('PENDING', 'În așteptare', Colors.orange, Icons.hourglass_empty);

  const SmsStatus(this.value, this.displayName, this.color, this.icon);

  final String value;
  final String displayName;
  final Color color;
  final IconData icon;

  static SmsStatus fromString(String value) {
    return SmsStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => SmsStatus.sent,
    );
  }
}

/// Paginated response for SMS logs
class SmsLogPage {
  final List<SmsLog> content;
  final int totalElements;
  final int totalPages;
  final int number;
  final int size;
  final bool first;
  final bool last;

  const SmsLogPage({
    required this.content,
    required this.totalElements,
    required this.totalPages,
    required this.number,
    required this.size,
    required this.first,
    required this.last,
  });

  factory SmsLogPage.fromJson(Map<String, dynamic> json) {
    return SmsLogPage(
      content: (json['content'] as List? ?? [])
          .map((item) => SmsLog.fromJson(item as Map<String, dynamic>))
          .toList(),
      totalElements: json['totalElements'] as int? ?? 0,
      totalPages: json['totalPages'] as int? ?? 0,
      number: json['number'] as int? ?? 0,
      size: json['size'] as int? ?? 10,
      first: json['first'] as bool? ?? true,
      last: json['last'] as bool? ?? true,
    );
  }
}

/// SMS statistics
class SmsStatistics {
  final int totalSms;
  final int sentSms;
  final int failedSms;
  final int pendingSms;
  final int confirmationSms;
  final int reminderSms;
  final int followUpSms;
  final int completionSms;

  const SmsStatistics({
    required this.totalSms,
    required this.sentSms,
    required this.failedSms,
    required this.pendingSms,
    required this.confirmationSms,
    required this.reminderSms,
    required this.followUpSms,
    required this.completionSms,
  });

  factory SmsStatistics.fromJson(Map<String, dynamic> json) {
    return SmsStatistics(
      totalSms: json['totalSms'] as int,
      sentSms: json['sentSms'] as int,
      failedSms: json['failedSms'] as int,
      pendingSms: json['pendingSms'] as int,
      confirmationSms: json['confirmationSms'] as int,
      reminderSms: json['reminderSms'] as int,
      followUpSms: json['followUpSms'] as int,
      completionSms: json['completionSms'] as int,
    );
  }
}
