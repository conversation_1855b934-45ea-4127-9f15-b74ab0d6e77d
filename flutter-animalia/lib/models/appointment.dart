
import 'package:flutter/material.dart';

class Appointment {
  final String id;
  final String clientId;
  final String clientName;
  final String clientPhone;
  final String clientAddress; // Client address
  final String petId; // Reference to the pet
  final String petName; // For convenience
  final String petSpecies; // For convenience
  final String service; // Primary service (for backward compatibility)
  final List<String> services; // Multiple services
  final Map<String, Map<String, dynamic>>? customServices; // Custom service modifications
  final DateTime startTime;
  final DateTime endTime;
  final String status; // 'confirmed', 'canceled', 'pending', 'completed', 'in-progress', 'delayed'
  final bool isPaid;
  final String notes;
  final String assignedGroomer; // Groomer assignment (name)
  final String? groomerId; // Groomer ID for lookup
  final double totalPrice; // Total price for all services
  final int totalDuration; // Total duration in minutes
  final String repetitionFrequency; // 'none', 'daily', 'weekly', 'biweekly', 'monthly'
  final DateTime? completedAt; // When the appointment was actually completed
  final int? actualDuration; // Actual duration if different from planned
  final List<String> photos; // Photo URLs associated with this appointment

  // Subscription-related fields
  final String? subscriptionId; // Reference to parent subscription
  final int? sequenceNumber; // Position in recurring series (1, 2, 3, etc.)
  final bool isRecurring; // Flag to indicate if this is part of a recurring series

  Appointment({
    required this.id,
    required this.clientId,
    required this.clientName,
    required this.clientPhone,
    this.clientAddress = '',
    required this.petId,
    required this.petName,
    required this.petSpecies,
    required this.service,
    this.services = const [],
    this.customServices,
    required this.startTime,
    required this.endTime,
    required this.status,
    required this.isPaid,
    this.notes = '',
    this.assignedGroomer = '',
    this.groomerId,
    this.totalPrice = 0.0,
    this.totalDuration = 60,
    this.repetitionFrequency = 'none',
    this.completedAt,
    this.actualDuration,
    this.subscriptionId,
    this.sequenceNumber,
    this.isRecurring = false,
    this.photos = const [],
  });

  /// Calculate duration in minutes between start and end time
  int get durationInMinutes {
    return endTime.difference(startTime).inMinutes;
  }

  /// Check if appointment is overdue (past end time and not completed)
  bool get isOverdue {
    final now = DateTime.now();
    return now.isAfter(endTime) && !isCompleted;
  }

  /// Check if appointment is currently in progress
  bool get isCurrent {
    final now = DateTime.now();
    return now.isAfter(startTime) && now.isBefore(endTime) && !isCompleted;
  }

  /// Check if appointment is completed
  bool get isCompleted {
    return status.toLowerCase() == 'completed' ||
           status.toLowerCase() == 'finalizat' ||
           status.toLowerCase() == 'finalized';
  }

  /// Check if appointment is a placeholder (proto-appointment)
  bool get isPlaceholder {
    return status.toUpperCase() == 'PLACEHOLDER';
  }

  /// Check if appointment is requested (awaiting approval)
  bool get isRequested {
    return status.toUpperCase() == 'REQUESTED';
  }

  /// Get time remaining for placeholder appointments (in seconds)
  /// Returns null if not a placeholder or already expired
  int? get placeholderTimeRemaining {
    if (!isPlaceholder) return null;

    // Placeholder appointments expire 5 minutes after creation (startTime)
    final expirationTime = startTime.add(const Duration(minutes: 5));
    final now = DateTime.now();

    if (now.isAfter(expirationTime)) return null;

    return expirationTime.difference(now).inSeconds;
  }

  /// Check if placeholder is about to expire (less than 2 minutes remaining)
  bool get isPlaceholderUrgent {
    final remaining = placeholderTimeRemaining;
    if (remaining == null) return false;
    return remaining < 120; // Less than 2 minutes
  }

  /// Get Romanian translation for appointment status
  String getStatusTranslationKey() {
    switch (status.toUpperCase()) {
      case 'SCHEDULED':
      case 'PROGRAMAT':
        return 'new_appointment.status_scheduled';
      case 'CONFIRMED':
      case 'CONFIRMAT':
        return 'new_appointment.status_confirmed';
      case 'IN_PROGRESS':
      case 'IN-PROGRESS':
      case 'IN PROGRESS':
      case 'ÎN DESFĂȘURARE':
        return 'new_appointment.status_in_progress';
      case 'COMPLETED':
      case 'FINALIZAT':
        return 'new_appointment.status_completed';
      case 'CANCELLED':
      case 'CANCELED':
      case 'ANULAT':
        return 'new_appointment.status_cancelled';
      case 'NO_SHOW':
      case 'NO-SHOW':
      case 'NU S-A PREZENTAT':
        return 'new_appointment.status_no_show';
      case 'REQUESTED':
      case 'CERUT':
        return 'new_appointment.status_requested';
      case 'PLACEHOLDER':
      case 'REZERVARE TEMPORARĂ':
        return 'new_appointment.status_placeholder';
      case 'RESCHEDULED':
      case 'REPROGRAMAT':
        return 'new_appointment.status_rescheduled';
      default:
        return 'new_appointment.status_scheduled';
    }
  }

  /// Check if appointment was completed late (after scheduled end time)
  bool get isDelayed {
    return isCompleted && completedAt != null && completedAt!.isAfter(endTime);
  }

  /// Get the actual duration if appointment is completed, otherwise planned duration
  int get effectiveDuration {
    if (isCompleted && completedAt != null) {
      return completedAt!.difference(startTime).inMinutes;
    }
    return totalDuration;
  }

  /// Get the time difference in minutes between completion and scheduled end time
  /// Positive = completed late, Negative = completed early, 0 = on time
  int get completionTimeDifferenceMinutes {
    if (!isCompleted || completedAt == null) return 0;
    return completedAt!.difference(endTime).inMinutes;
  }

  /// Get a human-readable description of the completion time difference
  String get completionTimeDifferenceText {
    if (!isCompleted || completedAt == null) return '';

    final diffMinutes = completionTimeDifferenceMinutes;
    if (diffMinutes == 0) return 'La timp';

    final absMinutes = diffMinutes.abs();
    final hours = absMinutes ~/ 60;
    final minutes = absMinutes % 60;

    String timeText = '';
    if (hours > 0) {
      timeText += '${hours}h';
      if (minutes > 0) timeText += ' ${minutes}m';
    } else {
      timeText = '${minutes}m';
    }

    return diffMinutes > 0 ? '+$timeText întârziere' : '-$timeText mai devreme';
  }

  /// Get a compact version of completion time difference for inline display
  String get completionTimeDifferenceCompact {
    if (!isCompleted || completedAt == null) return '';

    final diffMinutes = completionTimeDifferenceMinutes;
    if (diffMinutes == 0) return '';

    final absMinutes = diffMinutes.abs();
    final hours = absMinutes ~/ 60;
    final minutes = absMinutes % 60;

    String timeText = '';
    if (hours > 0) {
      timeText += '${hours}h';
      if (minutes > 0) timeText += '${minutes}m';
    } else {
      timeText = '${minutes}m';
    }

    return diffMinutes > 0 ? '+$timeText' : '-$timeText';
  }

  /// Check if the completion time difference is significant enough to show a shadow
  bool get shouldShowCompletionShadow {
    if (!isCompleted || completedAt == null) return false;
    return completionTimeDifferenceMinutes.abs() >= 15; // Show shadow for 15+ minute difference
  }

  /// Get the overlay color based on completion time difference
  Color get completionOverlayColor {
    if (!shouldShowCompletionShadow) return Colors.transparent;

    final diffMinutes = completionTimeDifferenceMinutes;
    if (diffMinutes > 0) {
      // Late completion - red overlay
      return Colors.red.withOpacity(0.15);
    } else {
      // Early completion - blue overlay
      return Colors.blue.withOpacity(0.15);
    }
  }

  /// Get the text color for completion time difference in inline display
  Color get completionTimeDifferenceColor {
    if (!isCompleted || completedAt == null) return Colors.transparent;

    final diffMinutes = completionTimeDifferenceMinutes;
    if (diffMinutes == 0) {
      return Colors.green.shade600; // On time - green
    } else if (diffMinutes > 0) {
      return Colors.red.shade700; // Late - red
    } else {
      return Colors.blue.shade700; // Early - blue
    }
  }

  // Format time as a string (e.g., "09:00 - 10:30")
  String get timeRange {
    final startHour = startTime.hour.toString().padLeft(2, '0');
    final startMinute = startTime.minute.toString().padLeft(2, '0');
    final endHour = endTime.hour.toString().padLeft(2, '0');
    final endMinute = endTime.minute.toString().padLeft(2, '0');
    return '$startHour:$startMinute - $endHour:$endMinute';
  }

  /// Get formatted display string for appointment
  String get displayInfo {
    final client = clientName.isNotEmpty ? clientName : 'Client necunoscut';
    final pet = petName.isNotEmpty ? petName : 'Animal necunoscut';
    final serviceInfo = service.isNotEmpty ? service : 'Serviciu general';
    return '$client - $pet | $serviceInfo';
  }

  /// Get compact display string for appointment
  String get compactDisplayInfo {
    final client = clientName.isNotEmpty ? clientName : 'Client';
    final pet = petName.isNotEmpty ? petName : 'Pet';
    return '$client - $pet';
  }

  /// Get service display string
  String get serviceDisplayInfo {
    return service.isNotEmpty ? service : 'Serviciu general';
  }

  /// Check if appointment is part of a subscription
  bool get hasSubscription {
    return subscriptionId != null && subscriptionId!.isNotEmpty;
  }

  /// Get subscription display info
  String get subscriptionDisplayInfo {
    if (!hasSubscription) return '';
    if (sequenceNumber != null) {
      return 'Abonament #$sequenceNumber';
    }
    return 'Abonament';
  }

  // Helper method to safely parse DateTime
  static DateTime? _parseDateTime(String? dateTimeStr) {
    if (dateTimeStr == null || dateTimeStr.isEmpty) return null;
    try {
      return DateTime.parse(dateTimeStr);
    } catch (e) {
      return null;
    }
  }

  // Helper method to parse custom services
  static Map<String, Map<String, dynamic>>? _parseCustomServices(dynamic customServicesData) {
    if (customServicesData == null) return null;

    try {
      if (customServicesData is Map<String, dynamic>) {
        return customServicesData.map((key, value) {
          if (value is Map<String, dynamic>) {
            return MapEntry(key, value);
          }
          return MapEntry(key, <String, dynamic>{});
        });
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Convert from JSON
  factory Appointment.fromJson(Map<String, dynamic> json) {
    try {
      // Handle both old flat structure and new nested backend structure
      // Note: ApiService._handleResponse passes only the 'data' part of the response to this method
      final bool isNestedStructure = json.containsKey('client') && json.containsKey('pet') && json.containsKey('staff');

      if (isNestedStructure) {
        // New backend structure with nested objects (from 'data' part of response)
        final client = json['client'] as Map<String, dynamic>? ?? {};
        final pet = json['pet'] as Map<String, dynamic>? ?? {};
        final staff = json['staff'] as Map<String, dynamic>? ?? {};
        final servicesArray = json['services'] as List<dynamic>? ?? [];

      // Parse date and time fields
      final appointmentDate = json['appointmentDate'] as String?;
      final startTimeStr = json['startTime'] as String?;
      final endTimeStr = json['endTime'] as String?;

      DateTime startTime;
      DateTime endTime;

      if (appointmentDate != null && startTimeStr != null && endTimeStr != null) {
        try {
          // Combine date and time: "2025-06-04" + "09:15:00"
          startTime = DateTime.parse('${appointmentDate}T$startTimeStr');
          endTime = DateTime.parse('${appointmentDate}T$endTimeStr');
        } catch (e) {
          // Fallback to current time if parsing fails
          startTime = DateTime.now();
          endTime = DateTime.now().add(const Duration(hours: 1));
        }
      } else {
        // Fallback to current time if parsing fails
        startTime = DateTime.now();
        endTime = DateTime.now().add(const Duration(hours: 1));
      }

      // Extract service names from services array
      final serviceNames = servicesArray
          .map((service) => service is Map<String, dynamic> ? service['name'] as String? ?? '' : service.toString())
          .where((name) => name.isNotEmpty)
          .toList();

      // Get first service name or default
      final primaryService = serviceNames.isNotEmpty ? serviceNames.first : 'Serviciu general';

      return Appointment(
        id: json['id'] as String? ?? '',
        clientId: client['id'] as String? ?? '',
        clientName: client['name'] as String? ?? '',
        clientPhone: client['phone'] as String? ?? '',
        clientAddress: client['address'] as String? ?? '',
        petId: pet['id'] as String? ?? '',
        petName: pet['name'] as String? ?? '',
        petSpecies: pet['breed'] as String? ?? '', // Backend uses 'breed' field
        service: primaryService,
        services: serviceNames,
        customServices: _parseCustomServices(json['customServices']),
        startTime: startTime,
        endTime: endTime,
        status: json['status'] as String? ?? 'SCHEDULED',
        isPaid: false, // Backend doesn't provide isPaid field yet, default to false
        notes: json['notes'] as String? ?? '',
        assignedGroomer: staff['name'] as String? ?? '',
        groomerId: staff['id'] as String?,
        totalPrice: (json['totalPrice'] as num?)?.toDouble() ?? 0.0,
        totalDuration: json['totalDuration'] as int? ?? 60,
        repetitionFrequency: json['repetitionFrequency'] as String? ?? 'none',
        completedAt: _parseDateTime(json['completedAt'] as String?),
        actualDuration: json['actualDuration'] as int?,
        subscriptionId: json['subscriptionId'] as String?,
        sequenceNumber: json['sequenceNumber'] as int?,
        isRecurring: json['isRecurring'] as bool? ?? false,
        photos: json['photos'] != null ? List<String>.from(json['photos']) : [],
      );
    } else {
      // Old flat structure (backward compatibility)
      return Appointment(
        id: json['id'] as String? ?? '',
        clientId: json['clientId'] as String? ?? '',
        clientName: json['clientName'] as String? ?? '',
        clientPhone: json['clientPhone'] as String? ?? '',
        clientAddress: json['clientAddress'] as String? ?? '',
        petId: json['petId'] as String? ?? '',
        petName: json['petName'] as String? ?? '',
        petSpecies: json['petSpecies'] as String? ?? '',
        service: json['service'] as String? ?? '',
        services: json['services'] != null
            ? List<String>.from(json['services'])
            : [json['service'] as String? ?? ''],
        customServices: _parseCustomServices(json['customServices']),
        startTime: _parseDateTime(json['startTime'] as String?) ?? DateTime.now(),
        endTime: _parseDateTime(json['endTime'] as String?) ?? _parseDateTime(json['startTime'] as String?)?.add(const Duration(hours: 1)) ?? DateTime.now().add(const Duration(hours: 1)),
        status: json['status'] as String? ?? 'SCHEDULED',
        isPaid: json['isPaid'] as bool? ?? false,
        notes: json['notes'] as String? ?? '',
        assignedGroomer: json['assignedGroomer'] as String? ?? json['assignedCoworker'] as String? ?? '',
        groomerId: json['groomerId'] as String? ?? json['assignedGroomerId'] as String?,
        totalPrice: (json['totalPrice'] as num?)?.toDouble() ?? 0.0,
        totalDuration: json['totalDuration'] as int? ?? 60,
        repetitionFrequency: json['repetitionFrequency'] as String? ?? 'none',
        completedAt: _parseDateTime(json['completedAt'] as String?),
        actualDuration: json['actualDuration'] as int?,
        subscriptionId: json['subscriptionId'] as String?,
        sequenceNumber: json['sequenceNumber'] as int?,
        isRecurring: json['isRecurring'] as bool? ?? false,
        photos: json['photos'] != null ? List<String>.from(json['photos']) : [],
      );
    }
    } catch (e) {
      rethrow;
    }
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'clientId': clientId,
      'clientName': clientName,
      'clientPhone': clientPhone,
      'clientAddress': clientAddress,
      'petId': petId,
      'petName': petName,
      'petSpecies': petSpecies,
      'service': service,
      'services': services,
      'customServices': customServices,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime.toIso8601String(),
      'status': status,
      'isPaid': isPaid,
      'notes': notes,
      'assignedGroomer': assignedGroomer,
      'groomerId': groomerId,
      'assignedCoworker': assignedGroomer, // For backward compatibility
      'totalPrice': totalPrice,
      'totalDuration': totalDuration,
      'repetitionFrequency': repetitionFrequency,
      'completedAt': completedAt?.toIso8601String(),
      'actualDuration': actualDuration,
      'subscriptionId': subscriptionId,
      'sequenceNumber': sequenceNumber,
      'isRecurring': isRecurring,
      'photos': photos,
    };
  }

  // Copy with method for immutable updates
  Appointment copyWith({
    String? id,
    String? clientId,
    String? clientName,
    String? clientPhone,
    String? clientAddress,
    String? petId,
    String? petName,
    String? petSpecies,
    String? service,
    List<String>? services,
    Map<String, Map<String, dynamic>>? customServices,
    DateTime? startTime,
    DateTime? endTime,
    String? status,
    bool? isPaid,
    String? notes,
    String? assignedGroomer,
    String? groomerId,
    double? totalPrice,
    int? totalDuration,
    String? repetitionFrequency,
    DateTime? completedAt,
    int? actualDuration,
    String? subscriptionId,
    int? sequenceNumber,
    bool? isRecurring,
    List<String>? photos,
  }) {
    return Appointment(
      id: id ?? this.id,
      clientId: clientId ?? this.clientId,
      clientName: clientName ?? this.clientName,
      clientPhone: clientPhone ?? this.clientPhone,
      clientAddress: clientAddress ?? this.clientAddress,
      petId: petId ?? this.petId,
      petName: petName ?? this.petName,
      petSpecies: petSpecies ?? this.petSpecies,
      service: service ?? this.service,
      services: services ?? this.services,
      customServices: customServices ?? this.customServices,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      status: status ?? this.status,
      isPaid: isPaid ?? this.isPaid,
      notes: notes ?? this.notes,
      assignedGroomer: assignedGroomer ?? this.assignedGroomer,
      groomerId: groomerId ?? this.groomerId,
      totalPrice: totalPrice ?? this.totalPrice,
      totalDuration: totalDuration ?? this.totalDuration,
      repetitionFrequency: repetitionFrequency ?? this.repetitionFrequency,
      completedAt: completedAt ?? this.completedAt,
      actualDuration: actualDuration ?? this.actualDuration,
      subscriptionId: subscriptionId ?? this.subscriptionId,
      sequenceNumber: sequenceNumber ?? this.sequenceNumber,
      isRecurring: isRecurring ?? this.isRecurring,
      photos: photos ?? this.photos,
    );
  }
}
