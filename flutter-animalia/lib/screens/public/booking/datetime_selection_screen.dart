import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';

import '../../../models/online_booking.dart';
import '../../../models/salon_web_preferences.dart';
import '../../../services/online_booking_service.dart';
import '../../../utils/debug_logger.dart';

/// Screen for selecting date and time for online booking
class DateTimeSelectionScreen extends StatefulWidget {
  final BookingFlowState bookingState;
  final SalonWebPreferences salonPreferences;

  const DateTimeSelectionScreen({
    super.key,
    required this.bookingState,
    required this.salonPreferences,
  });

  @override
  State<DateTimeSelectionScreen> createState() => _DateTimeSelectionScreenState();
}

class _DateTimeSelectionScreenState extends State<DateTimeSelectionScreen> {
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDate;
  TimeSlot? _selectedTimeSlot;
  List<TimeSlot> _availableSlots = [];
  bool _isLoadingSlots = false;
  String? _errorMessage;

  // Cache for availability by date
  final Map<String, List<TimeSlot>> _availabilityCache = {};
  final Map<String, bool> _loadingDates = {};
  final Set<String> _daysWithAvailability = {};
  bool _isLoadingCalendar = false;

  @override
  void initState() {
    super.initState();
    // Pre-select today's date
    _selectedDate = DateTime.now();
    _loadAvailableSlots();
    // Pre-load availability for the current month
    _preloadMonthAvailability(_focusedDay);
  }

  Color get _primaryColor => _parseColor(widget.salonPreferences.primaryColor);

  Color _parseColor(String hexColor) {
    try {
      final hex = hexColor.replaceAll('#', '');
      return Color(int.parse('FF$hex', radix: 16));
    } catch (e) {
      return Colors.blue[700]!;
    }
  }

  Future<void> _preloadMonthAvailability(DateTime month) async {
    setState(() {
      _isLoadingCalendar = true;
    });

    // Get first and last day of the month
    final firstDay = DateTime(month.year, month.month, 1);
    final lastDay = DateTime(month.year, month.month + 1, 0);

    // Load availability for each day in the month (in batches to avoid overwhelming the server)
    final today = DateTime.now();
    final startDay = firstDay.isBefore(today) ? today : firstDay;

    for (int i = 0; i <= lastDay.difference(startDay).inDays; i++) {
      final date = startDay.add(Duration(days: i));
      final dateStr = DateFormat('yyyy-MM-dd').format(date);

      // Skip if already loading or loaded
      if (_loadingDates[dateStr] == true || _availabilityCache.containsKey(dateStr)) {
        continue;
      }

      _loadingDates[dateStr] = true;

      // Load in background without blocking UI
      _loadAvailabilitySlotsForDate(date).then((_) {
        _loadingDates[dateStr] = false;
      });
    }

    setState(() {
      _isLoadingCalendar = false;
    });
  }

  Future<void> _loadAvailabilitySlotsForDate(DateTime date) async {
    final dateStr = DateFormat('yyyy-MM-dd').format(date);

    try {
      final response = await OnlineBookingService.getAvailableTimeSlots(
        salonId: widget.bookingState.salonId,
        date: dateStr,
        serviceIds: widget.bookingState.selectedServiceIds,
      );

      if (response.success && response.data != null) {
        if (mounted) {
          setState(() {
            _availabilityCache[dateStr] = response.data!.availableSlots;
            if (response.data!.availableSlots.isNotEmpty) {
              _daysWithAvailability.add(dateStr);
            }
          });
        }
      }
    } catch (e) {
      DebugLogger.logShowcase('❌ Error loading availability for $dateStr: $e');
    }
  }

  Future<void> _loadAvailableSlots() async {
    if (_selectedDate == null) return;

    setState(() {
      _isLoadingSlots = true;
      _errorMessage = null;
      _selectedTimeSlot = null;
    });

    try {
      final dateStr = DateFormat('yyyy-MM-dd').format(_selectedDate!);

      // Check cache first
      if (_availabilityCache.containsKey(dateStr)) {
        setState(() {
          _availableSlots = _availabilityCache[dateStr]!;
          _isLoadingSlots = false;
        });
        return;
      }

      final response = await OnlineBookingService.getAvailableTimeSlots(
        salonId: widget.bookingState.salonId,
        date: dateStr,
        serviceIds: widget.bookingState.selectedServiceIds,
      );

      if (response.success && response.data != null) {
        setState(() {
          _availableSlots = response.data!.availableSlots;
          _availabilityCache[dateStr] = response.data!.availableSlots;
          if (response.data!.availableSlots.isNotEmpty) {
            _daysWithAvailability.add(dateStr);
          }
          _isLoadingSlots = false;
        });
      } else {
        setState(() {
          _errorMessage = response.error ?? 'Nu s-au putut încărca intervalele disponibile';
          _isLoadingSlots = false;
        });
      }
    } catch (e) {
      DebugLogger.logShowcase('❌ Error loading time slots: $e');
      setState(() {
        _errorMessage = 'A apărut o eroare la încărcarea intervalelor';
        _isLoadingSlots = false;
      });
    }
  }

  Future<void> _createProtoAppointment() async {
    if (_selectedDate == null || _selectedTimeSlot == null) return;

    setState(() {
      _isLoadingSlots = true;
      _errorMessage = null;
    });

    try {
      final dateStr = DateFormat('yyyy-MM-dd').format(_selectedDate!);
      final request = CreateProtoAppointmentRequest(
        date: dateStr,
        startTime: _selectedTimeSlot!.startTime,
        endTime: _selectedTimeSlot!.endTime,
        serviceIds: widget.bookingState.selectedServiceIds,
      );

      final response = await OnlineBookingService.createProtoAppointment(
        salonId: widget.bookingState.salonId,
        request: request,
      );

      if (response.success && response.data != null) {
        final updatedState = widget.bookingState.copyWith(
          selectedDate: dateStr,
          selectedStartTime: _selectedTimeSlot!.startTime,
          selectedEndTime: _selectedTimeSlot!.endTime,
          appointmentId: response.data!.appointmentId,
          appointmentExpiresAt: response.data!.expirationTime,
        );

        if (mounted) {
          Navigator.pop(context, updatedState);
        }
      } else {
        setState(() {
          _errorMessage = response.error ?? 'Nu s-a putut rezerva intervalul';
          _isLoadingSlots = false;
        });
      }
    } catch (e) {
      DebugLogger.logShowcase('❌ Error creating proto-appointment: $e');
      setState(() {
        _errorMessage = 'A apărut o eroare la rezervarea intervalului';
        _isLoadingSlots = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Selectează data și ora'),
        backgroundColor: _primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: _primaryColor.withOpacity(0.1),
              border: Border(
                bottom: BorderSide(color: Colors.grey[300]!),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.salonPreferences.businessName,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '${widget.bookingState.selectedServiceIds.length} ${widget.bookingState.selectedServiceIds.length == 1 ? "serviciu selectat" : "servicii selectate"}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[700],
                  ),
                ),
              ],
            ),
          ),

          // Date selector
          _buildDateSelector(),

          // Time slots
          Expanded(
            child: _buildTimeSlotsSection(),
          ),
        ],
      ),
    );
  }

  Widget _buildDateSelector() {
    final today = DateTime.now();
    final firstDay = DateTime(today.year, today.month, today.day);
    final lastDay = DateTime(today.year + 1, today.month, today.day);

    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Selectează data',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          TableCalendar(
            firstDay: firstDay,
            lastDay: lastDay,
            focusedDay: _focusedDay,
            selectedDayPredicate: (day) {
              return _selectedDate != null && isSameDay(_selectedDate, day);
            },
            calendarFormat: CalendarFormat.month,
            startingDayOfWeek: StartingDayOfWeek.monday,
            locale: 'ro_RO',
            headerStyle: HeaderStyle(
              formatButtonVisible: false,
              titleCentered: true,
              titleTextStyle: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _primaryColor,
              ),
              leftChevronIcon: Icon(Icons.chevron_left, color: _primaryColor),
              rightChevronIcon: Icon(Icons.chevron_right, color: _primaryColor),
            ),
            calendarStyle: CalendarStyle(
              todayDecoration: BoxDecoration(
                color: _primaryColor.withOpacity(0.3),
                shape: BoxShape.circle,
              ),
              selectedDecoration: BoxDecoration(
                color: _primaryColor,
                shape: BoxShape.circle,
              ),
              selectedTextStyle: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
              todayTextStyle: TextStyle(
                color: _primaryColor,
                fontWeight: FontWeight.bold,
              ),
              // Grey out days without availability
              disabledTextStyle: TextStyle(
                color: Colors.grey[400],
              ),
              outsideTextStyle: TextStyle(
                color: Colors.grey[300],
              ),
            ),
            enabledDayPredicate: (day) {
              // Disable past days
              if (day.isBefore(DateTime(today.year, today.month, today.day))) {
                return false;
              }

              // If we have availability data, only enable days with slots
              final dateStr = DateFormat('yyyy-MM-dd').format(day);
              if (_availabilityCache.containsKey(dateStr)) {
                return _daysWithAvailability.contains(dateStr);
              }

              // If we don't have data yet, enable the day (it will load when selected)
              return true;
            },
            onDaySelected: (selectedDay, focusedDay) {
              if (!isSameDay(_selectedDate, selectedDay)) {
                setState(() {
                  _selectedDate = selectedDay;
                  _focusedDay = focusedDay;
                });
                _loadAvailableSlots();
              }
            },
            onPageChanged: (focusedDay) {
              setState(() {
                _focusedDay = focusedDay;
              });
              // Preload availability for the new month
              _preloadMonthAvailability(focusedDay);
            },
            calendarBuilders: CalendarBuilders(
              // Add a dot indicator for days with availability
              markerBuilder: (context, day, events) {
                final dateStr = DateFormat('yyyy-MM-dd').format(day);
                if (_daysWithAvailability.contains(dateStr)) {
                  return Positioned(
                    bottom: 4,
                    child: Container(
                      width: 6,
                      height: 6,
                      decoration: BoxDecoration(
                        color: _primaryColor,
                        shape: BoxShape.circle,
                      ),
                    ),
                  );
                }
                return null;
              },
            ),
          ),
          if (_isLoadingCalendar)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(_primaryColor),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Se încarcă disponibilitatea...',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTimeSlotsSection() {
    if (_isLoadingSlots) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
              const SizedBox(height: 16),
              Text(
                _errorMessage!,
                style: TextStyle(fontSize: 16, color: Colors.grey[700]),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: _loadAvailableSlots,
                icon: const Icon(Icons.refresh),
                label: const Text('Încearcă din nou'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_availableSlots.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.event_busy, size: 64, color: Colors.grey[400]),
              const SizedBox(height: 16),
              Text(
                'Nu sunt intervale disponibile',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500, color: Colors.grey[700]),
              ),
              const SizedBox(height: 8),
              Text(
                'Vă rugăm să selectați o altă dată',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      children: [
        Expanded(
          child: GridView.builder(
            padding: const EdgeInsets.all(16),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 2.2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: _availableSlots.length,
            itemBuilder: (context, index) {
              final slot = _availableSlots[index];
              final isSelected = _selectedTimeSlot == slot;
              return _buildTimeSlotCard(slot, isSelected);
            },
          ),
        ),
        if (_selectedTimeSlot != null) _buildContinueButton(),
      ],
    );
  }

  Widget _buildTimeSlotCard(TimeSlot slot, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedTimeSlot = slot;
        });
      },
      child: Container(
        decoration: BoxDecoration(
          color: isSelected ? _primaryColor : Colors.white,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: isSelected ? _primaryColor : Colors.grey[300]!,
            width: 2,
          ),
        ),
        child: Center(
          child: Text(
            slot.startTime,
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.bold,
              color: isSelected ? Colors.white : Colors.black87,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContinueButton() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isLoadingSlots ? null : _createProtoAppointment,
            style: ElevatedButton.styleFrom(
              backgroundColor: _primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 2,
            ),
            child: _isLoadingSlots
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Continuă cu ${_selectedTimeSlot!.startTime}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 8),
                      const Icon(Icons.arrow_forward, size: 20),
                    ],
                  ),
          ),
        ),
      ),
    );
  }
}
