import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../models/online_booking.dart';
import '../../../models/salon_web_preferences.dart';

/// Screen showing booking confirmation after successful finalization
class BookingConfirmationScreen extends StatelessWidget {
  final BookingResponse bookingResponse;
  final SalonWebPreferences salonPreferences;
  final BookingFlowState bookingState;

  const BookingConfirmationScreen({
    super.key,
    required this.bookingResponse,
    required this.salonPreferences,
    required this.bookingState,
  });

  Color get _primaryColor => _parseColor(salonPreferences.primaryColor);

  Color _parseColor(String hexColor) {
    try {
      final hex = hexColor.replaceAll('#', '');
      return Color(int.parse('FF$hex', radix: 16));
    } catch (e) {
      return Colors.blue[700]!;
    }
  }

  String _getStatusMessage() {
    if (bookingResponse.isAutoConfirmed) {
      return 'Rezervarea ta a fost confirmată automat!';
    } else {
      return 'Rezervarea ta așteaptă confirmare!';
    }
  }

  String _getStatusDescription() {
    if (bookingResponse.isAutoConfirmed) {
      return 'Programarea ta a fost confirmată. Vei primi un SMS de confirmare în curând.';
    } else {
      return 'Rezervarea ta a fost trimisă și așteaptă aprobarea salonului. Vei primi un SMS când va fi confirmată.';
    }
  }

  IconData _getStatusIcon() {
    return bookingResponse.isAutoConfirmed 
        ? Icons.check_circle 
        : Icons.schedule;
  }

  Color _getStatusColor() {
    return bookingResponse.isAutoConfirmed 
        ? Colors.green 
        : Colors.orange;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Confirmare rezervare'),
        backgroundColor: _primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        automaticallyImplyLeading: false,
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    const SizedBox(height: 20),
                    
                    // Success icon
                    _buildSuccessIcon(),
                    const SizedBox(height: 24),
                    
                    // Status message
                    _buildStatusMessage(),
                    const SizedBox(height: 32),
                    
                    // Booking details card
                    _buildBookingDetailsCard(),
                    const SizedBox(height: 24),
                    
                    // Important info card
                    _buildImportantInfoCard(),
                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
            
            // Action buttons
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildSuccessIcon() {
    return Container(
      width: 100,
      height: 100,
      decoration: BoxDecoration(
        color: _getStatusColor().withOpacity(0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(
        _getStatusIcon(),
        size: 60,
        color: _getStatusColor(),
      ),
    );
  }

  Widget _buildStatusMessage() {
    return Column(
      children: [
        Text(
          _getStatusMessage(),
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: _getStatusColor(),
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 12),
        Text(
          _getStatusDescription(),
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[700],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildBookingDetailsCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Detalii rezervare',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          
          _buildDetailRow(
            Icons.business,
            'Salon',
            salonPreferences.businessName,
          ),
          const SizedBox(height: 12),
          
          _buildDetailRow(
            Icons.calendar_today,
            'Data',
            _formatDate(bookingState.selectedDate ?? ''),
          ),
          const SizedBox(height: 12),
          
          _buildDetailRow(
            Icons.access_time,
            'Ora',
            bookingState.selectedStartTime ?? '',
          ),
          const SizedBox(height: 12),
          
          _buildDetailRow(
            Icons.confirmation_number,
            'Număr rezervare',
            bookingResponse.appointmentId,
          ),
          const SizedBox(height: 12),
          
          _buildDetailRow(
            Icons.info_outline,
            'Status',
            _getStatusText(),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(IconData icon, String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 20, color: Colors.grey[600]),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildImportantInfoCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(Icons.info, color: Colors.blue[700], size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Informații importante',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[900],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  bookingResponse.isAutoConfirmed
                      ? 'Vei primi un SMS de confirmare în curând. Te rugăm să ajungi cu 5-10 minute înainte de ora programată.'
                      : 'Vei primi un SMS când rezervarea va fi confirmată de salon. Te rugăm să verifici telefonul în următoarele ore.',
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.blue[800],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                Navigator.of(context).popUntil((route) => route.isFirst);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                elevation: 0,
              ),
              child: const Text(
                'Înapoi la pagina principală',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(String dateStr) {
    try {
      final date = DateTime.parse(dateStr);
      return DateFormat('EEEE, d MMMM yyyy', 'ro').format(date);
    } catch (e) {
      return dateStr;
    }
  }

  String _getStatusText() {
    if (bookingResponse.isAutoConfirmed) {
      return 'Confirmată';
    } else {
      return 'În așteptare';
    }
  }
}

