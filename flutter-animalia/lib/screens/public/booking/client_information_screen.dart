import 'package:flutter/material.dart';
import 'dart:async';
import 'package:intl_phone_field/intl_phone_field.dart';

import '../../../models/online_booking.dart';
import '../../../models/salon_web_preferences.dart';
import '../../../services/online_booking_service.dart';
import '../../../services/breed_service.dart';
import '../../../utils/debug_logger.dart';
import 'booking_confirmation_screen.dart';

/// Screen for collecting client and pet information to finalize booking
class ClientInformationScreen extends StatefulWidget {
  final BookingFlowState bookingState;
  final SalonWebPreferences salonPreferences;

  const ClientInformationScreen({
    super.key,
    required this.bookingState,
    required this.salonPreferences,
  });

  @override
  State<ClientInformationScreen> createState() => _ClientInformationScreenState();
}

class _ClientInformationScreenState extends State<ClientInformationScreen> {
  final _formKey = GlobalKey<FormState>();

  // Form controllers
  final _phoneController = TextEditingController();
  final _clientNameController = TextEditingController();
  final _petNameController = TextEditingController();
  final _petBreedController = TextEditingController();
  final _notesController = TextEditingController();
  final _passwordController = TextEditingController();

  // Form values
  String _selectedPetSpecies = 'Dog';
  String _selectedPetSize = 'M';
  String _completePhoneNumber = '';
  final String _initialCountryCode = 'RO';

  bool _isSubmitting = false;
  String? _errorMessage;

  // Breed loading
  bool _isLoadingBreeds = false;
  List<String> _currentBreeds = [];

  // Timer for proto-appointment expiration
  Timer? _expirationTimer;
  Duration? _timeRemaining;

  @override
  void initState() {
    super.initState();
    _startExpirationTimer();
    _loadBreedsForSpecies(_selectedPetSpecies);
  }

  @override
  void dispose() {
    _expirationTimer?.cancel();
    _phoneController.dispose();
    _clientNameController.dispose();
    _petNameController.dispose();
    _petBreedController.dispose();
    _notesController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _startExpirationTimer() {
    if (widget.bookingState.appointmentExpiresAt == null) return;

    _updateTimeRemaining();
    _expirationTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateTimeRemaining();

      if (_timeRemaining != null && _timeRemaining!.inSeconds <= 0) {
        timer.cancel();
        _handleExpiration();
      }
    });
  }

  void _updateTimeRemaining() {
    if (widget.bookingState.appointmentExpiresAt == null) return;

    final now = DateTime.now();
    final expiresAt = widget.bookingState.appointmentExpiresAt!;

    if (now.isBefore(expiresAt)) {
      setState(() {
        _timeRemaining = expiresAt.difference(now);
      });
    } else {
      setState(() {
        _timeRemaining = Duration.zero;
      });
    }
  }

  void _handleExpiration() {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Rezervarea a expirat'),
        content: const Text(
          'Intervalul rezervat a expirat. Vă rugăm să selectați din nou data și ora.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).popUntil((route) => route.isFirst);
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Color get _primaryColor => _parseColor(widget.salonPreferences.primaryColor);

  Color _parseColor(String hexColor) {
    try {
      final hex = hexColor.replaceAll('#', '');
      return Color(int.parse('FF$hex', radix: 16));
    } catch (e) {
      return Colors.blue[700]!;
    }
  }

  Future<void> _loadBreedsForSpecies(String species) async {
    setState(() => _isLoadingBreeds = true);

    try {
      final response = await BreedService.getBreedsBySpecies(species.toLowerCase());

      if (mounted) {
        setState(() {
          _currentBreeds = response.success && response.data != null
              ? response.data!
              : ['Metis', 'Necunoscut'];
          _isLoadingBreeds = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _currentBreeds = ['Metis', 'Necunoscut'];
          _isLoadingBreeds = false;
        });
      }
    }
  }

  Future<void> _submitBooking() async {
    if (!_formKey.currentState!.validate()) return;

    // Check if proto-appointment has expired
    if (widget.bookingState.isProtoAppointmentExpired) {
      _handleExpiration();
      return;
    }

    setState(() {
      _isSubmitting = true;
      _errorMessage = null;
    });

    try {
      final request = FinalizeBookingRequest(
        appointmentId: widget.bookingState.appointmentId!,
        clientPhone: _completePhoneNumber.isNotEmpty
            ? _completePhoneNumber
            : _phoneController.text.trim(),
        petName: _petNameController.text.trim(),
        petSpecies: _selectedPetSpecies,
        petBreed: _petBreedController.text.trim(),
        petSize: _selectedPetSize,
        clientName: _clientNameController.text.trim().isEmpty
            ? null
            : _clientNameController.text.trim(),
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        password: _passwordController.text.trim().isEmpty
            ? null
            : _passwordController.text.trim(),
      );

      final response = await OnlineBookingService.finalizeBooking(
        salonId: widget.bookingState.salonId,
        request: request,
      );

      if (response.success && response.data != null) {
        if (mounted) {
          // Navigate to confirmation screen
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => BookingConfirmationScreen(
                bookingResponse: response.data!,
                salonPreferences: widget.salonPreferences,
                bookingState: widget.bookingState,
              ),
            ),
          );
        }
      } else {
        setState(() {
          _errorMessage = response.error ?? 'Nu s-a putut finaliza rezervarea';
          _isSubmitting = false;
        });
      }
    } catch (e) {
      DebugLogger.logError('❌ Error finalizing booking: $e');
      setState(() {
        _errorMessage = 'A apărut o eroare la finalizarea rezervării';
        _isSubmitting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Informații client'),
        backgroundColor: _primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Expiration timer banner
          if (_timeRemaining != null) _buildExpirationBanner(),

          // Form
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    _buildHeader(),
                    const SizedBox(height: 24),

                    // Error message
                    if (_errorMessage != null) _buildErrorBanner(),

                    // Client information section
                    _buildSectionTitle('Informații client'),
                    const SizedBox(height: 12),
                    _buildPhoneField(),
                    const SizedBox(height: 16),
                    _buildClientNameField(),
                    const SizedBox(height: 24),

                    // Pet information section
                    _buildSectionTitle('Informații animal'),
                    const SizedBox(height: 12),
                    _buildPetNameField(),
                    const SizedBox(height: 16),
                    _buildPetSpeciesField(),
                    const SizedBox(height: 16),
                    _buildPetBreedField(),
                    const SizedBox(height: 16),
                    _buildPetSizeField(),
                    const SizedBox(height: 24),

                    // Notes section
                    _buildSectionTitle('Observații (opțional)'),
                    const SizedBox(height: 12),
                    _buildNotesField(),

                    // Password field (ALWAYS SHOW FOR TESTING)
                    if (widget.salonPreferences.bookingPassword.isNotEmpty) ...[
                      const SizedBox(height: 24),
                      _buildSectionTitle('Parolă rezervare'),
                      const SizedBox(height: 12),
                      _buildPasswordField(),
                    ],

                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
          ),

          // Submit button
          _buildSubmitButton(),
        ],
      ),
    );
  }

  Widget _buildExpirationBanner() {
    final minutes = _timeRemaining!.inMinutes;
    final seconds = _timeRemaining!.inSeconds % 60;
    final isUrgent = _timeRemaining!.inMinutes < 2;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
      decoration: BoxDecoration(
        color: isUrgent ? Colors.red[50] : Colors.orange[50],
        border: Border(
          bottom: BorderSide(
            color: isUrgent ? Colors.red[200]! : Colors.orange[200]!,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.timer_outlined,
            color: isUrgent ? Colors.red[700] : Colors.orange[700],
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            'Timp rămas: ${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: isUrgent ? Colors.red[700] : Colors.orange[700],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.salonPreferences.businessName,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 6),
              Text(
                widget.bookingState.selectedDate ?? '',
                style: TextStyle(fontSize: 14, color: Colors.grey[700]),
              ),
              const SizedBox(width: 16),
              Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 6),
              Text(
                widget.bookingState.selectedStartTime ?? '',
                style: TextStyle(fontSize: 14, color: Colors.grey[700]),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildErrorBanner() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red[200]!),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: Colors.red[700], size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _errorMessage!,
              style: TextStyle(fontSize: 14, color: Colors.red[700]),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: Colors.black87,
      ),
    );
  }

  Widget _buildPhoneField() {
    return IntlPhoneField(
      decoration: InputDecoration(
        labelText: 'Număr de telefon *',
        hintText: '731 234 567',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        filled: true,
        fillColor: Colors.white,
        contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      ),
      initialCountryCode: _initialCountryCode,
      dropdownIconPosition: IconPosition.trailing,
      flagsButtonPadding: const EdgeInsets.symmetric(horizontal: 8.0),
      showDropdownIcon: true,
      disableLengthCheck: false,
      keyboardType: TextInputType.phone,
      enabled: !_isSubmitting,
      onChanged: (phone) {
        setState(() {
          _completePhoneNumber = phone.completeNumber;
        });
      },
      validator: (phone) {
        if (phone == null || phone.completeNumber.isEmpty) {
          return 'Numărul de telefon este obligatoriu';
        }
        return null;
      },
    );
  }

  Widget _buildClientNameField() {
    return TextFormField(
      controller: _clientNameController,
      textCapitalization: TextCapitalization.words,
      decoration: InputDecoration(
        labelText: 'Nume client (opțional)',
        hintText: 'Ion Popescu',
        prefixIcon: const Icon(Icons.person),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        filled: true,
        fillColor: Colors.white,
      ),
    );
  }

  Widget _buildPetNameField() {
    return TextFormField(
      controller: _petNameController,
      textCapitalization: TextCapitalization.words,
      decoration: InputDecoration(
        labelText: 'Nume animal *',
        hintText: 'Rex',
        prefixIcon: const Icon(Icons.pets),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        filled: true,
        fillColor: Colors.white,
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Numele animalului este obligatoriu';
        }
        return null;
      },
    );
  }

  Widget _buildPetSpeciesField() {
    return DropdownButtonFormField<String>(
      value: _selectedPetSpecies,
      decoration: InputDecoration(
        labelText: 'Specie *',
        prefixIcon: const Icon(Icons.category),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        filled: true,
        fillColor: Colors.white,
      ),
      items: const [
        DropdownMenuItem(value: 'Dog', child: Text('Câine')),
        DropdownMenuItem(value: 'Cat', child: Text('Pisică')),
        DropdownMenuItem(value: 'Other', child: Text('Altele')),
      ],
      onChanged: _isSubmitting ? null : (value) {
        if (value != null) {
          setState(() {
            _selectedPetSpecies = value;
            // Clear breed when species changes
            _petBreedController.clear();
          });
          // Reload breeds for the new species
          _loadBreedsForSpecies(value);
        }
      },
    );
  }

  Widget _buildPetBreedField() {
    return Autocomplete<String>(
      optionsBuilder: (TextEditingValue textEditingValue) {
        final suggestions = _currentBreeds.isNotEmpty
            ? _currentBreeds
            : ['Metis', 'Necunoscut'];
        if (textEditingValue.text.isEmpty) {
          return suggestions;
        }
        return suggestions.where((option) =>
            option.toLowerCase().contains(textEditingValue.text.toLowerCase()));
      },
      onSelected: (String selection) {
        _petBreedController.text = selection;
      },
      fieldViewBuilder: (context, controller, focusNode, onEditingComplete) {
        // Sync the autocomplete controller with our breed value
        if (controller.text != _petBreedController.text) {
          controller.text = _petBreedController.text;
        }

        return TextFormField(
          controller: controller,
          focusNode: focusNode,
          onEditingComplete: onEditingComplete,
          onChanged: (value) {
            _petBreedController.text = value;
          },
          textCapitalization: TextCapitalization.words,
          decoration: InputDecoration(
            labelText: 'Rasă *',
            hintText: _isLoadingBreeds
                ? 'Se încarcă rasele...'
                : 'Începeți să tastați pentru sugestii...',
            prefixIcon: const Icon(Icons.search),
            suffixIcon: _isLoadingBreeds
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: Padding(
                      padding: EdgeInsets.all(12.0),
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  )
                : IconButton(
                    icon: const Icon(Icons.refresh),
                    onPressed: () => _loadBreedsForSpecies(_selectedPetSpecies),
                    tooltip: 'Actualizează lista de rase',
                  ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            filled: true,
            fillColor: Colors.white,
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Rasa este obligatorie';
            }
            return null;
          },
          enabled: !_isSubmitting,
        );
      },
    );
  }

  Widget _buildPetSizeField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Mărime *',
          style: TextStyle(fontSize: 14, color: Colors.black87),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Row(
            children: [
              _buildSizeOption('S', 'Mic'),
              const SizedBox(width: 12),
              _buildSizeOption('M', 'Mediu'),
              const SizedBox(width: 12),
              _buildSizeOption('L', 'Mare'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSizeOption(String value, String label) {
    final isSelected = _selectedPetSize == value;
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedPetSize = value;
          });
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected ? _primaryColor : Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected ? _primaryColor : Colors.grey[300]!,
              width: 2,
            ),
          ),
          child: Center(
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: isSelected ? Colors.white : Colors.black87,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNotesField() {
    return TextFormField(
      controller: _notesController,
      maxLines: 3,
      decoration: InputDecoration(
        labelText: 'Observații',
        hintText: 'Detalii suplimentare...',
        prefixIcon: const Icon(Icons.note),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        filled: true,
        fillColor: Colors.white,
      ),
    );
  }

  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: true,
      decoration: InputDecoration(
        labelText: 'Parolă *',
        hintText: 'Introduceți parola de rezervare',
        prefixIcon: const Icon(Icons.lock),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        filled: true,
        fillColor: Colors.white,
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Parola este obligatorie';
        }
        return null;
      },
    );
  }

  Widget _buildSubmitButton() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isSubmitting ? null : _submitBooking,
            style: ElevatedButton.styleFrom(
              backgroundColor: _primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              elevation: 0,
            ),
            child: _isSubmitting
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text(
                    'Finalizează rezervarea',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ),
      ),
    );
  }
}
