import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../l10n/app_localizations.dart';
import '../../models/working_hours_settings.dart';
import '../../providers/calendar_provider.dart';
import '../../services/ui_notification_service.dart';
import '../../services/working_hours_service.dart';
import '../../utils/debug_logger.dart';
import '../../widgets/schedule/advanced_schedule_step.dart';

/// Example screen showing how to use the Advanced Schedule Step
/// This can be integrated into onboarding flows or settings
class ScheduleConfigurationScreen extends StatefulWidget {
  final bool isOnboarding;
  final VoidCallback? onComplete;

  const ScheduleConfigurationScreen({
    Key? key,
    this.isOnboarding = false,
    this.onComplete,
  }) : super(key: key);

  @override
  State<ScheduleConfigurationScreen> createState() => _ScheduleConfigurationScreenState();
}

class _ScheduleConfigurationScreenState extends State<ScheduleConfigurationScreen> {
  Map<String, DaySchedule>? _selectedSchedule;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: widget.isOnboarding
          ? null
          : AppBar(
              title: Text(
                context.tr('schedule_step.configure_schedule'),
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              elevation: 0,
            ),
      body: SafeArea(
        child: AdvancedScheduleStep(
          title: widget.isOnboarding ? null : context.tr('schedule_step.configure_schedule'),
          subtitle: widget.isOnboarding ? null : context.tr('schedule_step.merged_from_staff'),
          onScheduleChanged: (schedule) {
            setState(() {
              _selectedSchedule = schedule;
            });
            DebugLogger.logVerbose('📅 Schedule changed: ${schedule.keys.length} days configured');
          },
          onNext: _handleSaveAndContinue,
          onBack: widget.isOnboarding ? null : () => Navigator.of(context).pop(),
        ),
      ),
    );
  }

  Future<void> _handleSaveAndContinue() async {
    if (_selectedSchedule == null) {
      UINotificationService.showWarning(
        context: context,
        title: context.tr('schedule_step.error_loading'),
        message: 'Please configure your schedule first',
      );
      return;
    }

    try {
      // Save the merged schedule as the salon's business hours
      final request = UpdateWorkingHoursRequest(
        weeklySchedule: _selectedSchedule!,
        holidays: [], // Can be populated from merged data
        customClosures: [],
      );

      final response = await WorkingHoursService.updateWorkingHours(request);

      if (response.success) {
        // Update calendar provider cache
        final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
        await calendarProvider.handleScheduleUpdate(
          salonScheduleChanged: true,
          customClosuresChanged: false,
        );

        DebugLogger.logSuccess('✅ Schedule saved successfully');

        UINotificationService.showSuccess(
          context: context,
          title: context.tr('common.success'),
          message: 'Schedule configured successfully!',
        );

        // Navigate to next step or complete
        if (widget.onComplete != null) {
          widget.onComplete!();
        } else if (mounted) {
          Navigator.of(context).pop();
        }
      } else {
        UINotificationService.showError(
          context: context,
          title: context.tr('common.error'),
          message: response.error ?? 'Failed to save schedule',
        );
      }
    } catch (e) {
      DebugLogger.logError('❌ Error saving schedule: $e');
      UINotificationService.showError(
        context: context,
        title: context.tr('common.error'),
        message: 'Failed to save schedule: $e',
      );
    }
  }
}
