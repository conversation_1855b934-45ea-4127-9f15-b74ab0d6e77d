import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/auth_provider.dart';
import '../../screens/auth/login_screen.dart';
import '../../widgets/onboarding/salon_onboarding_widget.dart';

/// Dedicated onboarding screen for users without salon association
/// Shows welcome content with logout option, no page title
class SalonOnboardingScreen extends StatelessWidget {
  const SalonOnboardingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        // No title - clean onboarding experience
        elevation: 0,
        backgroundColor: Colors.transparent,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () => _logout(context),
            tooltip: 'Deconectare',
          ),
        ],
      ),
      body: const SalonOnboardingWidget(),
    );
  }

  Future<void> _logout(BuildContext context) async {
    // Show confirmation dialog first
    final shouldLogout = await _showLogoutConfirmation(context);

    if (shouldLogout == true) {
      // Log out using the auth provider
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      await authProvider.signOut();

      if (context.mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const LoginScreen()),
        );
      }
    }
  }

  Future<bool?> _showLogoutConfirmation(BuildContext context) {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirmare Deconectare'),
          content: const Text('Ești sigur că vrei să te deconectezi?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Anulează'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('Deconectare'),
            ),
          ],
        );
      },
    );
  }
}
