import 'dart:async';

import 'package:flutter/material.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:provider/provider.dart';

import '../../config/theme/app_theme.dart';
import '../../providers/auth_provider.dart';
import '../../services/ui_notification_service.dart';
import '../../widgets/common/responsive_layout_wrapper.dart';
import '../main_layout.dart';

class OTPVerificationScreen extends StatefulWidget {
  final String phoneNumber;

  const OTPVerificationScreen({
    super.key,
    required this.phoneNumber,
  });

  @override
  State<OTPVerificationScreen> createState() => _OTPVerificationScreenState();
}

class _OTPVerificationScreenState extends State<OTPVerificationScreen> {
  TextEditingController? _otpController;
  bool _isLoading = false;
  int _resendTimer = 60;
  Timer? _timer;
  String _currentText = "";

  @override
  void initState() {
    super.initState();
    _otpController = TextEditingController();
    _startResendTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _otpController?.dispose();
    _otpController = null;
    super.dispose();
  }

  void _startResendTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      setState(() {
        if (_resendTimer == 0) {
          timer.cancel();
        } else {
          _resendTimer--;
        }
      });
    });
  }

  Future<void> _verifyOTP() async {
    if (_currentText.length != 6) {
      UINotificationService.showWarning(
        context: context,
        title: 'Cod invalid',
        message: 'Te rugăm să introduci un cod valid de 6 cifre',
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    try {
      final success = await authProvider.verifyOTP(_currentText);

      if (success && mounted) {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => MainLayout(key: MainLayout.globalKey)),
          (route) => false,
        );
      } else if (mounted) {
        UINotificationService.showError(
          context: context,
          title: 'Cod incorect',
          message: authProvider.error ?? 'Codul introdus nu este corect',
        );
      }
    } catch (e) {
      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: 'Eroare neașteptată',
          message: 'A apărut o problemă la verificarea codului',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _resendOTP() async {
    if (_resendTimer > 0) return;

    setState(() {
      _isLoading = true;
    });

    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    try {
      final success = await authProvider.verifyPhoneNumber(widget.phoneNumber);

      if (success && mounted) {
        UINotificationService.showSuccess(
          context: context,
          title: 'Cod retrimis',
          message: 'Codul de verificare a fost trimis din nou',
        );
        setState(() {
          _resendTimer = 60;
        });
        _startResendTimer();
      } else if (mounted) {
        UINotificationService.showError(
          context: context,
          title: 'Eroare la retrimitere',
          message: authProvider.error ?? 'Nu s-a putut retrimite codul de verificare',
        );
      }
    } catch (e) {
      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: 'Eroare neașteptată',
          message: 'A apărut o problemă la retrimiterea codului',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: ResponsiveFormWrapper(
            child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
              const SizedBox(height: 20),
              Icon(
                Icons.sms,
                size: 80,
                color: colorScheme.primary,
              ),
              const SizedBox(height: 20),
              Text(
                'Cod de verificare',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: isDark ? AppColors.darkText : AppColors.lightText,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 10),
              Text(
                'Veti primi un cod de confirmare la  ${widget.phoneNumber}',
                style: TextStyle(
                  fontSize: 16,
                  color: isDark ? AppColors.darkTextSecondary : AppColors.secondaryText,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 40),
              PinCodeTextField(
                appContext: context,
                length: 6,
                obscureText: false,
                animationType: AnimationType.fade,
                pinTheme: PinTheme(
                  shape: PinCodeFieldShape.box,
                  borderRadius: BorderRadius.circular(8),
                  fieldHeight: 50,
                  fieldWidth: 40,
                  activeFillColor: isDark ? AppColors.darkSurfaceVariant : AppColors.lightGray,
                  inactiveFillColor: isDark ? AppColors.darkSurfaceVariant : AppColors.lightGray,
                  selectedFillColor: isDark ? AppColors.darkAccentContainer : AppColors.lightGrayVariant,
                  activeColor: colorScheme.primary,
                  inactiveColor: isDark ? AppColors.darkBorder : AppColors.secondaryText,
                  selectedColor: colorScheme.primary,
                ),
                animationDuration: const Duration(milliseconds: 300),
                backgroundColor: Colors.transparent,
                enableActiveFill: true,
                controller: _otpController,
                onCompleted: (v) {
                  _currentText = v;
                  _verifyOTP();
                },
                onChanged: (value) {
                  setState(() {
                    _currentText = value;
                  });
                },
                beforeTextPaste: (text) {
                  // You can validate here if needed
                  return true;
                },
              ),
              const SizedBox(height: 30),
              ElevatedButton(
                onPressed: _isLoading ? null : _verifyOTP,
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  foregroundColor: colorScheme.onPrimary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isLoading
                    ? CircularProgressIndicator(color: colorScheme.onPrimary)
                    : const Text(
                        'continua',
                        style: TextStyle(fontSize: 16),
                      ),
              ),
              const SizedBox(height: 20),
              TextButton(
                onPressed: _resendTimer > 0 ? null : _resendOTP,
                child: Text(
                  _resendTimer > 0
                      ? 'Retrimite cod in  $_resendTimer seconds'
                      : 'Retrimite cod',
                  style: TextStyle(
                    color: _resendTimer > 0
                        ? (isDark ? AppColors.darkTextTertiary : AppColors.secondaryText)
                        : colorScheme.primary,
                  ),
                ),
              ),
              const SizedBox(height: 10),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text(
                  'Schimba numarul de telefon',
                  style: TextStyle(color: colorScheme.primary),
                ),
              ),
                ],
              ),
            ),
          ),
        ),
    );
  }
}
