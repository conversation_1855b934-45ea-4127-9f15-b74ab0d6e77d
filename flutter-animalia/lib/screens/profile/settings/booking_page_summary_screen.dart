import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';

import '../../../l10n/app_localizations.dart';
import '../../../models/salon_web_preferences.dart';
import '../../../services/salon_web_preferences_service.dart';
import '../../../services/ui_notification_service.dart';
import '../../../services/url_launcher_service.dart';
import 'website_management_screen.dart';

/// Summary screen for the online booking page
/// Shows the booking URL, QR code, logo, and options to edit
class BookingPageSummaryScreen extends StatefulWidget {
  const BookingPageSummaryScreen({super.key});

  @override
  State<BookingPageSummaryScreen> createState() => _BookingPageSummaryScreenState();
}

class _BookingPageSummaryScreenState extends State<BookingPageSummaryScreen> {
  bool _isLoading = true;
  SalonWebPreferences? _webPreferences;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadWebPreferences();
  }

  Future<void> _loadWebPreferences() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await SalonWebPreferencesService.getWebPreferences();

      if (response.success && response.data != null) {
        setState(() {
          _webPreferences = response.data;
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = response.error ?? 'Failed to load web preferences';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading web preferences: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        backgroundColor: colorScheme.surface,
        elevation: 0,
        title: Text(
          'Pagină Rezervări',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 18,
            color: colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.edit, color: colorScheme.primary),
            onPressed: _navigateToEdit,
            tooltip: 'Editează',
          ),
        ],
      ),
      body: _isLoading
          ? _buildLoadingView()
          : _error != null
              ? _buildErrorView()
              : _webPreferences != null
                  ? _buildSummaryView()
                  : _buildNoDataView(),
    );
  }

  Widget _buildLoadingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            'Se încarcă...',
            style: TextStyle(
              fontSize: 16,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Eroare',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.error,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error ?? 'Eroare necunoscută',
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadWebPreferences,
              icon: const Icon(Icons.refresh),
              label: const Text('Încearcă din nou'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoDataView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.web_asset_off,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
            ),
            const SizedBox(height: 16),
            Text(
              'Nicio pagină configurată',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Configurează pagina de rezervări pentru a vedea detaliile',
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _navigateToEdit,
              icon: const Icon(Icons.add),
              label: const Text('Configurează acum'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryView() {
    final prefs = _webPreferences!;
    final colorScheme = Theme.of(context).colorScheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Logo section
          if (prefs.logoUrl.isNotEmpty) ...[
            _buildLogoSection(prefs, colorScheme),
            const SizedBox(height: 24),
          ],

          // Business Info
          _buildBusinessInfoCard(prefs, colorScheme),
          const SizedBox(height: 16),

          // Booking Link section
          _buildBookingLinkCard(prefs, colorScheme),
          const SizedBox(height: 16),

          // QR Code section
          _buildQRCodeCard(prefs, colorScheme),
          const SizedBox(height: 16),

          // Quick Actions
          _buildQuickActionsCard(prefs, colorScheme),
          const SizedBox(height: 24),

          // Edit button
          _buildEditButton(),
        ],
      ),
    );
  }

  Widget _buildLogoSection(SalonWebPreferences prefs, ColorScheme colorScheme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: colorScheme.outline.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: Image.network(
                  prefs.logoUrl,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Icon(
                      Icons.business,
                      size: 48,
                      color: colorScheme.onSurface.withOpacity(0.3),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              prefs.businessName,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            if (prefs.businessDescription.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                prefs.businessDescription,
                style: TextStyle(
                  fontSize: 14,
                  color: colorScheme.onSurface.withOpacity(0.7),
                ),
                textAlign: TextAlign.center,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBusinessInfoCard(SalonWebPreferences prefs, ColorScheme colorScheme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, size: 20, color: colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Informații',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (prefs.businessAddress.isNotEmpty) _buildInfoRow(Icons.location_on, prefs.businessAddress, colorScheme),
            if (prefs.contactPhone.isNotEmpty) _buildInfoRow(Icons.phone, prefs.contactPhone, colorScheme),
            if (prefs.contactEmail.isNotEmpty) _buildInfoRow(Icons.email, prefs.contactEmail, colorScheme),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String text, ColorScheme colorScheme) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, size: 18, color: colorScheme.primary.withOpacity(0.7)),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                color: colorScheme.onSurface.withOpacity(0.8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBookingLinkCard(SalonWebPreferences prefs, ColorScheme colorScheme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.link, size: 20, color: colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Link de rezervare',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.primaryContainer.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: colorScheme.primary.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'www.animalia-programari.ro/book/'+prefs.bookingWebsiteSlug,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: colorScheme.primary,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  IconButton(
                    onPressed: () => _copyToClipboard('www.animalia-programari.ro/book/'+prefs.bookingWebsiteSlug),
                    icon: Icon(Icons.copy, size: 20),
                    color: colorScheme.primary,
                    tooltip: 'Copiază',
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _openWebsite('www.animalia-programari.ro/book/'+prefs.bookingWebsiteSlug),
                    icon: const Icon(Icons.open_in_browser, size: 18),
                    label: const Text('Deschide'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _shareLink('www.animalia-programari.ro/book/'+prefs.bookingWebsiteSlug, prefs.businessName),
                    icon: const Icon(Icons.share, size: 18),
                    label: const Text('Distribuie'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQRCodeCard(SalonWebPreferences prefs, ColorScheme colorScheme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.qr_code_2, size: 20, color: colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Cod QR',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Center(
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: QrImageView(
                  data: 'www.animalia-programari.ro/book/'+prefs.bookingWebsiteSlug,
                  version: QrVersions.auto,
                  size: 200,
                  backgroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Clienții pot scana acest cod QR pentru a accesa pagina de rezervări',
              style: TextStyle(
                fontSize: 13,
                color: colorScheme.onSurface.withOpacity(0.6),
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => _shareQRCode('www.animalia-programari.ro/book/'+prefs.bookingWebsiteSlug, prefs.businessName),
                icon: const Icon(Icons.download, size: 18),
                label: const Text('Salvează Cod QR'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsCard(SalonWebPreferences prefs, ColorScheme colorScheme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.flash_on, size: 20, color: colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Acțiuni rapide',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildQuickActionItem(
              Icons.color_lens,
              'Culoare: ${prefs.primaryColor}',
              colorScheme,
            ),
            _buildQuickActionItem(
              Icons.policy,
              'Politică anulare: ${_getCancellationPolicyText(prefs.cancellationPolicy)}',
              colorScheme,
            ),
            _buildQuickActionItem(
              Icons.verified,
              'Acceptare: ${_getBookingAcceptanceText(prefs.bookingAcceptance)}',
              colorScheme,
            ),
            if (prefs.websitePhotos.isNotEmpty)
              _buildQuickActionItem(
                Icons.photo_library,
                '${prefs.websitePhotos.length} ${prefs.websitePhotos.length == 1 ? "poză" : "poze"}',
                colorScheme,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionItem(IconData icon, String text, ColorScheme colorScheme) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, size: 18, color: colorScheme.primary.withOpacity(0.7)),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                color: colorScheme.onSurface.withOpacity(0.8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _navigateToEdit,
        icon: const Icon(Icons.edit),
        label: const Text('Editează setările'),
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    UINotificationService.showSuccess(
      context: context,
      title: 'Copiat!',
      message: 'Link-ul a fost copiat în clipboard',
    );
  }

  void _openWebsite(String url) async {
    final success = await UrlLauncherService.openWebUrl(url);
    if (!success && mounted) {
      UrlLauncherService.showLaunchError(context, 'website');
    }
  }

  void _shareLink(String url, String businessName) {
    Share.share(
      'Rezervă online la $businessName: $url',
      subject: 'Rezervare online - $businessName',
    );
  }

  void _shareQRCode(String url, String businessName) {
    // For now, just share the URL. In the future, we could generate and share the actual QR code image
    Share.share(
      'Scanează codul QR sau accesează: $url\n\nReservă online la $businessName',
      subject: 'Cod QR - $businessName',
    );
  }

  Future<void> _navigateToEdit() async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => const WebsiteManagementScreen(),
      ),
    );

    // Reload web preferences if they were updated
    if (result == true && mounted) {
      await _loadWebPreferences();
    }
  }

  String _getCancellationPolicyText(CancellationPolicy policy) {
    switch (policy) {
      case CancellationPolicy.HOURS_24:
        return '24 ore';
      case CancellationPolicy.HOURS_48:
        return '48 ore';
      case CancellationPolicy.HOURS_72:
        return '72 ore';
      case CancellationPolicy.NO_CHANGES:
        return 'Fără modificări';
    }
  }

  String _getBookingAcceptanceText(BookingAcceptance acceptance) {
    switch (acceptance) {
      case BookingAcceptance.automatic:
        return 'Automată';
      case BookingAcceptance.manual:
        return 'Manuală';
    }
  }
}

