import 'package:animaliaproject/screens/profile/settings/notification_settings_screen.dart';
import 'package:animaliaproject/utils/snack_bar_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:showcaseview/showcaseview.dart';

import '../models/notification_history.dart';
import '../models/salon_invitation.dart';
import '../providers/role_provider.dart';
import '../services/appointment/appointment_service.dart';
import '../services/invitation_service.dart';
import '../services/notification_service.dart';
import '../services/tour_keys.dart';
import '../widgets/sms/sms_history_list.dart';
import 'main_layout.dart';
import '../l10n/app_localizations.dart';

// Configuration class for notification icons
class NotificationIconConfig {
  final IconData icon;
  final List<Color> gradientColors;
  final Color shadowColor;
  final IconData? overlayIcon;

  const NotificationIconConfig({
    required this.icon,
    required this.gradientColors,
    required this.shadowColor,
    this.overlayIcon,
  });
}

class NotificationsScreen extends StatefulWidget {

  const NotificationsScreen({Key? key, this.unreadCountNotifier}) : super(key: key);

  /// Global key for accessing state from outside the widget tree
  static final GlobalKey<_NotificationsScreenState> globalKey =
      GlobalKey<_NotificationsScreenState>();

  /// Notifier used to update the unread count badge on bottom navigation
  final ValueNotifier<int>? unreadCountNotifier;

  /// Convenience method to trigger a history reload from outside
  static void loadHistory() {
    globalKey.currentState?._refreshData();
  }

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> with SingleTickerProviderStateMixin {
  List<SalonInvitation> _pendingInvitations = [];
  List<NotificationHistory> _notificationHistory = [];
  bool _isLoadingInvitations = false;
  bool _isLoadingNotifications = false;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadPendingInvitations();
    _loadNotificationHistory();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadPendingInvitations() async {
    setState(() {
      _isLoadingInvitations = true;
    });

    try {
      // Load real pending invitations from API
      final response = await InvitationService.getPendingInvitations();
      if (response.success && response.data != null) {
        _pendingInvitations = response.data!;
      } else {
        _pendingInvitations = [];
      }
    } catch (e) {
      _pendingInvitations = [];
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingInvitations = false;
        });
      }
    }
  }

  Future<void> _loadNotificationHistory() async {
    setState(() {
      _isLoadingNotifications = true;
    });

    try {
      // Try to load real notification history from paginated API
      final response = await NotificationService.getNotificationsPaginated(
        page: 0,
        pageSize: 50,
      );

      if (response.success && response.data != null) {
        final notifications =
            response.data!['notifications'] as List<NotificationHistory>;
        setState(() {
          _notificationHistory = notifications;
        });

        // Update unread count for badge
        final unread = notifications.where((n) => !n.read).length;
        widget.unreadCountNotifier?.value = unread;

        if (mounted) {
        }
      } else {
        // If API fails or returns no data, use empty list
        setState(() {
          _notificationHistory = [];
        });
        widget.unreadCountNotifier?.value = 0;
      }
    } catch (e) {
      // If API is not available or returns wrong format, create sample notifications
      // This provides a graceful fallback while the backend is being developed
      setState(() {
      });
      widget.unreadCountNotifier?.value =
          _notificationHistory.where((n) => !n.read).length;
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingNotifications = false;
        });
      }
    }
  }


  Future<void> _refreshData() async {
    await Future.wait([
      _loadPendingInvitations(),
      _loadNotificationHistory(),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.tr('notifications.title')),
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              text: context.tr('notifications.title'),
              icon: Icon(Icons.notifications, size: 16),
            ),
            Tab(
              text: context.tr('smsHistory'),
              icon: Icon(Icons.sms, size: 16),
            ),
          ],
        ),
        actions: [
          Showcase(
            key: NotificationsTourKeys.settingsKey,
            title: context.tr('notifications.settings_tour_title'),
            description: context.tr('notifications.settings_tour_description'),
            child: IconButton(
              icon: Icon(Icons.settings),
              onPressed: () => Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const NotificationSettingsScreen()),
              ),
              tooltip: context.tr('notifications.settings_tooltip'),
            ),
          ),
          IconButton(
            icon: Icon(Icons.help_outline),
            onPressed: () {
              final tourKeys = NotificationsTourKeys.getNotificationsTourKeys();
              ShowCaseWidget.of(context).startShowCase(tourKeys);
            },
            tooltip: context.tr('notifications.help_tooltip'),
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildNotificationsTab(),
          const SmsHistoryList(),
        ],
      ),
    );
  }

  Widget _buildNotificationsTab() {
    final unreadCount = _notificationHistory.where((n) => !n.read).length;

    return Column(
      children: [
        // Pending invitations section
        if (_isLoadingInvitations || _pendingInvitations.isNotEmpty) ...[
          Showcase(
            key: NotificationsTourKeys.invitationsKey,
            title: context.tr('notifications.invitations_tour_title'),
            description: context.tr('notifications.invitations_tour_description'),
            child: _buildInvitationsSection(),
          ),
          const Divider(height: 1),
        ],
        // Header with statistics
        Showcase(
          key: NotificationsTourKeys.notificationsListKey,
          title: context.tr('notifications.list_tour_title'),
          description: context.tr('notifications.list_tour_description'),
          child: _buildNotificationHeader(unreadCount),
        ),
        // Notifications list
        Expanded(
          child: RefreshIndicator(
            onRefresh: _refreshData,
            color: Theme.of(context).colorScheme.onSurface,
            child: _buildNotificationsList(),
          ),
        ),
      ],
    );
  }

  Widget _buildNotificationHeader(int unreadCount) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 400;

    return Container(
      padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
      child: Row(
        children: [
          Expanded(
            child: Card(
              child: Padding(
                padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
                child: isSmallScreen ? _buildCompactHeader(unreadCount) : _buildStandardHeader(unreadCount),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStandardHeader(int unreadCount) {
    return Column(
      children: [
        // First row: Icon and main title
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.notifications,
                color: Theme.of(context).colorScheme.onSurface,
                size: 24,
              ),
            ),
            SizedBox(width: 16),
            Expanded(
              child: Text(
                context.tr('notifications.count', params: {
                  'count': _notificationHistory.length.toString(),
                }),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        // Second row: Status and button
        Row(
          children: [
            SizedBox(width: 64), // Space to align with text above (icon width + spacing)
            Expanded(
              child: Text(
                unreadCount > 0
                    ? context.tr('notifications.unread_count', params: {
                        'count': unreadCount.toString(),
                      })
                    : context.tr('notifications.all_read'),
                style: TextStyle(
                  fontSize: 14,
                  color: unreadCount > 0
                      ? Theme.of(context).colorScheme.error
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                  fontWeight: unreadCount > 0 ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
            if (unreadCount > 0)
              TextButton(
                onPressed: _markAllAsRead,
                child: Text(context.tr('notifications.mark_all_read')),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildCompactHeader(int unreadCount) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Single row with icon and count
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                Icons.notifications,
                color: Theme.of(context).colorScheme.onSurface,
                size: 20,
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.tr('notifications.count', params: {
                      'count': _notificationHistory.length.toString(),
                    }),
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        if (unreadCount > 0) ...[
          SizedBox(height: 12),
          // Status and compact button
          Row(
            children: [
              Expanded(
                child: Text(
                  context.tr('notifications.unread_count', params: {
                    'count': unreadCount.toString(),
                  }),
                  style: TextStyle(
                    fontSize: 13,
                    color: Theme.of(context).colorScheme.error,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              TextButton(
                onPressed: _markAllAsRead,
                style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  minimumSize: Size(0, 32),
                ),
                child: Text(
                  context.tr('notifications.read_all'),
                  style: TextStyle(fontSize: 12),
                ),
              ),
            ],
          ),
        ] else ...[
          SizedBox(height: 8),
          Text(
            context.tr('notifications.all_read'),
            style: TextStyle(
              fontSize: 13,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildNotificationsList() {
    if (_isLoadingNotifications) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_notificationHistory.isEmpty) {
      return ListView(
        children: [
          SizedBox(height: 100),
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.notifications_none,
                  size: 64,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                SizedBox(height: 16),
                Text(
                  context.tr('notifications.no_notifications'),
                  style: TextStyle(
                    fontSize: 18,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  context.tr('notifications.notifications_will_appear'),
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _notificationHistory.length,
      itemBuilder: (context, index) {
        final notification = _notificationHistory[index];
        return _buildSwipeableNotificationItem(notification, index);
      },
    );
  }

  Widget _buildSwipeableNotificationItem(NotificationHistory notification, int index) {
    return Dismissible(
      key: Key(notification.id),
      direction: DismissDirection.endToStart,
      background: Container(
        margin: const EdgeInsets.only(bottom: 8),
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20.0),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.error,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.delete_outline,
              color: Theme.of(context).colorScheme.onError,
              size: 28,
            ),
            const SizedBox(height: 4),
            Text(
              context.tr('notifications.swipe_to_delete'),
              style: TextStyle(
                color: Theme.of(context).colorScheme.onError,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
      confirmDismiss: (direction) async {
        // Vibrate for haptic feedback
        HapticFeedback.mediumImpact();

        // Return true to confirm the dismiss
        return true;
      },
      onDismissed: (direction) {
        _deleteNotification(index);
      },
      child: _buildNotificationItem(notification, index),
    );
  }

  Widget _buildNotificationItem(NotificationHistory notification, int index) {
    final isUnread = !notification.read;

    // Get awesome, colorful, and elegant icon configuration
    final iconConfig = _getNotificationIconConfig(notification.type, notification.title);

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: isUnread ? 3 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isUnread
          ? BorderSide(color: Theme.of(context).colorScheme.onSurface, width: 1)
          : BorderSide.none,
      ),
      child: InkWell
      (
        onTap: () => _handleNotificationTap(notification, index),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              _buildNotificationIcon(iconConfig),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      notification.title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: isUnread ? FontWeight.bold : FontWeight.w600,
                        color: isUnread
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    SizedBox(height: 6),
                    Text(
                      notification.message,
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                        height: 1.3,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (notification.appointmentTime != null) ...[
                      SizedBox(height: 4),
                      Text(
                        DateFormat('HH:mm').format(notification.appointmentTime!),
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                    SizedBox(height: 8),
                    Text(
                      _formatNotificationTime(notification.timestamp),
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                children: [
                  if (isUnread)
                    Container(
                      width: 10,
                      height: 10,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.onSurface,
                        shape: BoxShape.circle,
                      ),
                    ),
                  SizedBox(height: 8),
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      if (value == 'delete') {
                        _deleteNotification(index);
                      } else if (value == 'mark_read') {
                        _markAsRead(index);
                      }
                    },
                    itemBuilder: (context) => [
                      if (isUnread)
                        PopupMenuItem(
                          value: 'mark_read',
                          child: Row(
                            children: [
                              Icon(Icons.mark_email_read_rounded, size: 18),
                              SizedBox(width: 8),
                              Text(context.tr('notifications.mark_as_read')),
                            ],
                          ),
                        ),
                      PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete_outline_rounded, size: 18, color: Theme.of(context).colorScheme.error),
                            SizedBox(width: 8),
                            Text(context.tr('common.delete')),
                          ],
                        ),
                      ),
                    ],
                    child: Icon(
                      Icons.more_vert_rounded,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      size: 20,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }



  // Get awesome, colorful, and elegant icon configuration based on notification type and title
  NotificationIconConfig _getNotificationIconConfig(String type, String title) {
    // Check title for specific patterns (removing emojis)
    final cleanTitle = title.replaceAll(RegExp(r'[📅❌🔄🗑️⏰]'), '').trim();

    // Get localized strings for comparison
    final newAppointmentRo = context.tr('notification_settings.new_appointments');
    final newAppointmentEn = 'New Appointment';
    final cancelledRo = context.tr('notification_settings.appointment_cancellations');
    final cancelledEn = 'Cancelled';
    final rescheduledRo = context.tr('notification_settings.appointment_rescheduled');
    final rescheduledEn = 'Rescheduled';
    final deletedRo = context.tr('notification_settings.deleted_appointment');
    final deletedEn = context.tr('notification_settings.deleted');

    if (cleanTitle.contains(newAppointmentRo) || cleanTitle.contains(newAppointmentEn) ||
        cleanTitle.contains('Programare Nouă') || cleanTitle.contains('Programare nouă')) {
      return NotificationIconConfig(
        icon: Icons.event_available_rounded,
        gradientColors: [Color(0xFF4CAF50), Color(0xFF81C784)], // Green gradient
        shadowColor: Color(0xFF4CAF50),
      );
    }

    if (cleanTitle.contains(cancelledRo) || cleanTitle.contains(cancelledEn) ||
        cleanTitle.contains('Programare Anulată') || cleanTitle.contains('Anulare')) {
      return NotificationIconConfig(
        icon: Icons.event_busy_rounded,
        gradientColors: [Color(0xFFF44336), Color(0xFFEF5350)], // Red gradient
        shadowColor: Color(0xFFF44336),
        overlayIcon: Icons.close,
      );
    }

    if (cleanTitle.contains(rescheduledRo) || cleanTitle.contains(rescheduledEn) ||
        cleanTitle.contains('Programare Reprogramată') || cleanTitle.contains('Reprogramată')) {
      return NotificationIconConfig(
        icon: Icons.update_rounded,
        gradientColors: [Color(0xFFFF9800), Color(0xFFFFB74D)], // Orange gradient
        shadowColor: Color(0xFFFF9800),
        overlayIcon: Icons.sync,
      );
    }

    if (cleanTitle.contains(deletedRo) || cleanTitle.contains(deletedEn) ||
        cleanTitle.contains('Programare Ștearsă') || cleanTitle.contains('Ștearsă')) {
      return NotificationIconConfig(
        icon: Icons.delete_forever_rounded,
        gradientColors: [Color(0xFF9C27B0), Color(0xFFBA68C8)], // Purple gradient
        shadowColor: Color(0xFF9C27B0),
      );
    }

    if (cleanTitle.contains('Reminder Programare') || cleanTitle.contains('Reminder')) {
      return NotificationIconConfig(
        icon: Icons.schedule_rounded,
        gradientColors: [Color(0xFF2196F3), Color(0xFF64B5F6)], // Blue gradient
        shadowColor: Color(0xFF2196F3),
        overlayIcon: Icons.notifications_active,
      );
    }

    // Fallback based on type
    switch (type) {
      case 'appointment_created':
      case 'new_appointment':
        return NotificationIconConfig(
          icon: Icons.event_available_rounded,
          gradientColors: [Color(0xFF4CAF50), Color(0xFF81C784)],
          shadowColor: Color(0xFF4CAF50),
        );
      case 'appointment_reminder':
      case 'reminder':
        return NotificationIconConfig(
          icon: Icons.schedule_rounded,
          gradientColors: [Color(0xFF2196F3), Color(0xFF64B5F6)],
          shadowColor: Color(0xFF2196F3),
          overlayIcon: Icons.notifications_active,
        );
      case 'appointment_cancelled':
      case 'appointment_cancellation':
      case 'cancellation':
        return NotificationIconConfig(
          icon: Icons.event_busy_rounded,
          gradientColors: [Color(0xFFF44336), Color(0xFFEF5350)],
          shadowColor: Color(0xFFF44336),
          overlayIcon: Icons.close,
        );
      case 'appointment_updated':
        return NotificationIconConfig(
          icon: Icons.update_rounded,
          gradientColors: [Color(0xFFFF9800), Color(0xFFFFB74D)],
          shadowColor: Color(0xFFFF9800),
          overlayIcon: Icons.sync,
        );
      case 'appointment_completed':
        return NotificationIconConfig(
          icon: Icons.task_alt_rounded,
          gradientColors: [Color(0xFF4CAF50), Color(0xFF66BB6A)],
          shadowColor: Color(0xFF4CAF50),
        );
      default:
        return NotificationIconConfig(
          icon: Icons.notifications_rounded,
          gradientColors: [Color(0xFF9E9E9E), Color(0xFFBDBDBD)],
          shadowColor: Color(0xFF9E9E9E),
        );
    }
  }

  // Build awesome notification icon with gradient background and shadow
  Widget _buildNotificationIcon(NotificationIconConfig config) {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: config.gradientColors,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(14),
        boxShadow: [
          BoxShadow(
            color: config.shadowColor.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: config.shadowColor.withValues(alpha: 0.1),
            blurRadius: 16,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Icon(
            config.icon,
            color: Colors.white,
            size: 24,
          ),
          if (config.overlayIcon != null)
            Positioned(
              right: 2,
              top: 2,
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  config.overlayIcon!,
                  color: config.gradientColors.first,
                  size: 12,
                ),
              ),
            ),
        ],
      ),
    );
  }

  String _formatNotificationTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return context.tr('notifications.now');
    } else if (difference.inMinutes < 60) {
      return context.tr('notifications.minutes_ago', params: {
        'minutes': difference.inMinutes.toString(),
      });
    } else if (difference.inHours < 24) {
      return context.tr('notifications.hours_ago', params: {
        'hours': difference.inHours.toString(),
      });
    } else if (difference.inDays == 1) {
      return context.tr('notifications.yesterday_at', params: {
        'time': DateFormat('HH:mm').format(time),
      });
    } else if (difference.inDays < 7) {
      return context.tr('notifications.days_ago', params: {
        'days': difference.inDays.toString(),
      });
    } else {
      final locale = Localizations.localeOf(context).languageCode;
      return DateFormat('dd MMM yyyy, HH:mm', locale).format(time);
    }
  }

  Future<void> _markAsRead(int index) async {
    final notification = _notificationHistory[index];
    if (notification.read) return;

    try {
      final response = await NotificationService.markAsRead(notification.id);
      if (response.success) {
        setState(() {
          _notificationHistory[index] = notification.copyWith(read: true);
        });
        widget.unreadCountNotifier?.value =
            _notificationHistory.where((n) => !n.read).length;
      }
    } catch (e) {
      // Fallback: update locally if API is not available
      setState(() {
        _notificationHistory[index] = notification.copyWith(read: true);
      });
      widget.unreadCountNotifier?.value =
          _notificationHistory.where((n) => !n.read).length;
    }
  }

  Future<void> _markAllAsRead() async {
    final unreadNotifications = _notificationHistory.where((n) => !n.read).toList();
    if (unreadNotifications.isEmpty) return;

    try {
      // Use the new bulk mark as read API
      final response = await NotificationService.markNotificationsAsRead(markAll: true);
      if (response.success) {
        setState(() {
          for (int i = 0; i < _notificationHistory.length; i++) {
            if (!_notificationHistory[i].read) {
              _notificationHistory[i] = _notificationHistory[i].copyWith(read: true);
            }
          }
        });
        widget.unreadCountNotifier?.value = 0;
      }
    } catch (e) {
      // Fallback: update locally if API is not available
      setState(() {
        for (int i = 0; i < _notificationHistory.length; i++) {
          if (!_notificationHistory[i].read) {
            _notificationHistory[i] = _notificationHistory[i].copyWith(read: true);
          }
        }
      });
      widget.unreadCountNotifier?.value = 0;
    }
  }

  Future<void> _deleteNotification(int index) async {
    final notification = _notificationHistory[index];

    try {
      final response = await NotificationService.deleteNotification(notification.id);
      if (!mounted) return;

      if (response.success) {
        setState(() {
          _notificationHistory.removeAt(index);
        });
        widget.unreadCountNotifier?.value =
            _notificationHistory.where((n) => !n.read).length;

      } else {
        showTopSnackBarSafe(context,
          SnackBar(
            content: Text(context.tr('notifications.error_deleting_notification', params: {
              'error': response.error?.toString() ?? '',
            })),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } catch (e) {
      // Fallback: delete locally if API is not available
      if (!mounted) return;

      setState(() {
        _notificationHistory.removeAt(index);
      });
      widget.unreadCountNotifier?.value =
          _notificationHistory.where((n) => !n.read).length;

    }
  }

  Future<void> _handleNotificationTap(
      NotificationHistory notification, int index) async {
    await _markAsRead(index);

    final appointmentId = notification.appointmentId;
    if (appointmentId == null) return;

    try {
      final response = await AppointmentService.getAppointment(appointmentId);
      if (!mounted) return;

      if (response.success && response.data != null) {
        final appt = response.data!;
        MainLayout.openAppointmentInCalendar(appt.id);
      } else {
        showTopSnackBarSafe(
          context,
          SnackBar(
            content: Text(context.tr('notifications.appointment_not_found')),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;
      showTopSnackBarSafe(
        context,
        SnackBar(
          content: Text(context.tr('notifications.error_opening_appointment', params: {
            'error': e.toString(),
          })),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  Widget _buildInvitationsSection() {
    return Container(
      color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.mail_outline,
                    color: Colors.blue.shade700,
                    size: 20,
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        context.tr('notifications.invitations_pending'),
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onPrimary,
                        ),
                      ),
                      Text(
                        context.tr('notifications.invitations_from_salons', params: {
                          'count': _pendingInvitations.length.toString(),
                        }),
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).colorScheme.onPrimary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Constrain invitations height to prevent overflow
          ConstrainedBox(
            constraints: const BoxConstraints(
              maxHeight: 300, // Limit height to prevent overflow
            ),
            child: SingleChildScrollView(
              child: Column(
                children: _pendingInvitations.map((invitation) => _buildInvitationCard(invitation)).toList(),
              ),
            ),
          ),
          SizedBox(height: 8),
        ],
      ),
    );
  }

  Widget _buildInvitationCard(SalonInvitation invitation) {
    final daysLeft = invitation.expiresAt.difference(DateTime.now()).inDays;
    final isExpiringSoon = daysLeft <= 2;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 3), // Reduced vertical margin
      child: Card(
        elevation: 2, // Reduced elevation
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10), // Slightly smaller radius
          side: BorderSide(
            color: isExpiringSoon ? Colors.orange : Colors.blue.shade200,
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12), // Reduced padding
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          invitation.salonName,
                          style:  TextStyle(
                            fontSize: 16, // Reduced font size
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                        SizedBox(height: 2), // Reduced spacing
                        Text(
                          context.tr('notifications.invited_by', params: {
                            'name': invitation.invitedByName,
                          }),
                          style: TextStyle(
                            fontSize: 12, // Reduced font size
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2), // Reduced padding
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      invitation.proposedRole.displayName,
                      style: TextStyle(
                        fontSize: 10, // Reduced font size
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8), // Reduced spacing

              // Message (truncated if too long)
              if (invitation.message != null) ...[
                Text(
                  invitation.message!.length > 80
                      ? '${invitation.message!.substring(0, 80)}...'
                      : invitation.message!,
                  style: TextStyle(
                    fontSize: 12, // Reduced font size
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    height: 1.2, // Reduced line height
                  ),
                ),
                SizedBox(height: 8), // Reduced spacing
              ],

              // Compact permissions and expiry info
              Row(
                children: [
                  // Permissions info (compact)
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4), // Reduced padding
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.security,
                            size: 12, // Reduced icon size
                            color: Colors.grey.shade600,
                          ),
                          SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              invitation.proposedClientDataPermission.displayName,
                              style: TextStyle(
                                fontSize: 10, // Reduced font size
                                  color: Theme.of(context).colorScheme.onSecondary,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Expiry warning (compact)
                  if (isExpiringSoon) ...[
                    SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade50,
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(color: Colors.orange.shade200),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.warning_amber,
                            size: 12,
                            color: Colors.orange.shade700,
                          ),
                          SizedBox(width: 4),
                          Text(
                            daysLeft == 0
                                ? context.tr('notifications.today')
                                : context.tr('notifications.days_short', params: {
                                    'days': daysLeft.toString(),
                                  }),
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: Colors.orange.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
              SizedBox(height: 8), // Reduced spacing

              // Action buttons (compact)
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => _declineInvitation(invitation),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.red,
                        side: BorderSide(color: Colors.red),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                      child: Text(context.tr('notifications.decline'), style: TextStyle(fontSize: 12)),
                    ),
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: () => _acceptInvitation(invitation),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                      child: Text(context.tr('notifications.accept'), style: TextStyle(fontSize: 12, color: Theme.of(context).colorScheme.onPrimary)),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _acceptInvitation(SalonInvitation invitation) async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) =>  Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.primary),
          ),
        ),
      );

      // Call actual API
      final response = await InvitationService.acceptInvitation(invitation.id);

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        if (response.success) {
          // Remove invitation from list
          setState(() {
            _pendingInvitations.removeWhere((inv) => inv.id == invitation.id);
          });

          // Refresh role provider to update permissions
          final roleProvider = context.read<RoleProvider>();
          await roleProvider.refresh();

          // 🔧 FIX: Reload notifications to remove accepted invitation from list
          await _loadPendingInvitations();

          showTopSnackBarSafe(context,
            SnackBar(
              content: Text(context.tr('notifications.joined_salon_success', params: {
                'salonName': invitation.salonName,
              })),
              backgroundColor: Theme.of(context).colorScheme.primary,
              duration: const Duration(seconds: 4),
            ),
          );

          // Navigate to profile screen to see the new salon
          _navigateToProfile();
        } else {
          showTopSnackBarSafe(context,
            SnackBar(
              content: Text(response.error ?? context.tr('notifications.error_accepting_invitation')),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        showTopSnackBarSafe(context,
          SnackBar(
            content: Text(context.tr('notifications.error_accepting_invitation_detail', params: {
              'error': e.toString(),
            })),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _declineInvitation(SalonInvitation invitation) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('notifications.decline_invitation_title')),
        content: Text(
          context.tr('notifications.decline_invitation_message', params: {
            'salonName': invitation.salonName,
          }),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(context.tr('common.cancel')),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(context.tr('notifications.decline')),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final response = await InvitationService.declineInvitation(invitation.id);

        if (response.success) {
          if (mounted) {
            // Remove invitation from list
            setState(() {
              _pendingInvitations.removeWhere((inv) => inv.id == invitation.id);
            });

            // Refresh role provider to ensure all state is updated
            final roleProvider = context.read<RoleProvider>();
            await roleProvider.refresh();

            // 🔧 FIX: Reload notifications to remove declined invitation from list
            await _loadPendingInvitations();

            showTopSnackBarSafe(context,
              SnackBar(
                content: Text(context.tr('notifications.invitation_declined', params: {
                  'salonName': invitation.salonName,
                })),
                backgroundColor: Colors.orange,
              ),
            );
          }
        } else {
          if (mounted) {
            showTopSnackBarSafe(context,
              SnackBar(
                content: Text(response.error ?? context.tr('notifications.could_not_decline_invitation')),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          showTopSnackBarSafe(context,
            SnackBar(
              content: Text(context.tr('notifications.error_declining_invitation', params: {
                'error': e.toString(),
              })),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  /// Navigate to main layout with bottom navigation
  void _navigateToProfile() {
    // Navigate to main layout which will automatically show the calendar tab
    // since the user now has salon association after accepting the invitation
    // Use replacement navigation since this is after accepting an invitation
    Navigator.of(context).pushReplacementNamed('/');
  }

}
