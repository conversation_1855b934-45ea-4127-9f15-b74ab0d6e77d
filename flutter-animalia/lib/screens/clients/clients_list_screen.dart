import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/snack_bar_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:flutter_speed_dial/flutter_speed_dial.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:showcaseview/showcaseview.dart';

import '../../config/theme/app_theme.dart';
import '../../l10n/app_localizations.dart';
import '../../models/client.dart';
import '../../models/user_role.dart';
import '../../providers/client_provider.dart';
import '../../providers/role_provider.dart';
import '../../services/auth/auth_service.dart';
import '../../services/tour_keys.dart';
import '../../widgets/analytics/comprehensive_analytics_mixin.dart';
import 'add_client_screen.dart';
import 'client_details_screen.dart';

class ClientsListScreen extends StatefulWidget {
  final bool showAppBar;
  final bool showSearchBar;

  const ClientsListScreen({
    super.key,
    this.showAppBar = true,
    this.showSearchBar = true,
  });

  @override
  State<ClientsListScreen> createState() => _ClientsListScreenState();
}

class _ClientsListScreenState extends State<ClientsListScreen>
    with ComprehensiveAnalyticsMixin {
  // Analytics mixin implementation
  @override
  String get screenName => 'clients_list_screen';

  @override
  String get screenCategory => 'clients';

  @override
  String? get entryPoint => 'tab_navigation';

  final TextEditingController _searchController = TextEditingController();

  // Bulk delete state
  bool _isBulkDeleteMode = false;
  Set<String> _selectedClientIds = <String>{};

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);

    // Initialize or refresh the client provider to ensure current salon data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final clientProvider = context.read<ClientProvider>();
      if (!clientProvider.isInitialized) {
        DebugLogger.logInit(
            '🔄 ClientsListScreen: Initializing ClientProvider...');
        clientProvider.initialize();
      } else {
        DebugLogger.logInit(
            '🔄 ClientsListScreen: Refreshing ClientProvider for current salon...');
        clientProvider.refresh();
      }
    });
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _toggleBulkDeleteMode() {
    setState(() {
      _isBulkDeleteMode = !_isBulkDeleteMode;
      if (!_isBulkDeleteMode) {
        _selectedClientIds.clear();
      }
    });
  }

  void _toggleClientSelection(String clientId) {
    setState(() {
      if (_selectedClientIds.contains(clientId)) {
        _selectedClientIds.remove(clientId);
      } else {
        _selectedClientIds.add(clientId);
      }
    });
  }

  void _selectAllClients(ClientProvider clientProvider) {
    setState(() {
      if (_selectedClientIds.length == clientProvider.filteredClients.length) {
        // If all are selected, deselect all
        _selectedClientIds.clear();
      } else {
        // Select all visible clients
        _selectedClientIds.clear();
        _selectedClientIds.addAll(
          clientProvider.filteredClients.map((client) => client.id),
        );
      }
    });
  }

  Future<void> _deleteSelectedClients() async {
    if (_selectedClientIds.isEmpty) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) =>
          AlertDialog(
            title: Text(AppLocalizations.of(context).translate(
                'clients.confirm_delete')),
            content: Text(
              _selectedClientIds.length == 1
                  ? AppLocalizations.of(context).translate(
                  'clients.delete_single_confirm')
                  : AppLocalizations.of(context).translate(
                  'clients.delete_multiple_confirm')
                  .replaceAll('{count}', _selectedClientIds.length.toString()),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text(
                    AppLocalizations.of(context).translate('common.cancel')),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(
                  foregroundColor: Theme
                      .of(context)
                      .colorScheme
                      .error,
                ),
                child: Text(
                    AppLocalizations.of(context).translate('common.delete')),
              ),
            ],
          ),
    );

    if (confirmed != true) return;

    final clientProvider = context.read<ClientProvider>();
    final clientIdsToDelete = List<String>.from(_selectedClientIds);

    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) =>
          AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text(AppLocalizations.of(context).translate(
                    'clients.deleting_clients')),
              ],
            ),
          ),
    );

    try {
      final result = await clientProvider.deleteMultipleClients(
          clientIdsToDelete);

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        final deletedCount = result['deletedCount'] as int? ?? 0;
        final failedCount = result['failedCount'] as int? ?? 0;
        final errors = result['errors'] as List<dynamic>? ?? [];

        if (deletedCount > 0) {
          setState(() {
            _selectedClientIds.clear();
            if (deletedCount == clientIdsToDelete.length) {
              _isBulkDeleteMode = false;
            }
          });

          String message = deletedCount == 1
              ? AppLocalizations.of(context).translate(
              'clients.delete_single_success')
              : AppLocalizations.of(context).translate(
              'clients.delete_multiple_success')
              .replaceAll('{count}', deletedCount.toString());

          if (failedCount > 0) {
            message += '\n' + (failedCount == 1
                ? AppLocalizations.of(context).translate(
                'clients.delete_single_partial_fail')
                : AppLocalizations.of(context).translate(
                'clients.delete_multiple_partial_fail')
                .replaceAll('{count}', failedCount.toString()));
          }

          showTopSnackBar(
            context,
            SnackBar(
              content: Text(message),
              backgroundColor: deletedCount > 0
                  ? Theme
                  .of(context)
                  .colorScheme
                  .primary
                  : Theme
                  .of(context)
                  .colorScheme
                  .error,
            ),
          );
        } else {
          String errorMessage = AppLocalizations.of(context).translate(
              'clients.delete_none_success');
          if (errors.isNotEmpty) {
            errorMessage += '\n' + AppLocalizations.of(context).translate(
                'clients.errors_details') + ': ${errors.join(', ')}';
          }

          showTopSnackBar(
            context,
            SnackBar(
              content: Text(errorMessage),
              backgroundColor: Theme
                  .of(context)
                  .colorScheme
                  .error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        showTopSnackBar(
          context,
          SnackBar(
            content: Text(
                AppLocalizations.of(context).translate('clients.delete_error') +
                    ': $e'),
            backgroundColor: Theme
                .of(context)
                .colorScheme
                .error,
          ),
        );
      }
    }
  }

  void _onSearchChanged() {
    final clientProvider = context.read<ClientProvider>();
    final query = _searchController.text;

    // Track search usage
    if (query.isNotEmpty) {
      trackSearch(
        query: query,
        resultsCount: clientProvider.filteredClients.length,
        context: {
          'search_category': 'clients',
          'query_length': query.length,
        },
      );
    }

    clientProvider.searchClients(query);
  }

  void _navigateToClientDetails(Client client) {
    // Track navigation to client details
    trackNavigationTo('client_details_screen', method: 'client_list_tap');

    // Track client interaction
    trackClientInteraction(
      clientId: client.id,
      interactionType: 'view_details',
      successful: true,
      context: {
        'has_pets': client.petCount > 0,
        'client_source': 'client_list',
      },
    );

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ClientDetailsScreen(client: client),
      ),
    );
  }

  void _addNewClient() async {
    // Track button click for adding new client
    trackButtonClick('add_client', context: {
      'trigger_source': 'floating_action_button',
      'current_clients_count': context
          .read<ClientProvider>()
          .clients
          .length,
    });

    // Track navigation to add client screen
    trackNavigationTo('add_client_screen', method: 'add_client_button');

    // Check limits before navigating
    final salonId = await AuthService.getCurrentSalonId();

    final result = await Navigator.of(context).push<Client>(
      MaterialPageRoute(
        builder: (context) => const AddClientScreen(),
      ),
    );

    // If a client was successfully created, refresh the provider
    if (result != null && mounted) {
      // Track successful client creation
      trackClientCreation(
        clientId: result.id,
        hasProfilePhoto: false,
        // AddClientScreen doesn't support photos yet
        fieldsCompleted: _countCompletedClientFields(result),
        totalFields: 4,
        // name, phone, email, address
        context: {
          'creation_source': 'client_list_screen',
          'has_pets': result.petCount > 0,
        },
      );

      final clientProvider = context.read<ClientProvider>();
      await clientProvider.refresh();


      // Show success message
      if (mounted) {
        showTopSnackBar(context,
          SnackBar(
            content: Text(AppLocalizations.of(context).translate(
                'clients.client_added_success')),
            backgroundColor: Theme
                .of(context)
                .colorScheme
                .primary,
            action: SnackBarAction(
              label: AppLocalizations.of(context).translate(
                  'clients.view_details'),
              textColor: AppColors.white,
              onPressed: () => _navigateToClientDetails(result),
            ),
          ),
        );
      }
    }
  }

  void _importClientFromContacts() async {
    DebugLogger.logVerbose('🚀 Import contacts button tapped');

    // Check if running on web

    // Mobile platforms - try flutter_contacts native permission first (more reliable on iOS)
    try {
      DebugLogger.logVerbose('🔍 Trying flutter_contacts native permission...');
      final permission = await FlutterContacts.requestPermission();
      DebugLogger.logVerbose(
          '🔍 Flutter contacts permission result: $permission');

      if (permission) {
        DebugLogger.logVerbose(
            '✅ Flutter contacts permission granted, showing import options');
        await _showImportOptionsDialog();
        return;
      }
    } catch (e) {
      DebugLogger.logVerbose(
          '⚠️ Flutter contacts permission failed: $e, falling back to permission_handler');
    }

    // Fallback to permission_handler approach
    await _handleContactsPermissionNative();
  }

  Future<void> _handleContactsPermissionNative() async {
    DebugLogger.logVerbose('🔍 Starting native contacts permission check...');

    try {
      // First check if we already have permission
      bool hasPermission = await Permission.contacts.isGranted;
      DebugLogger.logVerbose('🔍 Current permission status: $hasPermission');

      if (!hasPermission) {
        // Request permission using permission_handler for better Android support
        final requestResult = await Permission.contacts.request();
        hasPermission = requestResult.isGranted;
        DebugLogger.logVerbose(
            '🔍 Permission request result: $requestResult, granted: $hasPermission');
      }

      if (hasPermission) {
        DebugLogger.logVerbose(
            '✅ Contacts permission granted, showing import options');
        await _showImportOptionsDialog();
      } else {
        DebugLogger.logVerbose('❌ Contacts permission denied');
        await _showNativePermissionDeniedDialog();
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ Error requesting contacts permission: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context).translate(
              'clients.permission_request_error') + ': $e')),
        );
      }
    }
  }

  Future<void> _showNativePermissionDeniedDialog() async {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.info_outline, color: Theme
                  .of(context)
                  .colorScheme
                  .primary),
              const SizedBox(width: 8),
              Text(AppLocalizations.of(context).translate(
                  'clients.contacts_access')),
            ],
          ),
          content: Text(AppLocalizations.of(context).translate(
              'clients.contacts_permission_needed')),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                  AppLocalizations.of(context).translate('clients.understand')),
            ),
          ],
        );
      },
    );
  }


  Future<void> _showImportOptionsDialog() async {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context).translate(
              'clients.import_contacts')),
          content: Text(AppLocalizations.of(context).translate(
              'clients.choose_import_mode')),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                  AppLocalizations.of(context).translate('common.cancel')),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _performSingleContactImport();
              },
              child: Text(AppLocalizations.of(context).translate(
                  'clients.single_contact')),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _performMultipleContactImport();
              },
              child: Text(AppLocalizations.of(context).translate(
                  'clients.multiple_contacts')),
            ),
          ],
        );
      },
    );
  }


  Future<void> _performSingleContactImport() async {
    try {
      final contact = await FlutterContacts.openExternalPick();
      if (contact == null || !mounted) return;

      final result = await Navigator.of(context).push<Client>(
        MaterialPageRoute(
          builder: (context) =>
              AddClientScreen(
                initialName: contact.displayName,
                initialPhone: (contact.phones.isNotEmpty)
                    ? contact.phones.first.number
                    : '',
              ),
        ),
      );

      if (result != null && mounted) {
        final clientProvider = context.read<ClientProvider>();
        await clientProvider.refresh();
        if (mounted) {
          showTopSnackBar(
            context,
            SnackBar(
              content: Text(AppLocalizations.of(context).translate(
                  'clients.client_added_success')),
              backgroundColor: Theme
                  .of(context)
                  .colorScheme
                  .primary,
              action: SnackBarAction(
                label: AppLocalizations.of(context).translate(
                    'clients.view_details'),
                textColor: AppColors.white,
                onPressed: () => _navigateToClientDetails(result),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context).translate(
              'clients.contact_import_error') + ': $e')),
        );
      }
    }
  }

  Future<void> _performMultipleContactImport() async {
    try {
      DebugLogger.logVerbose('🔍 Starting multiple contact import...');

      // Since we already have permission from the main dialog,
      // proceed directly to contact import
      await _performFlutterContactsMultiImport();
    } catch (e) {
      DebugLogger.logVerbose('❌ Error in multiple contact import: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context).translate(
              'clients.contacts_import_error') + ': $e')),
        );
      }
    }
  }

  Future<List<Contact>?> _showCustomContactPicker(
      List<Contact> contacts) async {
    List<Contact> selectedContacts = [];
    final TextEditingController searchController = TextEditingController();
    List<Contact> filteredContacts = List.from(contacts);

    return await showDialog<List<Contact>>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            void filterContacts(String query) {
              setState(() {
                if (query.isEmpty) {
                  filteredContacts = List.from(contacts);
                } else {
                  filteredContacts = contacts.where((contact) {
                    final name = contact.displayName.toLowerCase();
                    final phone = contact.phones.isNotEmpty ? contact.phones
                        .first.number.toLowerCase() : '';
                    final searchQuery = query.toLowerCase();
                    return name.contains(searchQuery) ||
                        phone.contains(searchQuery);
                  }).toList();
                }
              });
            }

            return AlertDialog(
              title: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(AppLocalizations.of(context).translate(
                      'clients.select_contacts')),
                  const SizedBox(height: 4),
                  Text(
                    AppLocalizations.of(context).translate(
                        'clients.contacts_selected_count')
                        .replaceAll(
                        '{selected}', selectedContacts.length.toString())
                        .replaceAll('{total}', contacts.length.toString()),
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme
                          .of(context)
                          .colorScheme
                          .onSurfaceVariant,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                ],
              ),
              content: SizedBox(
                width: double.maxFinite,
                height: 500,
                child: Column(
                  children: [
                    // Search field
                    TextField(
                      controller: searchController,
                      decoration: InputDecoration(
                        labelText: AppLocalizations.of(context).translate(
                            'clients.search_contact'),
                        hintText: AppLocalizations.of(context).translate(
                            'clients.name_or_phone'),
                        prefixIcon: Icon(Icons.search),
                        suffixIcon: searchController.text.isNotEmpty
                            ? IconButton(
                          icon: Icon(Icons.clear),
                          onPressed: () {
                            searchController.clear();
                            filterContacts('');
                          },
                        )
                            : null,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                      ),
                      onChanged: filterContacts,
                    ),
                    const SizedBox(height: 12),
                    // Select All / Deselect All buttons
                    Row(
                      children: [
                        Flexible(
                          child: TextButton.icon(
                            onPressed: () {
                              setState(() {
                                selectedContacts = List.from(filteredContacts);
                              });
                            },
                            icon: const Icon(Icons.select_all, size: 16),
                            label: Text(AppLocalizations.of(context).translate(
                                'clients.select_all')),
                            style: TextButton.styleFrom(
                              textStyle: const TextStyle(fontSize: 12),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Flexible(
                          child: TextButton.icon(
                            onPressed: () {
                              setState(() {
                                selectedContacts = [];
                              });
                            },
                            icon: const Icon(Icons.clear, size: 16),
                            label: Text(AppLocalizations.of(context).translate(
                                'clients.deselect_all')),
                            style: TextButton.styleFrom(
                              textStyle: const TextStyle(fontSize: 12),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const Divider(),
                    // Contacts list
                    Expanded(
                      child: filteredContacts.isEmpty
                          ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.search_off,
                              size: 48,
                              color: Theme
                                  .of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              AppLocalizations.of(context).translate(
                                  'clients.no_contacts_found'),
                              style: TextStyle(
                                color: Theme
                                    .of(context)
                                    .colorScheme
                                    .onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      )
                          : ListView.builder(
                        itemCount: filteredContacts.length,
                        itemBuilder: (context, index) {
                          final contact = filteredContacts[index];
                          final isSelected = selectedContacts.contains(contact);
                          final hasPhone = contact.phones.isNotEmpty;

                          return ListTile(
                            enabled: hasPhone,
                            leading: CircleAvatar(
                              backgroundColor: isSelected
                                  ? Theme
                                  .of(context)
                                  .colorScheme
                                  .primary
                                  : Theme
                                  .of(context)
                                  .colorScheme
                                  .primary
                                  .withOpacity(0.1),
                              child: Text(
                                contact.displayName.isNotEmpty
                                    ? contact.displayName
                                    .substring(0, 1)
                                    .toUpperCase()
                                    : '?',
                                style: TextStyle(
                                  color: isSelected
                                      ? Colors.white
                                      : Theme
                                      .of(context)
                                      .colorScheme
                                      .primary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            title: Text(
                              contact.displayName.isNotEmpty ? contact
                                  .displayName : AppLocalizations
                                  .of(context)
                                  .translate('clients.unknown_name'),
                              style: TextStyle(
                                color: hasPhone ? null : Colors.grey,
                              ),
                            ),
                            subtitle: hasPhone
                                ? Text(contact.phones.first.number)
                                : Text(AppLocalizations.of(context).translate(
                                'clients.no_phone_number'),
                                style: TextStyle(color: Colors.grey)),
                            trailing: hasPhone
                                ? Checkbox(
                              value: isSelected,
                              onChanged: (bool? value) {
                                setState(() {
                                  if (value == true) {
                                    if (!selectedContacts.contains(contact)) {
                                      selectedContacts =
                                      [...selectedContacts, contact];
                                    }
                                  } else {
                                    selectedContacts =
                                        selectedContacts.where((c) =>
                                        c != contact).toList();
                                  }
                                });
                              },
                            )
                                : null,
                            onTap: hasPhone
                                ? () {
                              setState(() {
                                if (isSelected) {
                                  selectedContacts.remove(contact);
                                } else {
                                  selectedContacts.add(contact);
                                }
                              });
                            }
                                : null,
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(null),
                  child: Text(
                      AppLocalizations.of(context).translate('common.cancel')),
                ),
                ElevatedButton(
                  onPressed: selectedContacts.isNotEmpty
                      ? () => Navigator.of(context).pop(selectedContacts)
                      : null,
                  child: Text(AppLocalizations.of(context).translate(
                      'clients.import_count')
                      .replaceAll(
                      '{count}', selectedContacts.length.toString()),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _bulkCreateClientsFromFlutterContacts(
      List<Contact> contacts) async {
    if (!mounted) return;

    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) =>
          AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(
                  color: Theme
                      .of(context)
                      .colorScheme
                      .primary,
                ),
                const SizedBox(height: 16),
                Text(AppLocalizations.of(context).translate(
                    'clients.importing_contacts')
                    .replaceAll('{count}', contacts.length.toString())),
              ],
            ),
          ),
    );

    try {
      final clientProvider = context.read<ClientProvider>();
      final existingClients = clientProvider.clients;

      // Filter out duplicates
      final nonDuplicateContacts = <Contact>[];
      int duplicateCount = 0;

      for (final contact in contacts) {
        final phone = contact.phones.isNotEmpty
            ? contact.phones.first.number
            : '';

        bool isDuplicate = false;
        if (phone.isNotEmpty) {
          isDuplicate = existingClients.any((client) =>
          client.phone.replaceAll(RegExp(r'[^\d+]'), '') ==
              phone.replaceAll(RegExp(r'[^\d+]'), ''));
        }

        if (isDuplicate) {
          duplicateCount++;
        } else {
          nonDuplicateContacts.add(contact);
        }
      }

      int successCount = 0;
      int errorCount = 0;
      List<String> errors = [];

      if (nonDuplicateContacts.isNotEmpty) {
        // Prepare clients for batch creation
        final clientsToCreate = nonDuplicateContacts.map((contact) {
          final name = contact.displayName.isNotEmpty
              ? contact.displayName
              : AppLocalizations.of(context).translate('clients.unknown_name');
          final phone = contact.phones.isNotEmpty
              ? contact.phones.first.number
              : '';

          return Client(
            id: '', // Will be generated by server
            name: name,
            phone: phone,
            registrationDate: DateTime.now(),
          );
        }).toList();

        // Use batch creation
        final result = await clientProvider.addMultipleClients(clientsToCreate);
        if (result.success) {
          successCount = result.data?.length ?? 0;
          // Add server message if available (contains info about invalid phone numbers)
          if (result.message != null && result.message!.isNotEmpty) {
            errors.add(result.message!);
          }
        } else {
          errorCount = clientsToCreate.length;
          errors.add(result.message ?? AppLocalizations.of(context).translate(
              'clients.client_creation_error'));
        }
      }

      // Close loading dialog, refresh clients list, and show results
      if (mounted) {
        Navigator.of(context).pop();

        // Refresh the clients list to show newly added clients
        await clientProvider.refresh();

        _showImportResults(successCount, duplicateCount, errorCount, errors);
      }
    } catch (e) {
      // Close loading dialog
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context).translate(
              'clients.general_import_error') + ': $e')),
        );
      }
    }
  }

  Future<void> _performFlutterContactsMultiImport() async {
    try {
      // Get all contacts
      final contacts = await FlutterContacts.getContacts(withProperties: true);

      if (contacts.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(AppLocalizations.of(context).translate(
                'clients.no_contacts_available'))),
          );
        }
        return;
      }

      // Filter contacts with phone numbers
      final contactsWithPhones = contacts.where((contact) =>
      contact.phones.isNotEmpty).toList();

      if (contactsWithPhones.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(AppLocalizations.of(context).translate(
                'clients.no_contacts_with_phone'))),
          );
        }
        return;
      }

      // Show custom contact picker to let user select which contacts to import
      final selectedContacts = await _showCustomContactPicker(
          contactsWithPhones);

      if (selectedContacts != null && selectedContacts.isNotEmpty) {
        await _bulkCreateClientsFromFlutterContacts(selectedContacts);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context).translate(
              'clients.contacts_access_error') + ': $e')),
        );
      }
    }
  }


  void _showImportResults(int successCount, int duplicateCount, int errorCount,
      List<String> errors) {
    // Show results
    String message = '';
    if (successCount > 0) {
      message +=
          AppLocalizations.of(context).translate('clients.import_success_count')
              .replaceAll('{count}', successCount.toString());
    }
    if (duplicateCount > 0) {
      if (message.isNotEmpty) message += '\n';
      message += AppLocalizations.of(context).translate(
          'clients.duplicate_contacts_ignored')
          .replaceAll('{count}', duplicateCount.toString());
    }
    if (errorCount > 0) {
      if (message.isNotEmpty) message += '\n';
      message +=
          AppLocalizations.of(context).translate('clients.import_errors_count')
              .replaceAll('{count}', errorCount.toString());
    }

    final isSuccess = successCount > 0;
    showTopSnackBar(
      context,
      SnackBar(
        content: Text(message),
        backgroundColor: isSuccess
            ? Theme
            .of(context)
            .colorScheme
            .primary
            : Theme
            .of(context)
            .colorScheme
            .error,
        duration: const Duration(seconds: 4),
        action: errorCount > 0 && errors.isNotEmpty
            ? SnackBarAction(
          label: AppLocalizations.of(context).translate('clients.details'),
          textColor: Theme
              .of(context)
              .colorScheme
              .onError,
          onPressed: () => _showErrorDetails(errors),
        )
            : null,
      ),
    );
  }

  void _showErrorDetails(List<String> errors) {
    showDialog(
      context: context,
      builder: (context) =>
          AlertDialog(
            title: Text(AppLocalizations.of(context).translate(
                'clients.error_details')),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: errors.map((error) =>
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: Text('• $error'),
                    )).toList(),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                    AppLocalizations.of(context).translate('common.ok')),
              ),
            ],
          ),
    );
  }

  /// Refresh client data manually
  Future<void> _refreshClients() async {
    final clientProvider = context.read<ClientProvider>();
    await clientProvider.refresh();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context).translate(
              'clients.client_list_updated')),
          backgroundColor: Theme
              .of(context)
              .colorScheme
              .primary,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<ClientProvider, RoleProvider>(
      builder: (context, clientProvider, roleProvider, child) {
        // Check if user has permission to create clients
        final bool canCreateClients = roleProvider.hasManagementAccess ||
            (roleProvider.canAccessClientData &&
                roleProvider.permissions?.clientDataPermission ==
                    ClientDataPermission.fullAccess);

        return Scaffold(
          appBar: widget.showAppBar ? AppBar(
            title: _isBulkDeleteMode
                ? Text(
                AppLocalizations.of(context).translate('clients.selected_count')
                    .replaceAll(
                    '{count}', _selectedClientIds.length.toString()))
                : Text(AppLocalizations.of(context).translate('clients.title')),
            elevation: 0,
            leading: _isBulkDeleteMode
                ? IconButton(
              icon: const Icon(Icons.close),
              onPressed: _toggleBulkDeleteMode,
              tooltip: AppLocalizations.of(context).translate(
                  'clients.cancel_selection'),
            )
                : null,
            actions: [
              if (_isBulkDeleteMode) ...[
                IconButton(
                  icon: Icon(
                    _selectedClientIds.length ==
                        clientProvider.filteredClients.length
                        ? Icons.deselect
                        : Icons.select_all,
                  ),
                  onPressed: () => _selectAllClients(clientProvider),
                  tooltip: _selectedClientIds.length ==
                      clientProvider.filteredClients.length
                      ? AppLocalizations.of(context).translate(
                      'clients.deselect_all')
                      : AppLocalizations.of(context).translate(
                      'clients.select_all'),
                ),
                IconButton(
                  icon: const Icon(Icons.delete),
                  onPressed: _selectedClientIds.isEmpty
                      ? null
                      : _deleteSelectedClients,
                  tooltip: AppLocalizations.of(context).translate(
                      'clients.delete_selected'),
                ),
              ] else
                ...[
                  if (clientProvider.hasClients)
                    IconButton(
                      icon: const Icon(Icons.remove),
                      onPressed: _toggleBulkDeleteMode,
                      tooltip: AppLocalizations.of(context).translate(
                          'clients.delete_clients'),
                    ),
                  IconButton(
                    icon: const Icon(Icons.help_outline),
                    onPressed: () {
                      final tourKeys = ClientsTourKeys.getClientsTourKeys();
                      ShowCaseWidget.of(context).startShowCase(tourKeys);
                    },
                    tooltip: AppLocalizations.of(context).translate(
                        'clients.help'),
                  ),
                  IconButton(
                    icon: Icon(Icons.refresh),
                    onPressed: clientProvider.isLoading
                        ? null
                        : _refreshClients,
                    tooltip: AppLocalizations.of(context).translate(
                        'clients.refresh_list'),
                  ),
                ],
            ],
          ) : null,
          body: Column(
            children: [
              // Search field (conditionally shown)
              if (widget.showSearchBar)
                Showcase(
                  key: ClientsTourKeys.searchClientKey,
                  title: AppLocalizations.of(context).translate(
                      'clients.search_clients_tour'),
                  description: AppLocalizations.of(context).translate(
                      'clients.search_clients_description'),
                  child: Container(
                    color: Theme
                        .of(context)
                        .colorScheme
                        .surfaceContainerHighest,
                    padding: const EdgeInsets.fromLTRB(16, 16, 16, 16),
                    child: TextField(
                      controller: _searchController,
                      textCapitalization: TextCapitalization.sentences,
                      decoration: InputDecoration(
                        labelText: AppLocalizations.of(context).translate(
                            'clients.search_client'),
                        hintText: AppLocalizations.of(context).translate(
                            'clients.name_phone_email'),
                        prefixIcon: Icon(Icons.search, color: Theme
                            .of(context)
                            .colorScheme
                            .onSurface),
                        suffixIcon: clientProvider.searchQuery.isNotEmpty
                            ? IconButton(
                          icon: Icon(Icons.clear, color: Theme
                              .of(context)
                              .colorScheme
                              .onSurfaceVariant),
                          onPressed: () {
                            _searchController.clear();
                          },
                        )
                            : null,
                        filled: true,
                        fillColor: Theme
                            .of(context)
                            .colorScheme
                            .surface,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: Theme
                              .of(context)
                              .colorScheme
                              .outline, width: 1),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: Theme
                              .of(context)
                              .colorScheme
                              .outline, width: 1),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: Theme
                              .of(context)
                              .colorScheme
                              .primary, width: 2),
                        ),
                      ),
                    ),
                  ),
                ),

              // Results header (only show if there are clients)
              if (!clientProvider.isLoading &&
                  clientProvider.filteredClients.isNotEmpty)
                Container(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 16.0, vertical: 12.0),
                  child: Row(
                    children: [
                      Icon(
                        Icons.people,
                        color: Theme
                            .of(context)
                            .colorScheme
                            .onSurface,
                        size: 20,
                      ),
                      SizedBox(width: 8),
                      Text(
                        AppLocalizations.of(context).translate(
                            'clients.clients_found_count')
                            .replaceAll('{count}',
                            clientProvider.totalClientCount.toString()),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Theme
                              .of(context)
                              .colorScheme
                              .onSurface,
                        ),
                      ),
                    ],
                  ),
                ),

              // Clients list with pull-to-refresh
              Expanded(
                child: Showcase(
                  key: ClientsTourKeys.clientsListKey,
                  title: AppLocalizations.of(context).translate(
                      'clients.clients_list_tour'),
                  description: AppLocalizations.of(context).translate(
                      'clients.clients_list_description'),
                  child: RefreshIndicator(
                    onRefresh: _refreshClients,
                    color: Theme
                        .of(context)
                        .colorScheme
                        .onSurface,
                    child: _buildClientsList(clientProvider),
                  ),
                ),
              ),
            ],
          ),
          // Only show FAB if user has permission to create clients
          floatingActionButton: canCreateClients
              ? FutureBuilder<String?>(
            future: AuthService.getCurrentSalonId(),
            builder: (context, snapshot) {
              // Don't show FAB until we have salon data
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const SizedBox.shrink();
              }

              final salonId = snapshot.data ?? '';

              // If no salon ID, show FAB without limit guard
              if (salonId.isEmpty) {
                return SpeedDial(
                  heroTag: "clients_add_fab",
                  icon: Icons.add,
                  activeIcon: Icons.close,
                  backgroundColor:
                  Theme
                      .of(context)
                      .colorScheme
                      .primary,
                  foregroundColor:
                  Theme
                      .of(context)
                      .colorScheme
                      .onPrimary,
                  children: [
                    SpeedDialChild(
                      child: const Icon(Icons.person_add),
                      label: AppLocalizations.of(context).translate(
                          'clients.add_manual'),
                      onTap: _addNewClient,
                    ),
                    SpeedDialChild(
                      child: const Icon(Icons.contacts),
                      label: AppLocalizations.of(context).translate(
                          'clients.import_contacts'),
                      onTap: _importClientFromContacts,
                    ),
                  ],
                );
              }

              return Showcase(
                key: ClientsTourKeys.addClientKey,
                title: AppLocalizations.of(context).translate(
                    'clients.add_clients_tour'),
                description: AppLocalizations.of(context).translate(
                    'clients.add_clients_description'),
                child: SpeedDial(
                  heroTag: "clients_add_fab",
                  icon: Icons.add,
                  activeIcon: Icons.close,
                  backgroundColor:
                  Theme
                      .of(context)
                      .colorScheme
                      .primary,
                  foregroundColor:
                  Theme
                      .of(context)
                      .colorScheme
                      .onPrimary,
                  children: [
                    SpeedDialChild(
                      child: const Icon(Icons.person_add),
                      label: AppLocalizations.of(context).translate(
                          'clients.add_manual'),
                      onTap: _addNewClient,
                    ),
                    SpeedDialChild(
                      child: const Icon(Icons.contacts),
                      label: AppLocalizations.of(context).translate(
                          'clients.import_contacts'),
                      onTap: _importClientFromContacts,
                    ),
                  ],
                ),
              );
            },
          )
              : null,
        );
      },
    );
  }

  Widget _buildClientsList(ClientProvider clientProvider) {
    if (clientProvider.isLoading) {
      return Center(
        child: CircularProgressIndicator(
          color: Theme
              .of(context)
              .colorScheme
              .onSurface,
        ),
      );
    }

    if (clientProvider.hasError) {
      return ListView(
        physics: const AlwaysScrollableScrollPhysics(),
        children: [
          SizedBox(height: MediaQuery
              .of(context)
              .size
              .height * 0.2),
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: AppTheme.getStatusColor(context, 'error'),
                ),
                SizedBox(height: 16),
                Text(
                  clientProvider.error ??
                      AppLocalizations.of(context).translate(
                          'clients.error_occurred'),
                  style: TextStyle(
                    fontSize: 16,
                    color: AppTheme.getStatusColor(context, 'error'),
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () => clientProvider.refresh(),
                  icon: Icon(Icons.refresh),
                  label: Text(AppLocalizations.of(context).translate(
                      'clients.try_again')),
                  style: ElevatedButton.styleFrom(),
                ),
              ],
            ),
          ),
        ],
      );
    }

    if (clientProvider.filteredClients.isEmpty) {
      return ListView(
        physics: const AlwaysScrollableScrollPhysics(),
        children: [
          SizedBox(height: MediaQuery
              .of(context)
              .size
              .height * 0.2),
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  clientProvider.searchQuery.isEmpty
                      ? Icons.people_outline
                      : Icons.search_off,
                  size: 64,
                  color: Theme
                      .of(context)
                      .colorScheme
                      .onSurfaceVariant,
                ),
                SizedBox(height: 16),
                Text(
                  clientProvider.searchQuery.isEmpty
                      ? AppLocalizations.of(context).translate(
                      'clients.no_clients_registered')
                      : AppLocalizations.of(context).translate(
                      'clients.no_clients_found')
                      .replaceAll('{query}', clientProvider.searchQuery),
                  style: TextStyle(
                    fontSize: 16,
                    color: Theme
                        .of(context)
                        .colorScheme
                        .onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                ),
                if (clientProvider.searchQuery.isEmpty) ...[
                  SizedBox(height: 16),
                  Text(
                    AppLocalizations.of(context).translate(
                        'clients.pull_to_refresh_or_add'),
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme
                          .of(context)
                          .colorScheme
                          .onSurfaceVariant
                          .withValues(alpha: 0.7),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
          ),
        ],
      );
    }

    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification scrollInfo) {
        // Load more when scrolling near bottom (200px threshold)
        if (!clientProvider.isSearching &&
            clientProvider.hasMoreClients &&
            !clientProvider.isLoadingMore &&
            scrollInfo.metrics.pixels >= scrollInfo.metrics.maxScrollExtent - 200) {
          clientProvider.loadMoreClients();
        }
        return false;
      },
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        physics: const AlwaysScrollableScrollPhysics(),
      // Enable pull-to-refresh even with few items
        itemCount: clientProvider.filteredClients.length +
                   (clientProvider.isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          // Show loading indicator at bottom
          if (index == clientProvider.filteredClients.length) {
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 16.0),
              child: Center(
                child: CircularProgressIndicator(
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            );
          }

          final client = clientProvider.filteredClients[index];
          return _buildClientCard(client);
        },
      ),
    );
  }

  Widget _buildClientCard(Client client) {
    final isSelected = _selectedClientIds.contains(client.id);

    return Card(
      key: ValueKey(client.id),
      margin: const EdgeInsets.only(bottom: 8.0),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: _isBulkDeleteMode && isSelected
            ? BorderSide(
          color: Theme
              .of(context)
              .colorScheme
              .primary,
          width: 2,
        )
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: _isBulkDeleteMode
            ? () => _toggleClientSelection(client.id)
            : () => _navigateToClientDetails(client),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              // Checkbox for bulk delete mode
              if (_isBulkDeleteMode) ...[
                Checkbox(
                  value: isSelected,
                  onChanged: (_) => _toggleClientSelection(client.id),
                  activeColor: Theme
                      .of(context)
                      .colorScheme
                      .primary,
                ),
                SizedBox(width: 8),
              ],

              // Profile avatar
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Theme
                          .of(context)
                          .colorScheme
                          .primary,
                      Theme
                          .of(context)
                          .colorScheme
                          .primary
                          .withValues(alpha: 0.8),
                    ],
                  ),
                ),
                child: Center(
                  child: Text(
                    client.name.isNotEmpty ? client.name[0].toUpperCase() : '?',
                    style: TextStyle(
                      color: Theme
                          .of(context)
                          .colorScheme
                          .onPrimary,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

              SizedBox(width: 16),

              // Client info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Client name with pets - apply overflow protection without extra Expanded
                    RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: client.name,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Theme
                                  .of(context)
                                  .colorScheme
                                  .onSurface,
                            ),
                          ),
                          if (client.petNames.isNotEmpty)
                            TextSpan(
                              text: ' (${client.petNames.join(', ')})',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.normal,
                                color: Theme
                                    .of(context)
                                    .colorScheme
                                    .onSurfaceVariant,
                              ),
                            ),
                        ],
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                    SizedBox(height: 4),
                    // Primary phone number
                    if (client.phone.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 2),
                        child: Row(
                          children: [
                            Icon(
                              Icons.phone,
                              size: 16,
                              color: Theme
                                  .of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                            ),
                            SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                client.phone,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Theme
                                      .of(context)
                                      .colorScheme
                                      .onSurfaceVariant,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    // Secondary phone numbers (displayed one per line)
                    ...client.secondaryPhoneNumbers.map((phone) =>
                      Padding(
                        padding: const EdgeInsets.only(bottom: 2),
                        child: Row(
                          children: [
                            Icon(
                              Icons.phone_outlined,
                              size: 16,
                              color: Theme
                                  .of(context)
                                  .colorScheme
                                  .onSurfaceVariant
                                  .withOpacity(0.7),
                            ),
                            SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                phone,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Theme
                                      .of(context)
                                      .colorScheme
                                      .onSurfaceVariant
                                      .withOpacity(0.7),
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ).toList(),
                    if (client.email.isNotEmpty) ...[
                      SizedBox(height: 2),
                      Row(
                        children: [
                          Icon(
                            Icons.email,
                            size: 16,
                            color: Theme
                                .of(context)
                                .colorScheme
                                .onSurfaceVariant,
                          ),
                          SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              client.email,
                              style: TextStyle(
                                fontSize: 12,
                                color: Theme
                                    .of(context)
                                    .colorScheme
                                    .onSurfaceVariant,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                    if (client.petCount > 0) ...[
                      SizedBox(height: 4),
                      // Pets count row with overflow protection
                      Row(
                        children: [
                          Icon(
                            Icons.pets,
                            size: 16,
                            color: Theme
                                .of(context)
                                .colorScheme
                                .secondary,
                          ),
                          SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              '${client.petCount} ${client.petCount == 1
                                  ? AppLocalizations.of(context).translate(
                                  'clients.pet_singular')
                                  : AppLocalizations.of(context).translate(
                                  'clients.pet_plural')}',
                              style: TextStyle(
                                fontSize: 12,
                                color: Theme
                                    .of(context)
                                    .colorScheme
                                    .secondary,
                                fontWeight: FontWeight.w500,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),

              // Arrow indicator (only show when not in bulk delete mode)
              if (!_isBulkDeleteMode)
                Icon(
                  Icons.arrow_forward_ios,
                  color: Theme
                      .of(context)
                      .colorScheme
                      .onSurfaceVariant,
                  size: 16,
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Helper method to count completed client fields for analytics
  int _countCompletedClientFields(Client client) {
    int count = 0;
    if (client.name.isNotEmpty) count++;
    if (client.phone.isNotEmpty) count++;
    if (client.email.isNotEmpty) count++;
    if (client.address.isNotEmpty) count++;
    return count;
  }
}
