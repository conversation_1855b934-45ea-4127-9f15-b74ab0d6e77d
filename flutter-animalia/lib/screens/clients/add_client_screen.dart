import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../config/theme/app_theme.dart';
import '../../l10n/app_localizations.dart';
import '../../models/client.dart';
import '../../models/pet.dart';
import '../../services/client/client_service.dart';
import '../../utils/formatters/phone_number_utils.dart';
import '../../widgets/analytics/comprehensive_analytics_mixin.dart';
import '../../widgets/address_selection/location_selection_button.dart';
import 'add_pet_screen.dart';

class AddClientScreen extends StatefulWidget {
  final String? initialName;
  final String? initialPhone;
  final String? initialEmail;
  final String? initialAddress;
  final String? initialNotes;

  const AddClientScreen({
    super.key,
    this.initialName,
    this.initialPhone,
    this.initialEmail,
    this.initialAddress,
    this.initialNotes,
  });

  @override
  State<AddClientScreen> createState() => _AddClientScreenState();
}

class _AddClientScreenState extends State<AddClientScreen>
    with ComprehensiveAnalyticsMixin {
  // Analytics mixin implementation
  @override
  String get screenName => 'add_client_screen';

  @override
  String get screenCategory => 'clients';

  @override
  String? get entryPoint => widget.initialName != null ? 'contact_import' : 'manual_entry';

  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _addressController = TextEditingController();
  final _notesController = TextEditingController();

  // Multiple phone numbers support (max 3)
  final List<TextEditingController> _phoneControllers = [];
  final List<String> _initialCountryCodes = [];
  int _phoneFieldsCount = 1; // Start with 1 phone field
  int _primaryPhoneIndex = 0; // Track which phone is primary
  static const int _maxPhoneFields = 3;

  bool _isLoading = false;
  String? _errorMessage;

  // Location selection
  LatLng? _selectedLocation;
  String? _selectedAddress;

  // SharedPreferences key for storing last selected country code
  static const String _lastCountryCodeKey = 'last_client_country_code';

  @override
  void initState() {
    super.initState();
    _nameController.text = widget.initialName ?? '';

    // Initialize first phone field
    _phoneControllers.add(TextEditingController());

    // Extract country code from phone number if provided
    if (widget.initialPhone != null && widget.initialPhone!.isNotEmpty) {
      _phoneControllers[0].text = widget.initialPhone!;
      _initialCountryCodes.add(_extractCountryCode(widget.initialPhone!));
    } else {
      // Load last used country code if no phone provided
      _loadLastCountryCode();
    }

    _emailController.text = widget.initialEmail ?? '';
    _addressController.text = widget.initialAddress ?? '';
    _notesController.text = widget.initialNotes ?? '';

    // Track form start
    trackFormInteraction(
      formName: 'add_client',
      action: 'start',
      context: {
        'has_initial_data': widget.initialName != null,
        'entry_method': widget.initialName != null ? 'contact_import' : 'manual',
      },
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    for (var controller in _phoneControllers) {
      controller.dispose();
    }
    _emailController.dispose();
    _addressController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  // Add a new phone field
  void _addPhoneField() {
    if (_phoneFieldsCount < _maxPhoneFields) {
      setState(() {
        _phoneControllers.add(TextEditingController());
        _initialCountryCodes.add(_initialCountryCodes.isNotEmpty ? _initialCountryCodes.last : 'RO');
        _phoneFieldsCount++;
      });
    }
  }

  // Remove a phone field
  void _removePhoneField(int index) {
    if (_phoneFieldsCount > 1 && index < _phoneControllers.length) {
      setState(() {
        // If removing the primary phone, set the first remaining as primary
        if (index == _primaryPhoneIndex) {
          _primaryPhoneIndex = 0;
        } else if (index < _primaryPhoneIndex) {
          _primaryPhoneIndex--;
        }

        _phoneControllers[index].dispose();
        _phoneControllers.removeAt(index);
        _initialCountryCodes.removeAt(index);
        _phoneFieldsCount--;
      });
    }
  }

  // Combine all phone numbers with primary first
  String _getCombinedPhoneNumbers() {
    final phones = <String>[];
    String? primaryPhone;

    print('DEBUG: _phoneControllers.length = ${_phoneControllers.length}');
    print('DEBUG: _primaryPhoneIndex = $_primaryPhoneIndex');

    // Collect all non-empty phone numbers and identify primary
    for (int i = 0; i < _phoneControllers.length; i++) {
      final phone = _phoneControllers[i].text.trim();
      print('DEBUG: Phone $i = "$phone"');
      if (phone.isNotEmpty) {
        if (i == _primaryPhoneIndex) {
          primaryPhone = phone;
          print('DEBUG: Set as primary: $phone');
        } else {
          phones.add(phone);
          print('DEBUG: Added to secondary: $phone');
        }
      }
    }

    // Put primary phone first if it exists
    if (primaryPhone != null) {
      phones.insert(0, primaryPhone);
    }

    final result = phones.join(',');
    print('DEBUG: Final result = "$result"');
    return result;
  }

  Future<void> _saveClient() async {
    final startTime = DateTime.now();

    // Track save button click
    trackButtonClick('save_client', context: {
      'fields_completed': _countCompletedFields(),
      'total_fields': 5,
      'has_initial_data': widget.initialName != null,
      'phone_count': _phoneFieldsCount,
    });

    if (!_formKey.currentState!.validate()) {
      // Track form validation failure
      trackFormInteraction(
        formName: 'add_client',
        action: 'validation_failed',
        fieldsCompleted: _countCompletedFields(),
        totalFields: 5,
        context: {
          'validation_errors': _getValidationErrors(),
        },
      );
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Create new client object
      final combinedPhones = _getCombinedPhoneNumbers();
      print('DEBUG: Combined phones before Client creation: "$combinedPhones"');

      final newClient = Client(
        id: '', // Will be generated by server
        name: _nameController.text.trim(),
        phone: combinedPhones, // Combine all phone numbers
        email: _emailController.text.trim(),
        address: _selectedAddress ?? _addressController.text.trim(),
        notes: _notesController.text.trim(),
        petIds: [], // Empty initially
        registrationDate: DateTime.now(),
      );

      print('DEBUG: Client JSON: ${newClient.toJson()}');

      // Send HTTP request to create client
      final response = await ClientService.createClient(newClient);

      if (response.success && response.data != null) {
        // Track successful client creation
        trackFormInteraction(
          formName: 'add_client',
          action: 'complete',
          fieldsCompleted: _countCompletedFields(),
          totalFields: 5,
          context: {
            'completion_time_ms': DateTime.now().difference(startTime).inMilliseconds,
            'client_id': response.data!.id,
            'phone_count': _phoneFieldsCount,
          },
        );

        // Success - ask if user wants to add a pet immediately
        if (mounted) {
          final shouldAddPet = await _showAddPetOption();
          if (shouldAddPet) {
            // Track navigation to add pet
            trackNavigationTo('add_pet_screen', method: 'post_client_creation');

            // Navigate to add pet for this client
            final result = await _navigateToAddPet(response.data!);
            Navigator.of(context).pop(response.data);
          } else {
            Navigator.of(context).pop(response.data);
          }
        }
      } else {
        // Track client creation failure
        trackError(
          errorType: 'client_creation_error',
          errorMessage: response.message ?? 'Unknown error',
          context: {
            'attempt_time_ms': DateTime.now().difference(startTime).inMilliseconds,
            'fields_completed': _countCompletedFields(),
          },
        );

        // Handle error
        setState(() {
          _errorMessage = response.message ?? context.tr('add_client.error_save');
        });
      }
    } catch (e) {
      // Track network/connection error
      trackError(
        errorType: 'network_error',
        errorMessage: e.toString(),
        context: {
          'attempt_time_ms': DateTime.now().difference(startTime).inMilliseconds,
          'fields_completed': _countCompletedFields(),
          'error_source': 'client_service',
        },
      );

      setState(() {
        _errorMessage = context.tr('add_client.error_connection', params: {'error': e.toString()});
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<bool> _showAddPetOption() async {
    return await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(context.tr('add_client.success_title')),
          content: Text(context.tr('add_client.success_message')),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(context.tr('add_client.later')),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(),
              child: Text(context.tr('add_client.add_pet'), style: TextStyle(color: Colors.white),),
            ),
          ],
        );
      },
    ) ?? false;
  }

  Future<void> _navigateToAddPet(Client client) async {
    await Navigator.of(context).push<Pet>(
      MaterialPageRoute(
        builder: (context) => AddPetScreen(
          clientId: client.id,
          clientName: client.name,
        ),
      ),
    );
  }

  String? _validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return context.tr('add_client.name_required');
    }
    if (value.trim().length < 2) {
      return context.tr('add_client.name_min_length');
    }
    return null;
  }

  String? _validatePhone(String? value, int index) {
    // First phone is required, others are optional
    if (index == 0 && (value == null || value.trim().isEmpty)) {
      return 'Telefonul este obligatoriu';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.tr('add_client.title')),
        elevation: 0,
        actions: [
          if (_isLoading)
             Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  color: Theme.of(context).colorScheme.onPrimary,
                  strokeWidth: 2,
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveClient,
              child: Text(
                context.tr('add_client.save'),
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      body: Container(
        child: Form(
          key: _formKey,
          child: ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              // Error message
              if (_errorMessage != null) ...[
                Container(
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.error.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Theme.of(context).colorScheme.error.withValues(alpha: 0.3)),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.error_outline, color: Theme.of(context).colorScheme.error),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: TextStyle(color: Theme.of(context).colorScheme.error),
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Client info card
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.person,
                            color: Theme.of(context).colorScheme.onSurface,
                            size: 24,
                          ),
                          SizedBox(width: 8),
                           Text(
                            context.tr('add_client.client_info'),
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 16),

                      // Name field
                      TextFormField(
                        controller: _nameController,
                        decoration: InputDecoration(
                          labelText: context.tr('add_client.name_label'),
                          hintText: context.tr('add_client.name_hint'),
                          prefixIcon: Icon(Icons.person_outline),
                          border: OutlineInputBorder(),
                        ),
                        textCapitalization: TextCapitalization.words,
                        validator: _validateName,
                        enabled: !_isLoading,
                      ),
                      SizedBox(height: 16),

                      // Phone fields - Multiple phone numbers support
                      if (_phoneFieldsCount > 1) ...[
                        Container(
                          padding: const EdgeInsets.all(12),
                          margin: const EdgeInsets.only(bottom: 12),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Theme.of(context).colorScheme.primary.withOpacity(0.3)),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.info_outline, color: Theme.of(context).colorScheme.primary, size: 20),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'Selectează numărul principal pentru SMS-urile automate',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Theme.of(context).colorScheme.primary,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],

                      ...List.generate(_phoneFieldsCount, (index) {
                        return Padding(
                          padding: EdgeInsets.only(bottom: 12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (_phoneFieldsCount > 1)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 18, right: 8),
                                      child: Radio<int>(
                                        value: index,
                                        groupValue: _primaryPhoneIndex,
                                        onChanged: _isLoading ? null : (int? value) {
                                          if (value != null) {
                                            setState(() {
                                              _primaryPhoneIndex = value;
                                            });
                                          }
                                        },
                                        activeColor: Theme.of(context).colorScheme.primary,
                                      ),
                                    ),
                                  Expanded(
                                    child: IntlPhoneField(
                                      decoration: InputDecoration(
                                        labelText: index == 0
                                            ? 'Număr de telefon *'
                                            : 'Număr de telefon ${index + 1} (opțional)',
                                        hintText: '+40 XXX XXX XXX',
                                        prefixIcon: Icon(Icons.phone_outlined),
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(8),
                                        ),
                                        contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                                        helperText: index == _primaryPhoneIndex && _phoneFieldsCount > 1
                                            ? '📱 Principal - pentru SMS automate'
                                            : (index == 0 ? 'Format: +40 XXX XXX XXX' : null),
                                        helperStyle: TextStyle(
                                          color: index == _primaryPhoneIndex && _phoneFieldsCount > 1
                                              ? Theme.of(context).colorScheme.primary
                                              : null,
                                          fontWeight: index == _primaryPhoneIndex && _phoneFieldsCount > 1
                                              ? FontWeight.w600
                                              : null,
                                        ),
                                      ),
                                      initialCountryCode: _initialCountryCodes.length > index
                                          ? _initialCountryCodes[index]
                                          : 'RO',
                                      dropdownIconPosition: IconPosition.trailing,
                                      flagsButtonPadding: const EdgeInsets.symmetric(horizontal: 8.0),
                                      dropdownTextStyle: TextStyle(
                                        color: Theme.of(context).colorScheme.onSurface,
                                      ),
                                      showDropdownIcon: true,
                                      disableLengthCheck: false,
                                      keyboardType: TextInputType.phone,
                                      enabled: !_isLoading,
                                      onChanged: (phone) {
                                        setState(() {
                                          _phoneControllers[index].text = phone.completeNumber;
                                        });
                                        // Save the selected country code for next time
                                        if (index == 0) {
                                          _saveLastCountryCode(phone.countryISOCode);
                                        }
                                      },
                                      validator: (value) {
                                        if (index == 0 && (value == null || value.number.isEmpty)) {
                                          return 'Numărul de telefon este obligatoriu';
                                        }
                                        // Basic validation - accept numbers with at least 6 digits
                                        if (value != null && value.number.isNotEmpty && value.number.length < 6) {
                                          return 'Numărul de telefon nu este valid';
                                        }
                                        return null;
                                      },
                                      initialValue: _phoneControllers[index].text.isNotEmpty
                                          ? _phoneControllers[index].text.replaceAll(RegExp(r'[^\d]'), '')
                                          : null,
                                    ),
                                  ),
                                  if (index > 0)
                                    Padding(
                                      padding: const EdgeInsets.only(left: 8, top: 8),
                                      child: IconButton(
                                        icon: Icon(Icons.remove_circle, color: Theme.of(context).colorScheme.error),
                                        onPressed: _isLoading ? null : () => _removePhoneField(index),
                                        tooltip: 'Șterge numărul de telefon',
                                      ),
                                    ),
                                ],
                              ),
                            ],
                          ),
                        );
                      }),

                      // Add phone button
                      if (_phoneFieldsCount < _maxPhoneFields)
                        OutlinedButton.icon(
                          onPressed: _isLoading ? null : _addPhoneField,
                          icon: Icon(Icons.add),
                          label: Text('Adaugă număr de telefon (${_phoneFieldsCount}/$_maxPhoneFields)'),
                          style: OutlinedButton.styleFrom(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),

                      if (_phoneFieldsCount < _maxPhoneFields)
                        SizedBox(height: 16),

                      // Email field (optional)
                      TextFormField(
                        controller: _emailController,
                        decoration: InputDecoration(
                          labelText: context.tr('add_client.email_label'),
                          hintText: context.tr('add_client.email_hint'),
                          prefixIcon: Icon(Icons.email_outlined),
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.emailAddress,
                        enabled: !_isLoading,
                      ),
                      SizedBox(height: 16),

                      // Address field (optional) - using LocationSelectionButton
                      LocationSelectionButton(
                        selectedAddress: _selectedAddress,
                        selectedLocation: _selectedLocation,
                        label: context.tr('add_client.address_label'),
                        hint: context.tr('add_client.address_hint'),
                        isRequired: false,
                        onLocationSelected: (location, address) {
                          setState(() {
                            _selectedLocation = location;
                            _selectedAddress = address;
                            if (address != null && address.isNotEmpty) {
                              _addressController.text = address;
                            }
                          });
                        },
                      ),
                      SizedBox(height: 16),

                      // Notes field
                      TextFormField(
                        controller: _notesController,
                        decoration: InputDecoration(
                          labelText: context.tr('add_client.notes_label'),
                          hintText: context.tr('add_client.notes_hint'),
                          prefixIcon: Icon(Icons.note_outlined),
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 3,
                        enabled: !_isLoading,
                        textCapitalization: TextCapitalization.sentences,
                      ),
                    ],
                  ),
                ),
              ),

              SizedBox(height: 24),

              // Save button
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton.icon(
                  onPressed: _isLoading ? null : _saveClient,
                  icon: _isLoading
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: Theme.of(context).colorScheme.onPrimary,
                            strokeWidth: 2,
                          ),
                        )
                      :  Icon(Icons.save, color: Colors.white,),
                  label: Text(_isLoading ? context.tr('add_client.saving') : context.tr('add_client.save_button'), style: TextStyle(color: Colors.white),),
                  style: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),

              SizedBox(height: 16),

              // Info text
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Theme.of(context).colorScheme.onSurface,
                      size: 20,
                    ),
                    SizedBox(width: 8),
                     Expanded(
                      child: Text(
                        context.tr('add_client.info_text'),
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Extract country code from international phone number
  /// Returns ISO country code (e.g., 'RO', 'MD', 'US', 'GB')
  String _extractCountryCode(String phoneNumber) {
    final cleanPhone = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

    // Map of country dial codes to ISO codes
    final countryCodeMap = {
      '+1': 'US',      // USA/Canada
      '+7': 'RU',      // Russia
      '+33': 'FR',     // France
      '+34': 'ES',     // Spain
      '+39': 'IT',     // Italy
      '+40': 'RO',     // Romania
      '+41': 'CH',     // Switzerland
      '+43': 'AT',     // Austria
      '+44': 'GB',     // United Kingdom
      '+45': 'DK',     // Denmark
      '+46': 'SE',     // Sweden
      '+47': 'NO',     // Norway
      '+48': 'PL',     // Poland
      '+49': 'DE',     // Germany
      '+90': 'TR',     // Turkey
      '+91': 'IN',     // India
      '+212': 'MA',    // Morocco
      '+213': 'DZ',    // Algeria
      '+351': 'PT',    // Portugal
      '+352': 'LU',    // Luxembourg
      '+353': 'IE',    // Ireland
      '+354': 'IS',    // Iceland
      '+355': 'AL',    // Albania
      '+356': 'MT',    // Malta
      '+357': 'CY',    // Cyprus
      '+358': 'FI',    // Finland
      '+359': 'BG',    // Bulgaria
      '+370': 'LT',    // Lithuania
      '+371': 'LV',    // Latvia
      '+372': 'EE',    // Estonia
      '+373': 'MD',    // Moldova
      '+374': 'AM',    // Armenia
      '+375': 'BY',    // Belarus
      '+376': 'AD',    // Andorra
      '+377': 'MC',    // Monaco
      '+378': 'SM',    // San Marino
      '+380': 'UA',    // Ukraine
      '+381': 'RS',    // Serbia
      '+382': 'ME',    // Montenegro
      '+383': 'XK',    // Kosovo
      '+385': 'HR',    // Croatia
      '+386': 'SI',    // Slovenia
      '+387': 'BA',    // Bosnia and Herzegovina
      '+389': 'MK',    // North Macedonia
    };

    // Try to match country codes from longest to shortest
    final sortedCodes = countryCodeMap.keys.toList()
      ..sort((a, b) => b.length.compareTo(a.length));

    for (final code in sortedCodes) {
      if (cleanPhone.startsWith(code)) {
        return countryCodeMap[code]!;
      }
    }

    // Default to Romania if no match found
    return 'RO';
  }

  /// Load the last used country code from SharedPreferences
  Future<void> _loadLastCountryCode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastCountryCode = prefs.getString(_lastCountryCodeKey);
      if (lastCountryCode != null && mounted) {
        setState(() {
          _initialCountryCodes.add(lastCountryCode);
        });
      }
    } catch (e) {
      // If loading fails, keep default 'RO'
      print('Error loading last country code: $e');
    }
  }

  /// Save the selected country code to SharedPreferences
  Future<void> _saveLastCountryCode(String countryCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastCountryCodeKey, countryCode);
    } catch (e) {
      // Silently fail if saving doesn't work
      print('Error saving last country code: $e');
    }
  }

  /// Helper method to count completed fields for analytics
  int _countCompletedFields() {
    int count = 0;
    if (_nameController.text.trim().isNotEmpty) count++;
    if (_emailController.text.trim().isNotEmpty) count++;
    if (_addressController.text.trim().isNotEmpty) count++;
    if (_notesController.text.trim().isNotEmpty) count++;
    count += _phoneControllers.where((controller) => controller.text.trim().isNotEmpty).length;
    return count;
  }

  /// Helper method to get validation errors for analytics
  List<String> _getValidationErrors() {
    List<String> errors = [];
    if (_nameController.text.trim().isEmpty) errors.add('name');
    // Phone validation - only first is required
    if (_phoneControllers.isNotEmpty && _phoneControllers[0].text.trim().isEmpty) errors.add('phone');
    return errors;
  }
}
