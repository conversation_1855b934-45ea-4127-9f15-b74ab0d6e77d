import 'package:animaliaproject/utils/snack_bar_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../config/theme/app_theme.dart';
import '../../l10n/app_localizations.dart';
import '../../models/client.dart';
import '../../services/client/client_service.dart';
import '../../utils/formatters/phone_number_utils.dart';
import '../../widgets/address_selection/location_selection_button.dart';

class EditClientScreen extends StatefulWidget {
  final Client client;

  const EditClientScreen({
    super.key,
    required this.client,
  });

  @override
  State<EditClientScreen> createState() => _EditClientScreenState();
}

class _EditClientScreenState extends State<EditClientScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _addressController = TextEditingController();
  final _notesController = TextEditingController();

  // Multiple phone numbers support (max 2)
  final List<TextEditingController> _phoneControllers = [];
  final List<String> _initialCountryCodes = [];
  int _phoneFieldsCount = 1; // Start with 1 phone field
  int _primaryPhoneIndex = 0; // Track which phone is primary
  static const int _maxPhoneFields = 3;

  bool _isLoading = false;
  String? _errorMessage;

  // Location selection variables
  LatLng? _selectedLocation;
  String? _selectedAddress;

  // SharedPreferences key for storing last selected country code
  static const String _lastCountryCodeKey = 'last_client_country_code';

  @override
  void initState() {
    super.initState();
    // Pre-populate form with existing client data
    _nameController.text = widget.client.name;

    // Parse phone numbers from client data (primary + secondary)
    final phones = <String>[];
    if (widget.client.phone.isNotEmpty) {
      phones.add(widget.client.phone);
    }
    if (widget.client.secondaryPhone != null && widget.client.secondaryPhone!.isNotEmpty) {
      // Parse comma-separated secondary phone numbers
      final secondaryPhones = widget.client.secondaryPhone!.split(',').map((p) => p.trim()).where((p) => p.isNotEmpty);
      phones.addAll(secondaryPhones);
    }

    _phoneFieldsCount = phones.isNotEmpty ? phones.length : 1;

    for (var i = 0; i < _phoneFieldsCount; i++) {
      _phoneControllers.add(TextEditingController());
      if (i < phones.length) {
        _phoneControllers[i].text = phones[i];
        _initialCountryCodes.add(_extractCountryCode(phones[i]));
      } else {
        _initialCountryCodes.add('RO');
      }
    }

    // If no phones, initialize with one empty field
    if (phones.isEmpty) {
      _phoneControllers.add(TextEditingController());
      _initialCountryCodes.add('RO');
    }

    _emailController.text = widget.client.email;
    _addressController.text = widget.client.address;
    _notesController.text = widget.client.notes;

    // Initialize address selection if client has an address
    if (widget.client.address.isNotEmpty) {
      _selectedAddress = widget.client.address;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    for (var controller in _phoneControllers) {
      controller.dispose();
    }
    _emailController.dispose();
    _addressController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  // Add a new phone field
  void _addPhoneField() {
    if (_phoneFieldsCount < _maxPhoneFields) {
      setState(() {
        _phoneControllers.add(TextEditingController());
        _initialCountryCodes.add(_initialCountryCodes.isNotEmpty ? _initialCountryCodes.last : 'RO');
        _phoneFieldsCount++;
      });
    }
  }

  // Remove a phone field
  void _removePhoneField(int index) {
    if (_phoneFieldsCount > 1 && index < _phoneControllers.length) {
      setState(() {
        // If removing the primary phone, set the first remaining as primary
        if (index == _primaryPhoneIndex) {
          _primaryPhoneIndex = 0;
        } else if (index < _primaryPhoneIndex) {
          _primaryPhoneIndex--;
        }

        _phoneControllers[index].dispose();
        _phoneControllers.removeAt(index);
        _initialCountryCodes.removeAt(index);
        _phoneFieldsCount--;
      });
    }
  }

  // Combine all phone numbers with primary first
  String _getCombinedPhoneNumbers() {
    final phones = <String>[];

    // Collect all non-empty phone numbers
    for (int i = 0; i < _phoneControllers.length; i++) {
      final phone = _phoneControllers[i].text.trim();
      if (phone.isNotEmpty) {
        phones.add(phone);
      }
    }

    // Reorder to put primary phone first
    if (_primaryPhoneIndex > 0 && _primaryPhoneIndex < phones.length) {
      final primaryPhone = phones.removeAt(_primaryPhoneIndex);
      phones.insert(0, primaryPhone);
    }

    return phones.join(',');
  }

  Future<void> _updateClient() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Get phone numbers with primary phone first
      final phoneNumbers = <String>[];
      for (int i = 0; i < _phoneControllers.length; i++) {
        final phone = _phoneControllers[i].text.trim();
        if (phone.isNotEmpty) {
          phoneNumbers.add(phone);
        }
      }

      // Reorder to put primary phone first
      String primaryPhone = '';
      String? secondaryPhone;

      if (phoneNumbers.isNotEmpty) {
        if (_primaryPhoneIndex < phoneNumbers.length) {
          primaryPhone = phoneNumbers[_primaryPhoneIndex];
          // Get all other phones as secondary (comma-separated)
          final otherPhones = List<String>.from(phoneNumbers);
          otherPhones.removeAt(_primaryPhoneIndex);
          secondaryPhone = otherPhones.isNotEmpty ? otherPhones.join(',') : null;
        } else {
          primaryPhone = phoneNumbers.first;
          final remainingPhones = phoneNumbers.skip(1).toList();
          secondaryPhone = remainingPhones.isNotEmpty ? remainingPhones.join(',') : null;
        }
      }

      // Create updated client object using copyWith
      final updatedClient = widget.client.copyWith(
        name: _nameController.text.trim(),
        phone: primaryPhone,
        secondaryPhone: secondaryPhone,
        email: _emailController.text.trim(),
        address: _addressController.text.trim(),
        notes: _notesController.text.trim(),
        updatedAt: DateTime.now(),
      );

      // Send HTTP request to update client
      final response = await ClientService.updateClient(widget.client.id, updatedClient);

      if (response.success && response.data != null) {
        // Success - return the updated client
        if (mounted) {
          Navigator.of(context).pop(response.data);
          
          showTopSnackBar(context, 
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(child: Text(context.tr('edit_client.success_message'))),
                ],
              ),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
          );
        }
      } else {
        // Handle error
        setState(() {
          _errorMessage = response.message ?? context.tr('edit_client.error_update');
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = context.tr('edit_client.error_connection', params: {'error': e.toString()});
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String? _validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return context.tr('edit_client.name_required');
    }
    if (value.trim().length < 2) {
      return context.tr('edit_client.name_min_length');
    }
    if (value.trim().length > 255) {
      return context.tr('edit_client.name_max_length');
    }
    return null;
  }

  String? _validatePhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Phone is optional
    }
    if (value.trim().length > 50) {
      return context.tr('edit_client.phone_max_length');
    }
    if (!PhoneNumberUtils.isValidRomanianMobile(value)) {
      return context.tr('edit_client.phone_invalid');
    }

    return null;
}

  String? _validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Email is optional
    }
    if (value.trim().length > 255) {
      return context.tr('edit_client.email_max_length');
    }
    final emailRegex = RegExp(r'^[^\s@]+@[^\s@]+\.[^\s@]+$');
    if (!emailRegex.hasMatch(value.trim())) {
      return context.tr('edit_client.email_invalid');
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.tr('edit_client.title', params: {'name': widget.client.name})),
        elevation: 0,
        actions: [
          if (_isLoading)
             Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  color: Theme.of(context).colorScheme.onPrimary,
                  strokeWidth: 2,
                ),
              ),
            )
          else
            TextButton(
              onPressed: _updateClient,
              child: Text(
                context.tr('edit_client.save'),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      body: Container(
        color: Theme.of(context).colorScheme.surface,
        child: Form(
          key: _formKey,
          child: ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              // Error message
              if (_errorMessage != null) ...[
                Container(
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: AppTheme.getStatusColor(context, 'error').withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AppTheme.getStatusColor(context, 'error').withValues(alpha: 0.3)),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.error_outline, color: AppTheme.getStatusColor(context, 'error')),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: TextStyle(color: AppTheme.getStatusColor(context, 'error')),
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Client info header
              Container(
                padding: const EdgeInsets.all(16),
                margin: const EdgeInsets.only(bottom: 24),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.onSurface,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.person,
                        color: Theme.of(context).colorScheme.onPrimary,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            context.tr('edit_client.edit_info_title'),
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            context.tr('edit_client.registered_on', params: {
                              'date': '${widget.client.registrationDate.day}/${widget.client.registrationDate.month}/${widget.client.registrationDate.year}'
                            }),
                            style: TextStyle(
                              fontSize: 12,
                              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Basic Information Card
              _buildBasicInfoCard(),
              const SizedBox(height: 16),

              // Contact Information Card
              _buildContactInfoCard(),
              const SizedBox(height: 16),

              // Additional Information Card
              _buildAdditionalInfoCard(),
              const SizedBox(height: 24),

              // Save button
              _buildSaveButton(),
              const SizedBox(height: 16),

              // Info text
              _buildInfoText(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.person_outline,
                  color: Theme.of(context).colorScheme.onSurface,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  context.tr('edit_client.basic_info_title'),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Name field
            TextFormField(
              controller: _nameController,
              decoration: InputDecoration(
                labelText: context.tr('edit_client.name_label'),
                hintText: context.tr('edit_client.name_hint'),
                prefixIcon: Icon(Icons.person),
                border: OutlineInputBorder(),
              ),
              textCapitalization: TextCapitalization.words,
              validator: _validateName,
              enabled: !_isLoading,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.contact_phone,
                  color: Theme.of(context).colorScheme.onSurface,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  context.tr('edit_client.contact_info_title'),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Phone fields - Multiple phone numbers support
            if (_phoneFieldsCount > 1) ...[
              Container(
                padding: const EdgeInsets.all(12),
                margin: const EdgeInsets.only(bottom: 12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Theme.of(context).colorScheme.primary.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Theme.of(context).colorScheme.primary, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Selectează numărul principal pentru SMS-urile automate',
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],

            ...List.generate(_phoneFieldsCount, (index) {
              return Padding(
                padding: EdgeInsets.only(bottom: index < _phoneFieldsCount - 1 ? 12 : 16),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (_phoneFieldsCount > 1)
                      Padding(
                        padding: const EdgeInsets.only(top: 18, right: 8),
                        child: Radio<int>(
                          value: index,
                          groupValue: _primaryPhoneIndex,
                          onChanged: _isLoading ? null : (int? value) {
                            if (value != null) {
                              setState(() {
                                _primaryPhoneIndex = value;
                              });
                            }
                          },
                          activeColor: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    Expanded(
                      child: IntlPhoneField(
                        decoration: InputDecoration(
                          labelText: index == 0
                              ? context.tr('edit_client.phone_label')
                              : 'Număr de telefon ${index + 1} (opțional)',
                          hintText: context.tr('edit_client.phone_hint'),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          helperText: index == _primaryPhoneIndex && _phoneFieldsCount > 1
                              ? '📱 Principal - pentru SMS automate'
                              : (index == 0 ? context.tr('edit_client.phone_helper') : null),
                          helperStyle: TextStyle(
                            color: index == _primaryPhoneIndex && _phoneFieldsCount > 1
                                ? Theme.of(context).colorScheme.primary
                                : null,
                            fontWeight: index == _primaryPhoneIndex && _phoneFieldsCount > 1
                                ? FontWeight.w600
                                : null,
                          ),
                          prefixIcon: const Icon(Icons.phone),
                          contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                        ),
                        initialCountryCode: _initialCountryCodes.length > index
                            ? _initialCountryCodes[index]
                            : 'RO',
                        dropdownIconPosition: IconPosition.trailing,
                        flagsButtonPadding: const EdgeInsets.symmetric(horizontal: 8.0),
                        dropdownTextStyle: TextStyle(
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        showDropdownIcon: true,
                        disableLengthCheck: false,
                        keyboardType: TextInputType.phone,
                        enabled: !_isLoading,
                        onChanged: (phone) {
                          setState(() {
                            _phoneControllers[index].text = phone.completeNumber;
                          });
                          // Save the selected country code for next time
                          if (index == 0) {
                            _saveLastCountryCode(phone.countryISOCode);
                          }
                        },
                        validator: (value) {
                          if (value != null && value.number.isNotEmpty && value.number.length < 6) {
                            return 'Numărul de telefon nu este valid';
                          }
                          return null;
                        },
                        initialValue: _phoneControllers[index].text.isNotEmpty
                            ? _phoneControllers[index].text.replaceAll(RegExp(r'[^\d]'), '')
                            : null,
                      ),
                    ),
                    if (index > 0)
                      Padding(
                        padding: const EdgeInsets.only(left: 8, top: 8),
                        child: IconButton(
                          icon: Icon(Icons.remove_circle, color: Theme.of(context).colorScheme.error),
                          onPressed: _isLoading ? null : () => _removePhoneField(index),
                          tooltip: 'Șterge numărul de telefon',
                        ),
                      ),
                  ],
                ),
              );
            }),

            // Add phone button
            if (_phoneFieldsCount < _maxPhoneFields)
              OutlinedButton.icon(
                onPressed: _isLoading ? null : _addPhoneField,
                icon: Icon(Icons.add),
                label: Text('Adaugă număr de telefon (${_phoneFieldsCount}/$_maxPhoneFields)'),
                style: OutlinedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),

            if (_phoneFieldsCount < _maxPhoneFields)
              const SizedBox(height: 16),

            // Email field
            TextFormField(
              controller: _emailController,
              decoration: InputDecoration(
                labelText: context.tr('edit_client.email_label'),
                hintText: context.tr('edit_client.email_hint'),
                prefixIcon: Icon(Icons.email),
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
              validator: _validateEmail,
              enabled: !_isLoading,
            ),
            const SizedBox(height: 16),

            // Address field - using LocationSelectionButton
            LocationSelectionButton(
              selectedAddress: _selectedAddress,
              selectedLocation: _selectedLocation,
              label: context.tr('edit_client.address_label'),
              hint: context.tr('edit_client.address_hint'),
              isRequired: false,
              onLocationSelected: (location, address) {
                setState(() {
                  _selectedLocation = location;
                  _selectedAddress = address;
                  if (address != null && address.isNotEmpty) {
                    _addressController.text = address;
                  }
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.note_outlined,
                  color: Theme.of(context).colorScheme.onSurface,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  context.tr('edit_client.additional_info_title'),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Notes field
            TextFormField(
              controller: _notesController,
              decoration: InputDecoration(
                labelText: context.tr('edit_client.notes_label'),
                hintText: context.tr('edit_client.notes_hint'),
                prefixIcon: Icon(Icons.note_outlined),
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              enabled: !_isLoading,
              textCapitalization: TextCapitalization.sentences,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton.icon(
        onPressed: _isLoading ? null : _updateClient,
        icon: _isLoading
            ?  SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  color: Theme.of(context).colorScheme.onPrimary,
                  strokeWidth: 2,
                ),
              )
            : const Icon(Icons.save),
        label: Text(_isLoading ? context.tr('edit_client.saving') : context.tr('edit_client.update_button')),
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoText() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: Theme.of(context).colorScheme.onSurface,
            size: 20,
          ),
          const SizedBox(width: 8),
           Expanded(
            child: Text(
              context.tr('edit_client.info_text'),
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Extract country code from international phone number
  /// Returns ISO country code (e.g., 'RO', 'MD', 'US', 'GB')
  String _extractCountryCode(String phoneNumber) {
    final cleanPhone = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

    // Map of country dial codes to ISO codes
    final countryCodeMap = {
      '+1': 'US',      // USA/Canada
      '+7': 'RU',      // Russia
      '+33': 'FR',     // France
      '+34': 'ES',     // Spain
      '+39': 'IT',     // Italy
      '+40': 'RO',     // Romania
      '+41': 'CH',     // Switzerland
      '+43': 'AT',     // Austria
      '+44': 'GB',     // United Kingdom
      '+45': 'DK',     // Denmark
      '+46': 'SE',     // Sweden
      '+47': 'NO',     // Norway
      '+48': 'PL',     // Poland
      '+49': 'DE',     // Germany
      '+90': 'TR',     // Turkey
      '+91': 'IN',     // India
      '+212': 'MA',    // Morocco
      '+213': 'DZ',    // Algeria
      '+351': 'PT',    // Portugal
      '+352': 'LU',    // Luxembourg
      '+353': 'IE',    // Ireland
      '+354': 'IS',    // Iceland
      '+355': 'AL',    // Albania
      '+356': 'MT',    // Malta
      '+357': 'CY',    // Cyprus
      '+358': 'FI',    // Finland
      '+359': 'BG',    // Bulgaria
      '+370': 'LT',    // Lithuania
      '+371': 'LV',    // Latvia
      '+372': 'EE',    // Estonia
      '+373': 'MD',    // Moldova
      '+374': 'AM',    // Armenia
      '+375': 'BY',    // Belarus
      '+376': 'AD',    // Andorra
      '+377': 'MC',    // Monaco
      '+378': 'SM',    // San Marino
      '+380': 'UA',    // Ukraine
      '+381': 'RS',    // Serbia
      '+382': 'ME',    // Montenegro
      '+383': 'XK',    // Kosovo
      '+385': 'HR',    // Croatia
      '+386': 'SI',    // Slovenia
      '+387': 'BA',    // Bosnia and Herzegovina
      '+389': 'MK',    // North Macedonia
    };

    // Try to match country codes from longest to shortest
    final sortedCodes = countryCodeMap.keys.toList()
      ..sort((a, b) => b.length.compareTo(a.length));

    for (final code in sortedCodes) {
      if (cleanPhone.startsWith(code)) {
        return countryCodeMap[code]!;
      }
    }

    // Default to Romania if no match found
    return 'RO';
  }

  /// Save the selected country code to SharedPreferences
  Future<void> _saveLastCountryCode(String countryCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastCountryCodeKey, countryCode);
    } catch (e) {
      // Silently fail if saving doesn't work
      print('Error saving last country code: $e');
    }
  }
}
