import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../models/client.dart';
import '../../l10n/app_localizations.dart';
import '../../providers/client_provider.dart';

class ClientSearchScreen extends StatefulWidget {
  final List<Client> availableClients;

  const ClientSearchScreen({
    super.key,
    required this.availableClients,
  });

  @override
  State<ClientSearchScreen> createState() => _ClientSearchScreenState();
}

class _ClientSearchScreenState extends State<ClientSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);

    // Initialize with empty search to show initial clients
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final clientProvider = context.read<ClientProvider>();
      clientProvider.searchClients('');
    });
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.toLowerCase();
    });

    // Use backend search through ClientProvider
    final clientProvider = context.read<ClientProvider>();
    clientProvider.searchClients(_searchController.text);
  }

  void _selectClient(Client client) {
    Navigator.of(context).pop(client);
  }

  void _addNewClient() {
    Navigator.of(context).pop('new_client');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.tr('client_search.title')),
      ),
      body: Consumer<ClientProvider>(
        builder: (context, clientProvider, child) {
          final filteredClients = clientProvider.filteredClients;
          final isSearching = clientProvider.isSearching;
          final totalCount = clientProvider.totalClientCount;

          return Column(
            children: [
              // Search field
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: TextField(
                  controller: _searchController,
                  textCapitalization: TextCapitalization.sentences,
                  decoration: InputDecoration(
                    labelText: context.tr('client_search.search_label'),
                    hintText: context.tr('client_search.search_placeholder'),
                    prefixIcon: isSearching
                        ? Padding(
                            padding: const EdgeInsets.all(12.0),
                            child: SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                            ),
                          )
                        : Icon(Icons.search, color: Theme.of(context).colorScheme.onSurface),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                            },
                          )
                        : null,
                    border: const OutlineInputBorder(),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Theme.of(context).colorScheme.onSurface),
                    ),
                  ),
                ),
              ),

              // Add new client button
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _addNewClient,
                    icon: Icon(Icons.person_add, color: Colors.white),
                    label: Text(
                      context.tr('client_search.add_new_client'),
                      style: TextStyle(color: Colors.white),
                    ),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ),

              SizedBox(height: 16),

              // Results header
              if (filteredClients.isNotEmpty || isSearching)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Row(
                    children: [
                      Text(
                        _searchQuery.isEmpty
                            ? context.tr('client_search.clients_found', params: {'count': totalCount.toString()})
                            : context.tr('client_search.clients_found', params: {'count': filteredClients.length.toString()}),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      if (totalCount > filteredClients.length && _searchQuery.isEmpty)
                        Padding(
                          padding: const EdgeInsets.only(left: 8.0),
                          child: Text(
                            '(afișând primii ${filteredClients.length})',
                            style: TextStyle(
                              fontSize: 14,
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

              SizedBox(height: 8),

              // Clients list
              Expanded(
                child: _buildClientsList(filteredClients, isSearching),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildClientsList(List<Client> filteredClients, bool isSearching) {
    if (isSearching && filteredClients.isEmpty) {
      return Center(
        child: CircularProgressIndicator(),
      );
    }

    if (filteredClients.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            SizedBox(height: 16),
            Text(
              _searchQuery.isEmpty 
                  ? context.tr('client_search.no_clients_available')
                  : context.tr('client_search.no_results_for_query', params: {'query': _searchQuery}),
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: filteredClients.length,
      itemBuilder: (context, index) {
        final client = filteredClients[index];
        final queryLower = _searchQuery.trim();
        final backendMatches = client.matchingPetNames
            .where((petName) => queryLower.isEmpty || petName.toLowerCase().contains(queryLower))
            .toList();
        final localMatches = client.petNames
            .where((petName) => queryLower.isNotEmpty && petName.toLowerCase().contains(queryLower))
            .toList();
        final combinedMatches = {
          ...backendMatches,
          ...localMatches,
        }.toList();
        final shouldShowPetMatches = combinedMatches.isNotEmpty && (queryLower.isNotEmpty || client.matchedViaPetName);

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Theme.of(context).colorScheme.primary,
              child: Text(
                client.name.isNotEmpty ? client.name[0].toUpperCase() : '?',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            title: RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: client.name,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  if (client.pets.isNotEmpty)
                    TextSpan(
                      text: ' (${client.pets.first.name} - ${client.pets.first.species != null ? _getSpeciesDisplayName(client.pets.first.species!) : 'necunoscut'})',
                      style: TextStyle(
                        fontWeight: FontWeight.normal,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    )
                  else if (client.petNames.isNotEmpty)
                    TextSpan(
                      text: ' (${client.petNames.join(', ')})',
                      style: TextStyle(
                        fontWeight: FontWeight.normal,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                ],
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Primary phone number
                if (client.phone.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 2),
                    child: Row(
                      children: [
                        Icon(
                          Icons.phone,
                          size: 16,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                        SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            client.phone,
                            style: TextStyle(
                              fontSize: 14,
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                // Secondary phone number
                if (client.secondaryPhone != null && client.secondaryPhone!.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 2),
                    child: Row(
                      children: [
                        Icon(
                          Icons.phone_outlined,
                          size: 16,
                          color: Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.7),
                        ),
                        SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            client.secondaryPhone!,
                            style: TextStyle(
                              fontSize: 14,
                              color: Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.7),
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                if (client.email.isNotEmpty)
                  Text(
                    client.email,
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                if (shouldShowPetMatches)
                  Padding(
                    padding: const EdgeInsets.only(top: 4.0),
                    child: Text(
                      context.tr('client_search.pet_match', params: {'pets': combinedMatches.join(', ')}),
                      style: TextStyle(
                        fontSize: 12,
                        fontStyle: FontStyle.italic,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ),
              ],
            ),
            trailing: Icon(
              Icons.arrow_forward_ios,
              color: Theme.of(context).colorScheme.onSurface,
              size: 16,
            ),
            onTap: () => _selectClient(client),
          ),
        );
      },
    );
  }

  String _getSpeciesDisplayName(String species) {
    switch (species.toLowerCase()) {
      case 'dog':
        return 'câine';
      case 'cat':
        return 'pisică';
      case 'other':
        return 'altele';
      default:
        return species;
    }
  }
}
