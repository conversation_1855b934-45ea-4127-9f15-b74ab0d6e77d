import 'dart:async';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';

import '../config/api_config.dart';
import '../config/theme/app_theme.dart';
import '../models/appointment.dart';
import '../models/block_time_result.dart';
import '../models/client.dart';
import '../models/google_calendar_event.dart';
import '../models/pet.dart';
import '../models/service.dart';
import '../models/staff_working_hours_settings.dart';
import '../models/working_hours_settings.dart';
import '../services/appointment/calendar_service.dart';
import '../services/auth/auth_service.dart';
import '../services/breed_service.dart';
import '../services/calendar_preferences_service.dart';
import '../services/client/client_service.dart';
import '../services/google_calendar_sync_service.dart';
import '../services/service_management_service.dart';
import '../services/sms_quota_service.dart';
import '../services/staff_service.dart';
import '../services/staff_working_hours_service.dart';
import '../services/working_hours_service.dart';
import '../utils/romanian_holidays.dart';

class CalendarProvider extends ChangeNotifier {
  final CalendarService _calendarService = CalendarService();

  // Calendar view settings
  bool _showCanceledAppointments = false;
  bool _showUnpaidAppointments = false;
  bool _showRevenueSummary = true;
  CalendarHourViewMode _hourViewMode = CalendarHourViewMode.businessHours;
  CalendarTimeFormat _timeFormat = CalendarTimeFormat.twelveHour;
  bool _googleCalendarSyncEnabled = false;
  bool _showGoogleCalendarEvents = true; // Show imported Google Calendar events by default

  // Dynamic time range settings
  bool _useDynamicTimeRange = true; // Enable dynamic time range based on working hours
  int _timeRangePaddingHours = 1; // Hours to add before/after calculated range
  int _minimumTimeRangeHours = 8; // Minimum hours to show (e.g., 8 hours minimum)

  // Zoom and accessibility settings
  double _timeSlotHeight = 80.0; // Increased default height for better visibility
  static const double _minTimeSlotHeight = 40.0;
  static const double _maxTimeSlotHeight = 120.0;

  // Unavailable slot opacity setting
  double _unavailableSlotOpacity = 0.3; // Increased default opacity for unavailable slots
  static const double _minUnavailableSlotOpacity = 0.0; // Updated minimum range to 0.1
  static const double _maxUnavailableSlotOpacity = 1.0; // Expanded maximum range

  // Unavailable slot color setting
  UnavailableSlotColor _unavailableSlotColor = UnavailableSlotColor.gray;

  // Responsive slot height settings
  static const double _mobileSlotHeight = 80.0;
  static const double _desktopSlotHeight = 100.0; // Larger slots for desktop

  // Search and filter settings
  String _searchQuery = '';
  String _selectedServiceFilter = '';
  String _selectedStatusFilter = '';

  // Feature 4: Real-time color update timer
  Timer? _colorUpdateTimer;

  // Staff management
  List<StaffResponse> _availableStaff = [];
  List<String> _selectedStaff = []; // IDs of selected staff members
  bool _isLoadingStaff = false;
  String? _staffError;

  // Staff selection per salon to maintain independent settings
  final Map<String, List<String>> _staffSelectionPerSalon = {};

  // Appointments data
  List<Appointment> _appointments = [];
  Map<String, List<Appointment>> _appointmentsByDate = {}; // Cache appointments by date
  bool _isLoadingAppointments = false;
  String? _appointmentsError;

  // Highlighted appointment ID for visual feedback after creation
  String? _highlightedAppointmentId;
  Timer? _highlightTimer;

  // Blocked times data
  Map<String, List<Map<String, dynamic>>> _blockedTimesByDate = {};
  bool _isLoadingBlockedTimes = false;
  String? _blockedTimesError;

  // Google Calendar events data (imported from Google Calendar)
  List<GoogleCalendarEvent> _googleCalendarEvents = [];
  Map<String, List<GoogleCalendarEvent>> _googleCalendarEventsByDate = {}; // Cache events by date
  bool _isLoadingGoogleCalendarEvents = false;
  String? _googleCalendarEventsError;

  // Auto-refresh timer for Google Calendar events (every 2 seconds)
  Timer? _googleCalendarAutoRefreshTimer;

  // Current visible date in calendar (for tracking which week/day is being viewed)
  DateTime _currentVisibleDate = DateTime.now();

  // Client and pet data
  Map<String, Client> clientsCache = {};
  Map<String, Pet> petsCache = {};
  Map<String, List<Pet>> clientPetsCache = {};
  bool _isLoadingClientData = false;
  String? _clientDataError;

  // Service data
  List<Service> _services = [];
  Map<String, Service> _servicesCache = {};
  bool _isLoadingServices = false;
  String? _servicesError;

  // Working hours data
  WorkingHoursSettings? _workingHoursSettings;
  bool _isLoadingWorkingHours = false;
  String? _workingHoursError;

  // Expose the calendar service for mock data access
  CalendarService get calendarService => _calendarService;

  // Getters
  bool get showCanceledAppointments => _showCanceledAppointments;
  bool get showUnpaidAppointments => _showUnpaidAppointments;
  bool get showRevenueSummary => _showRevenueSummary;
  CalendarHourViewMode get hourViewMode => _hourViewMode;
  bool get showFullDay => _hourViewMode == CalendarHourViewMode.fullDay;
  CalendarTimeFormat get timeFormat => _timeFormat;
  bool get isTwelveHourFormat => _timeFormat == CalendarTimeFormat.twelveHour;
  bool get googleCalendarSyncEnabled => _googleCalendarSyncEnabled;

  // Dynamic time range getters
  bool get useDynamicTimeRange => _useDynamicTimeRange;
  int get timeRangePaddingHours => _timeRangePaddingHours;
  int get minimumTimeRangeHours => _minimumTimeRangeHours;
  String get searchQuery => _searchQuery;
  String get selectedServiceFilter => _selectedServiceFilter;
  String get selectedStatusFilter => _selectedStatusFilter;

  // Zoom and accessibility getters
  double get timeSlotHeight => _timeSlotHeight;
  double get minTimeSlotHeight => _minTimeSlotHeight;
  double get maxTimeSlotHeight => _maxTimeSlotHeight;

  // Unavailable slot opacity getters
  double get unavailableSlotOpacity => _unavailableSlotOpacity;
  double get minUnavailableSlotOpacity => _minUnavailableSlotOpacity;
  double get maxUnavailableSlotOpacity => _maxUnavailableSlotOpacity;

  // Unavailable slot color getters
  UnavailableSlotColor get unavailableSlotColor => _unavailableSlotColor;

  /// Get responsive time slot height based on screen size
  double getResponsiveTimeSlotHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isLargeScreen = screenWidth >= 1024.0; // Desktop breakpoint

    if (isLargeScreen) {
      // On large screens, use a larger base height but still respect user zoom preferences
      final baseHeight = _desktopSlotHeight;
      final zoomRatio = _timeSlotHeight / _mobileSlotHeight;
      return (baseHeight * zoomRatio).clamp(_minTimeSlotHeight, _maxTimeSlotHeight + 20.0); // Allow slightly larger max for desktop
    } else {
      // On mobile/tablet, use the regular time slot height
      return _timeSlotHeight;
    }
  }

  static const double _mobileColumnWidth = 100.0;
  static const double _tabletColumnWidth = 120.0;   // iPad/tablet opt
  static const double _laptopColumnWidth = 140.0;   // Laptop screens
  static const double _desktopColumnWidth = 160.0; // Wider columns for desktop

  /// Get responsive staff column width based on screen size
  double getResponsiveStaffColumnWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    

    // Calculate dynamic width based on screen space (for 7-day week view)
    const timeColumnWidth = 60.0; // Reserve space for time column
    final availableWidth = screenWidth - timeColumnWidth;
    final dynamicWidth = availableWidth / 8;
    
    // Define minimum widths for different device types
    double minWidth;
    if (screenWidth >= 1440.0) {
      minWidth = _desktopColumnWidth;
    } else if (screenWidth >= 1024.0) {
      minWidth = _laptopColumnWidth;
    } else if (screenWidth >= 768.0) {
      minWidth = _tabletColumnWidth;
    } else {
      minWidth = _mobileColumnWidth;
    }
    
    // Use the larger of dynamic width or minimum width
    return dynamicWidth > minWidth ? dynamicWidth : minWidth;
  }




  List<Appointment> get appointments => _appointments;
  bool get isLoadingAppointments => _isLoadingAppointments;
  String? get appointmentsError => _appointmentsError;
  String? get highlightedAppointmentId => _highlightedAppointmentId;
  bool get isLoadingBlockedTimes => _isLoadingBlockedTimes;
  String? get blockedTimesError => _blockedTimesError;

  // Google Calendar events getters
  List<GoogleCalendarEvent> get googleCalendarEvents => _googleCalendarEvents;
  bool get isLoadingGoogleCalendarEvents => _isLoadingGoogleCalendarEvents;
  String? get googleCalendarEventsError => _googleCalendarEventsError;
  bool get showGoogleCalendarEvents => _showGoogleCalendarEvents;

  // Current visible date getter
  DateTime get currentVisibleDate => _currentVisibleDate;

  // Method to update the current visible date (called from calendar views)
  void setCurrentVisibleDate(DateTime date) {
    _currentVisibleDate = date;
    // No need to notify listeners as this is just tracking state
  }

  bool get isLoadingClientData => _isLoadingClientData;
  String? get clientDataError => _clientDataError;

  List<StaffResponse> get availableStaff => _availableStaff;
  List<String> get selectedStaff => _selectedStaff;
  bool get isLoadingStaff => _isLoadingStaff;
  String? get staffError => _staffError;

  // Service getters
  List<Service> get services => _services;
  bool get isLoadingServices => _isLoadingServices;
  String? get servicesError => _servicesError;

  // Working hours getters
  WorkingHoursSettings? get workingHoursSettings => _workingHoursSettings;
  bool get isLoadingWorkingHours => _isLoadingWorkingHours;
  String? get workingHoursError => _workingHoursError;

  // Filtered appointments based on settings
  List<Appointment> getFilteredAppointments() {
    return _appointments.where((appointment) {
      // Filter by staff selection
      // If no staff are selected, show no appointments
      if (_selectedStaff.isEmpty) {
        return false;
      }

      // Use the assignedGroomer field directly, with null safety
      String appointmentStaffName;
      try {
        appointmentStaffName = appointment.assignedGroomer.isNotEmpty
            ? appointment.assignedGroomer
            : 'Ana Popescu'; // Fallback if no staff assigned
      } catch (e) {
        appointmentStaffName = 'Ana Popescu'; // Fallback on any error
      }

      // Debug logging
      DebugLogger.logVerbose('🔍 Filtering appointment ${appointment.id}: staff = "$appointmentStaffName", selected staff = $_selectedStaff');

      // Check if the appointment's staff is in the selected list
      StaffResponse? appointmentStaff;

      // First try to find by groomerId (most reliable)
      if (appointment.groomerId != null && appointment.groomerId!.isNotEmpty) {
        try {
          appointmentStaff = _availableStaff.firstWhere(
            (s) => s.id == appointment.groomerId,
          );
        } catch (e) {
          // Staff not found by ID, continue to name matching
        }
      }

      // Fallback to name matching if not found by ID
      if (appointmentStaff == null) {
        try {
          appointmentStaff = _availableStaff.firstWhere(
            (s) => s.name == appointmentStaffName || s.displayName == appointmentStaffName,
          );
        } catch (e) {
          // If staff not found, use first available staff
          appointmentStaff = _availableStaff.isNotEmpty ? _availableStaff.first : null;
        }
      }

      if (appointmentStaff != null && !_selectedStaff.contains(appointmentStaff.id)) {
        return false;
      }

      // Filter by canceled appointments (handles multiple status variants)
      if (!_showCanceledAppointments) {
        final status = appointment.status.toLowerCase();
        if (status == 'canceled' || status == 'cancelled' || status == 'anulat') {
          return false;
        }
      }

      // Filter by unpaid appointments
      if (_showUnpaidAppointments && appointment.isPaid) {
        return false;
      }

      // Filter by search query
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        final matchesClient = appointment.clientName.toLowerCase().contains(query);
        final matchesPet = appointment.petName.toLowerCase().contains(query);
        final matchesService = appointment.service.toLowerCase().contains(query);
        final matchesPhone = appointment.clientPhone.contains(query);

        if (!matchesClient && !matchesPet && !matchesService && !matchesPhone) {
          return false;
        }
      }

      // Filter by service type
      if (_selectedServiceFilter.isNotEmpty && appointment.service != _selectedServiceFilter) {
        return false;
      }

      // Filter by status
      if (_selectedStatusFilter.isNotEmpty && appointment.status != _selectedStatusFilter) {
        return false;
      }

      return true;
    }).toList();
  }

  // Setters for calendar view settings
  void setShowCanceledAppointments(bool value) {
    _showCanceledAppointments = value;
    notifyListeners();
  }

  void setShowUnpaidAppointments(bool value) {
    _showUnpaidAppointments = value;
    notifyListeners();
  }

  void toggleRevenueSummaryVisibility(bool checked) {
    _showRevenueSummary = checked;
    notifyListeners();
  }

  void setHourViewMode(CalendarHourViewMode mode) {
    _hourViewMode = mode;
    CalendarPreferencesService.setHourViewMode(mode);
    notifyListeners();
  }

  void setTimeFormat(CalendarTimeFormat format) {
    if (_timeFormat != format) {
      _timeFormat = format;
      CalendarPreferencesService.setTimeFormat(format);
      notifyListeners();
    }
  }

  void setGoogleCalendarSyncEnabled(bool value) async {
    _googleCalendarSyncEnabled = value;
    CalendarPreferencesService.setGoogleCalendarSyncEnabled(value);

    if (value) {
      // Enable sync - authenticate with Google Calendar
      await _enableGoogleCalendarSync();
    } else {
      // Disable sync - sign out from Google Calendar
      await _disableGoogleCalendarSync();
    }

    notifyListeners();
  }

  void setShowGoogleCalendarEvents(bool value) async {
    _showGoogleCalendarEvents = value;
    CalendarPreferencesService.setShowGoogleCalendarEvents(value);

    // Start or stop auto-refresh timer based on the setting
    if (value && _googleCalendarSyncEnabled && !kIsWeb) {
      _startGoogleCalendarAutoRefresh();
    } else {
      _stopGoogleCalendarAutoRefresh();
    }

    notifyListeners();
  }

  /// Start automatic refresh of Google Calendar events every 30 seconds
  void _startGoogleCalendarAutoRefresh() {
    // Always cancel existing timer first
    _stopGoogleCalendarAutoRefresh();

    DebugLogger.logVerbose('🔄 Starting Google Calendar auto-refresh timer (30 seconds interval)');

    _googleCalendarAutoRefreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) async {
      if (!_googleCalendarSyncEnabled || !_showGoogleCalendarEvents || kIsWeb) {
        _stopGoogleCalendarAutoRefresh();
        return;
      }

      try {
        // Refresh Google Calendar events for the current visible week
        final visibleDate = _currentVisibleDate;
        final weekStart = visibleDate.subtract(Duration(days: visibleDate.weekday - 1));

        DebugLogger.logVerbose('🔄 Auto-refreshing Google Calendar events...');
        await fetchGoogleCalendarEventsForWeek(weekStart, forceRefresh: true);
      } catch (e) {
        DebugLogger.logError('❌ Error during auto-refresh: $e');
      }
    });
  }

  /// Stop automatic refresh of Google Calendar events
  void _stopGoogleCalendarAutoRefresh() {
    if (_googleCalendarAutoRefreshTimer != null) {
      DebugLogger.logVerbose('⏹️ Stopping Google Calendar auto-refresh timer');
      _googleCalendarAutoRefreshTimer?.cancel();
      _googleCalendarAutoRefreshTimer = null;
    }
  }

  /// Enable Google Calendar sync
  Future<void> _enableGoogleCalendarSync() async {
    try {
      // Google Calendar sync is not supported on web
      if (kIsWeb) {
        DebugLogger.logVerbose('⚠️ Google Calendar sync is not supported on web');
        _googleCalendarSyncEnabled = false;
        CalendarPreferencesService.setGoogleCalendarSyncEnabled(false);
        notifyListeners();
        return;
      }

      DebugLogger.logVerbose('🔄 Enabling Google Calendar sync...');

      // Authenticate with Google Calendar
      final authenticated = await GoogleCalendarSyncService.authenticate();

      if (!authenticated) {
        DebugLogger.logVerbose('❌ Google Calendar authentication failed');
        _googleCalendarSyncEnabled = false;
        CalendarPreferencesService.setGoogleCalendarSyncEnabled(false);
        notifyListeners();
        return;
      }

      DebugLogger.logVerbose('✅ Google Calendar sync enabled successfully');

      // Fetch Google Calendar events for the current week with force refresh
      // This ensures we get the latest events from Google Calendar
      final now = DateTime.now();
      final weekStart = now.subtract(Duration(days: now.weekday - 1)); // Start of current week (Monday)
      await fetchGoogleCalendarEventsForWeek(weekStart, forceRefresh: true);

      // Always start auto-refresh when sync is enabled
      _startGoogleCalendarAutoRefresh();

      // Optionally: Sync existing appointments
      // await _syncExistingAppointments();
    } catch (e) {
      DebugLogger.logError('❌ Error enabling Google Calendar sync: $e');
      _googleCalendarSyncEnabled = false;
      CalendarPreferencesService.setGoogleCalendarSyncEnabled(false);
      notifyListeners();
    }
  }

  /// Disable Google Calendar sync
  Future<void> _disableGoogleCalendarSync() async {
    try {
      DebugLogger.logVerbose('🔄 Disabling Google Calendar sync...');

      // Stop auto-refresh timer
      _stopGoogleCalendarAutoRefresh();

      await GoogleCalendarSyncService.signOut();

      // Clear Google Calendar events cache
      clearGoogleCalendarEventsCache();

      DebugLogger.logVerbose('✅ Google Calendar sync disabled successfully');
    } catch (e) {
      DebugLogger.logError('❌ Error disabling Google Calendar sync: $e');
    }
  }

  /// Sync an appointment to Google Calendar
  Future<void> _syncAppointmentToGoogleCalendar(Appointment appointment) async {
    try {
      // Skip sync on web
      if (kIsWeb) {
        return;
      }

      if (!_googleCalendarSyncEnabled) {
        return;
      }

      DebugLogger.logVerbose('📅 Syncing appointment to Google Calendar: ${appointment.id}');
      final googleEventId = await GoogleCalendarSyncService.syncAppointment(appointment);

      if (googleEventId != null) {
        DebugLogger.logVerbose('✅ Appointment synced to Google Calendar with ID: $googleEventId');
        // TODO: Store googleEventId in appointment metadata for future updates/deletes
      }
    } catch (e) {
      DebugLogger.logError('❌ Error syncing appointment to Google Calendar: $e');
    }
  }

  // Dynamic time range setters
  void setUseDynamicTimeRange(bool value) {
    if (_useDynamicTimeRange != value) {
      _useDynamicTimeRange = value;
      _refreshCalendarAfterTimeRangeChange();
    }
  }

  void setTimeRangePaddingHours(int hours) {
    if (hours >= 0 && hours <= 4 && _timeRangePaddingHours != hours) { // Reasonable limits
      _timeRangePaddingHours = hours;
      _refreshCalendarAfterTimeRangeChange();
    }
  }

  void setMinimumTimeRangeHours(int hours) {
    if (hours >= 4 && hours <= 16 && _minimumTimeRangeHours != hours) { // Reasonable limits
      _minimumTimeRangeHours = hours;
      _refreshCalendarAfterTimeRangeChange();
    }
  }

  /// Refresh calendar views when time range settings change
  void _refreshCalendarAfterTimeRangeChange() {
    DebugLogger.logVerbose('🔄 Refreshing calendar after time range settings change');
    DebugLogger.logVerbose('   Dynamic range enabled: $_useDynamicTimeRange');
    DebugLogger.logVerbose('   Padding hours: $_timeRangePaddingHours');
    DebugLogger.logVerbose('   Minimum range hours: $_minimumTimeRangeHours');
    notifyListeners();
  }

  // Zoom and accessibility setters
  void setTimeSlotHeight(double height) {
    _timeSlotHeight = height.clamp(_minTimeSlotHeight, _maxTimeSlotHeight);
    CalendarPreferencesService.setTimeSlotHeight(_timeSlotHeight);
    notifyListeners();
  }

  // Unavailable slot opacity setters
  void setUnavailableSlotOpacity(double opacity) {
    _unavailableSlotOpacity = opacity.clamp(_minUnavailableSlotOpacity, _maxUnavailableSlotOpacity);
    CalendarPreferencesService.setUnavailableSlotOpacity(_unavailableSlotOpacity);
    notifyListeners();
  }

  // Unavailable slot color setters
  void setUnavailableSlotColor(UnavailableSlotColor color) {
    _unavailableSlotColor = color;
    CalendarPreferencesService.setUnavailableSlotColor(_unavailableSlotColor);
    notifyListeners();
  }

  void zoomIn() {
    final newHeight = (_timeSlotHeight + 10).clamp(_minTimeSlotHeight, _maxTimeSlotHeight);
    setTimeSlotHeight(newHeight);
  }

  void zoomOut() {
    final newHeight = (_timeSlotHeight - 10).clamp(_minTimeSlotHeight, _maxTimeSlotHeight);
    setTimeSlotHeight(newHeight);
  }

  void resetZoom() {
    setTimeSlotHeight(80.0); // Updated default height for better visibility
  }

  /// Initialize calendar preferences from storage
  Future<void> initializePreferences() async {
    if (ApiConfig.enableVerboseLogging) {
      DebugLogger.logVerbose('🔄 CalendarProvider: Initializing preferences...');
    }

    // Migrate legacy preference if needed
    await CalendarPreferencesService.migrateLegacyPreference();

    // Load current preferences
    _hourViewMode = await CalendarPreferencesService.getHourViewMode();
    _timeSlotHeight = await CalendarPreferencesService.getTimeSlotHeight();
    _unavailableSlotOpacity = await CalendarPreferencesService.getUnavailableSlotOpacity();
    _unavailableSlotColor = await CalendarPreferencesService.getUnavailableSlotColor();
    _showRevenueSummary = await CalendarPreferencesService.getShowRevenueSummary();
    _timeFormat = await CalendarPreferencesService.getTimeFormat();
    _googleCalendarSyncEnabled = await CalendarPreferencesService.getGoogleCalendarSyncEnabled();
    _showGoogleCalendarEvents = await CalendarPreferencesService.getShowGoogleCalendarEvents();

    // Load working hours to get correct business hours (critical for closure checks)
    if (ApiConfig.enableVerboseLogging) {
      DebugLogger.logVerbose('🔄 CalendarProvider: Loading working hours during initialization...');
    }
    await loadWorkingHours();

    // Feature 4: Start real-time color update timer
    startColorUpdateTimer();

    // Start Google Calendar auto-refresh if sync is enabled (only once)
    if (_googleCalendarSyncEnabled && _showGoogleCalendarEvents && !kIsWeb && _googleCalendarAutoRefreshTimer == null) {
      _startGoogleCalendarAutoRefresh();
    }

    notifyListeners();
    if (ApiConfig.enableVerboseLogging) {
      DebugLogger.logVerbose('✅ CalendarProvider: Preferences initialization completed');
      DebugLogger.logVerbose('📏 CalendarProvider: Time slot height loaded: ${_timeSlotHeight}px');
      DebugLogger.logVerbose('🎨 CalendarProvider: Unavailable slot opacity loaded: $_unavailableSlotOpacity');
    }
  }

  /// Lazy load calendar data when calendar screen is accessed
  Future<void> ensureCalendarDataLoaded() async {
    // Only load appointments and blocked times when actually needed
    if (_appointments.isEmpty && !_isLoadingAppointments) {
      await fetchAppointmentsForDate(DateTime.now());
    }

    final today = DateTime.now();
    final todayKey = _formatDateKey(today);
    if (!_blockedTimesByDate.containsKey(todayKey) && !_isLoadingBlockedTimes) {
      await fetchBlockedTimesForDate(today);
    }
  }

  // Setters for search and filter
  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  void setServiceFilter(String service) {
    _selectedServiceFilter = service;
    notifyListeners();
  }

  void setStatusFilter(String status) {
    _selectedStatusFilter = status;
    notifyListeners();
  }

  void clearFilters() {
    _searchQuery = '';
    _selectedServiceFilter = '';
    _selectedStatusFilter = '';
    notifyListeners();
  }

  // Fetch appointments for a specific date (build-safe)
  Future<void> fetchAppointmentsForDate(DateTime date, {bool forceRefresh = false}) async {
    final dateKey = _formatDateKey(date);

    // Ensure underlying service cache is bypassed when forceRefresh is true
    if (forceRefresh) {
      _calendarService.clearCacheForDate(date);
    }

    // Check if we already have appointments for this date (unless forcing refresh)
    if (!forceRefresh && _appointmentsByDate.containsKey(dateKey)) {
      _appointments = _appointmentsByDate[dateKey]!;
      // Defer notifyListeners to avoid build-time state updates
      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });

      // Still fetch Google Calendar events if sync is enabled
      if (_googleCalendarSyncEnabled && !kIsWeb) {
        await fetchGoogleCalendarEventsForDate(date, forceRefresh: forceRefresh);
      }

      return;
    }

    // Use microtask to defer the async operation
    Future.microtask(() async {
      _isLoadingAppointments = true;
      _appointmentsError = null;
      notifyListeners();

      try {
        final appointments = await _calendarService.getAppointmentsForDate(date);
        _appointmentsByDate[dateKey] = appointments;
        _appointments = appointments;
        _appointmentsError = null;
        DebugLogger.logVerbose('✅ Appointments refreshed for date: $dateKey (${appointments.length} appointments)');

        // Also fetch Google Calendar events if sync is enabled
        if (_googleCalendarSyncEnabled && !kIsWeb) {
          await fetchGoogleCalendarEventsForDate(date, forceRefresh: forceRefresh);
        }
      } catch (e) {
        _appointmentsError = 'Eroare la încărcarea programărilor: $e';
        _appointments = [];
        DebugLogger.logError('❌ Error fetching appointments for date: $dateKey - $e');
      } finally {
        _isLoadingAppointments = false;
        notifyListeners();
      }
    });
  }

  // Get appointments for a specific date from cache
  List<Appointment> getAppointmentsForDate(DateTime date) {
    final dateKey = _formatDateKey(date);
    return _appointmentsByDate[dateKey] ?? [];
  }

  /// CRITICAL SECURITY FIX: Clear all appointment caches
  /// This MUST be called when switching salons to prevent cross-salon data leakage
  void clearAppointmentCache() {
    _appointmentsByDate.clear();
    _appointments.clear();
    DebugLogger.logVerbose('🧹 SECURITY: Cleared all appointment caches to prevent cross-salon data leakage');
    notifyListeners();
  }
  // Get blocked times for a specific date from cache
  List<Map<String, dynamic>> getBlockedTimesForDate(DateTime date) {
    final dateKey = _formatDateKey(date);
    return _blockedTimesByDate[dateKey] ?? [];
  }

  // Get filtered appointments for a specific date
  List<Appointment> getFilteredAppointmentsForDate(DateTime date) {
    final dateKey = _formatDateKey(date);
    final appointments = _appointmentsByDate[dateKey] ?? [];

    return appointments.where((appointment) {
      // Filter by staff selection
      if (_selectedStaff.isNotEmpty) {
        // Use the assignedGroomer field directly, with null safety

        String appointmentStaffName;
        try {
          appointmentStaffName = appointment.assignedGroomer.isNotEmpty
              ? appointment.assignedGroomer
              : 'Ana Popescu'; // Fallback if no staff assigned
        } catch (e) {
          appointmentStaffName = 'Ana Popescu'; // Fallback on any error
        }

        // Check if the appointment's staff is in the selected list
        StaffResponse? appointmentStaff;

        // First try to find by groomerId (most reliable)
        if (appointment.groomerId != null && appointment.groomerId!.isNotEmpty) {
          try {
            appointmentStaff = _availableStaff.firstWhere(
              (s) => s.id == appointment.groomerId,
            );
          } catch (e) {
            // Staff not found by ID, continue to name matching
          }
        }

        // Fallback to name matching if not found by ID
        if (appointmentStaff == null) {
          try {
            appointmentStaff = _availableStaff.firstWhere(
              (s) => s.name == appointmentStaffName || s.displayName == appointmentStaffName,
            );
          } catch (e) {
            // If staff not found, use first available staff
            appointmentStaff = _availableStaff.isNotEmpty ? _availableStaff.first : null;
          }
        }

        if (appointmentStaff != null && !_selectedStaff.contains(appointmentStaff.id)) {
          return false;
        }
      }

      // Include or exclude canceled appointments based on preference
      if (!_showCanceledAppointments) {
        final status = appointment.status.toLowerCase();
        if (status == 'canceled' || status == 'cancelled' || status == 'anulat') {
          return false;
        }
      }


      // Filter by unpaid appointments
      if (_showUnpaidAppointments && appointment.isPaid) {
        return false;
      }

      // Filter by search query
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        final matchesClient = appointment.clientName.toLowerCase().contains(query);
        final matchesPet = appointment.petName.toLowerCase().contains(query);
        final matchesService = appointment.service.toLowerCase().contains(query);
        final matchesPhone = appointment.clientPhone.contains(query);

        if (!matchesClient && !matchesPet && !matchesService && !matchesPhone) {
          return false;
        }
      }

      // Filter by service type
      if (_selectedServiceFilter.isNotEmpty && appointment.service != _selectedServiceFilter) {
        return false;
      }

      // Filter by status
      if (_selectedStatusFilter.isNotEmpty && appointment.status != _selectedStatusFilter) {
        return false;
      }

      return true;
    }).toList();
  }

  // Fetch appointments for a date range (optimized - single API call)
  Future<void> fetchAppointmentsForDateRange(DateTime startDate, DateTime endDate) async {
    _isLoadingAppointments = true;
    _appointmentsError = null;
    notifyListeners();

    try {
      // Make a single API call for the entire date range
      final appointments = await _calendarService.getAppointmentsForDateRange(startDate, endDate);

      // Group appointments by date and cache them
      for (final appointment in appointments) {
        final dateKey = _formatDateKey(appointment.startTime);
        if (!_appointmentsByDate.containsKey(dateKey)) {
          _appointmentsByDate[dateKey] = [];
        }

        // Avoid duplicates
        if (!_appointmentsByDate[dateKey]!.any((a) => a.id == appointment.id)) {
          _appointmentsByDate[dateKey]!.add(appointment);
        }
      }

      // Update current appointments to show the range
      _appointments = [];
      for (DateTime date = startDate;
           date.isBefore(endDate.add(const Duration(days: 1)));
           date = date.add(const Duration(days: 1))) {
        _appointments.addAll(getAppointmentsForDate(date));
      }

      _appointmentsError = null;
      DebugLogger.logVerbose('✅ Appointments fetched for date range: ${_formatDateKey(startDate)} to ${_formatDateKey(endDate)} (${appointments.length} total appointments)');
    } catch (e) {
      _appointmentsError = 'Eroare la încărcarea programărilor: $e';
      _appointments = [];
      DebugLogger.logError('❌ Error fetching appointments for date range: $startDate to $endDate - $e');
    } finally {
      _isLoadingAppointments = false;
      notifyListeners();
    }
  }

  // Fetch appointments for a week (optimized - single API call)
  Future<void> fetchAppointmentsForWeek(DateTime weekStart) async {
    final weekEnd = weekStart.add(const Duration(days: 6)); // 7 days total
    DebugLogger.logVerbose('📅 Fetching appointments for week: ${_formatDateKey(weekStart)} to ${_formatDateKey(weekEnd)}');

    // Check if we already have all appointments for this week cached
    bool hasAllWeekData = true;
    for (int i = 0; i < 7; i++) {
      final day = weekStart.add(Duration(days: i));
      final dateKey = _formatDateKey(day);
      if (!_appointmentsByDate.containsKey(dateKey)) {
        hasAllWeekData = false;
        break;
      }
    }

    if (hasAllWeekData) {
      DebugLogger.logVerbose('📋 Using cached appointments for week');
    } else {
      await fetchAppointmentsForDateRange(weekStart, weekEnd);
    }

    // Also fetch Google Calendar events for the week if sync is enabled
    // Use force refresh to ensure deleted events are removed
    if (_googleCalendarSyncEnabled && !kIsWeb) {
      await fetchGoogleCalendarEventsForWeek(weekStart, forceRefresh: true);
    }
  }

  // Fetch blocked times for a date
  Future<void> fetchBlockedTimesForDate(DateTime date, {bool forceRefresh = false}) async {
    final dateKey = _formatDateKey(date);
    if (!forceRefresh && _blockedTimesByDate.containsKey(dateKey)) {
      return;
    }

    _isLoadingBlockedTimes = true;
    _blockedTimesError = null;
    notifyListeners();

    try {
      if (ApiConfig.enableVerboseLogging) {
        DebugLogger.logVerbose('🔄 Fetching block times from service for date: $date');
      }
      final blocks = await _calendarService.getBlockedTimes(
        startDate: date,
        endDate: date,
      );
      _blockedTimesByDate[dateKey] = blocks;
      _blockedTimesError = null;
      if (ApiConfig.enableVerboseLogging) {
        DebugLogger.logVerbose('✅ CalendarProvider: Cached ${blocks.length} block times for $dateKey');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('❌ CalendarProvider: Error fetching block times for $dateKey: $e');
      }
      _blockedTimesError = 'Eroare la încărcarea blocărilor: $e';
      _blockedTimesByDate[dateKey] = [];
    } finally {
      _isLoadingBlockedTimes = false;
      notifyListeners();
    }
  }

  // Fetch blocked times for a date range (optimized - single API call)
  Future<void> fetchBlockedTimesForDateRange(DateTime startDate, DateTime endDate) async {
    _isLoadingBlockedTimes = true;
    _blockedTimesError = null;
    notifyListeners();

    try {
      // Make a single API call for the entire date range
      final blocks = await _calendarService.getBlockedTimes(
        startDate: startDate,
        endDate: endDate,
      );

      // Group blocked times by date and cache them
      for (final block in blocks) {
        final startTime = DateTime.parse(block['startTime']);
        final dateKey = _formatDateKey(startTime);
        if (!_blockedTimesByDate.containsKey(dateKey)) {
          _blockedTimesByDate[dateKey] = [];
        }

        // Avoid duplicates
        if (!_blockedTimesByDate[dateKey]!.any((b) => b['id'] == block['id'])) {
          _blockedTimesByDate[dateKey]!.add(block);
        }
      }
    } catch (e) {
      _blockedTimesError = 'Eroare la încărcarea blocărilor: $e';
    } finally {
      _isLoadingBlockedTimes = false;
      notifyListeners();
    }
  }

  // Fetch blocked times for a week (optimized - single API call)
  Future<void> fetchBlockedTimesForWeek(DateTime weekStart) async {
    final weekEnd = weekStart.add(const Duration(days: 6)); // 7 days total

    // Check if we already have all blocked times for this week cached
    bool hasAllWeekData = true;
    for (int i = 0; i < 7; i++) {
      final day = weekStart.add(Duration(days: i));
      final dateKey = _formatDateKey(day);
      if (!_blockedTimesByDate.containsKey(dateKey)) {
        hasAllWeekData = false;
        break;
      }
    }

    if (hasAllWeekData) {
      DebugLogger.logVerbose('📋 Using cached blocked times for week');
      return;
    }

    await fetchBlockedTimesForDateRange(weekStart, weekEnd);
  }

  // Helper method to format date as key
  String _formatDateKey(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  // Fetch Google Calendar events for a specific date range
  Future<void> fetchGoogleCalendarEventsForDateRange(DateTime startDate, DateTime endDate, {bool forceRefresh = false}) async {
    // Skip if sync is not enabled or on web
    if (!_googleCalendarSyncEnabled || kIsWeb) {
      return;
    }

    _isLoadingGoogleCalendarEvents = true;
    _googleCalendarEventsError = null;
    notifyListeners();

    try {
      DebugLogger.logVerbose('📥 Fetching Google Calendar events from ${_formatDateKey(startDate)} to ${_formatDateKey(endDate)}');

      // If force refresh, clear cache for the date range
      if (forceRefresh) {
        DebugLogger.logVerbose('🔄 Force refresh - clearing Google Calendar events cache for date range');
        // Clear cache for all dates in the range
        DateTime currentDate = startDate;
        while (currentDate.isBefore(endDate) || currentDate.isAtSameMomentAs(endDate)) {
          final dateKey = _formatDateKey(currentDate);
          _googleCalendarEventsByDate.remove(dateKey);
          currentDate = currentDate.add(const Duration(days: 1));
        }
      }

      // Fetch events from all Google Calendars
      final googleEvents = await GoogleCalendarSyncService.fetchAllGoogleCalendarEvents(
        startDate: startDate,
        endDate: endDate,
      );

      // Convert to GoogleCalendarEvent objects
      final events = googleEvents.map((event) {
        return GoogleCalendarEvent.fromGoogleEvent(event);
      }).toList();

      // Clear existing events for the date range and rebuild cache
      // This ensures deleted events are removed
      DateTime currentDate = startDate;
      while (currentDate.isBefore(endDate) || currentDate.isAtSameMomentAs(endDate)) {
        final dateKey = _formatDateKey(currentDate);
        _googleCalendarEventsByDate[dateKey] = [];
        currentDate = currentDate.add(const Duration(days: 1));
      }

      // Group events by date and cache them
      for (final event in events) {
        final dateKey = _formatDateKey(event.startTime);
        if (!_googleCalendarEventsByDate.containsKey(dateKey)) {
          _googleCalendarEventsByDate[dateKey] = [];
        }

        // Add event (no need to check for duplicates since we cleared the cache)
        _googleCalendarEventsByDate[dateKey]!.add(event);
      }

      // Update the main list with all events
      _googleCalendarEvents = events;
      _googleCalendarEventsError = null;

      DebugLogger.logVerbose('✅ Fetched ${events.length} Google Calendar events');
    } catch (e) {
      DebugLogger.logError('❌ Error fetching Google Calendar events: $e');
      _googleCalendarEventsError = 'Eroare la încărcarea evenimentelor Google Calendar: $e';
    } finally {
      _isLoadingGoogleCalendarEvents = false;
      notifyListeners();
    }
  }

  // Fetch Google Calendar events for a specific date
  Future<void> fetchGoogleCalendarEventsForDate(DateTime date, {bool forceRefresh = false}) async {
    final dateKey = _formatDateKey(date);

    // Skip if sync is not enabled or on web
    if (!_googleCalendarSyncEnabled || kIsWeb) {
      return;
    }

    // Check if we already have events for this date (unless forcing refresh)
    if (!forceRefresh && _googleCalendarEventsByDate.containsKey(dateKey)) {
      DebugLogger.logVerbose('📋 Using cached Google Calendar events for $dateKey');
      return;
    }

    // Fetch events for the entire day
    final startOfDay = DateTime(date.year, date.month, date.day, 0, 0, 0);
    final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);

    await fetchGoogleCalendarEventsForDateRange(startOfDay, endOfDay, forceRefresh: forceRefresh);
  }

  // Fetch Google Calendar events for a week
  Future<void> fetchGoogleCalendarEventsForWeek(DateTime weekStart, {bool forceRefresh = false}) async {
    final weekEnd = weekStart.add(const Duration(days: 6)); // 7 days total

    // Skip if sync is not enabled or on web
    if (!_googleCalendarSyncEnabled || kIsWeb) {
      return;
    }

    // If force refresh, always fetch
    if (forceRefresh) {
      await fetchGoogleCalendarEventsForDateRange(weekStart, weekEnd, forceRefresh: true);
      return;
    }

    // Check if we already have all events for this week cached
    bool hasAllWeekData = true;
    for (int i = 0; i < 7; i++) {
      final day = weekStart.add(Duration(days: i));
      final dateKey = _formatDateKey(day);
      if (!_googleCalendarEventsByDate.containsKey(dateKey)) {
        hasAllWeekData = false;
        break;
      }
    }

    if (hasAllWeekData) {
      DebugLogger.logVerbose('📋 Using cached Google Calendar events for week');
      return;
    }

    await fetchGoogleCalendarEventsForDateRange(weekStart, weekEnd);
  }

  // Get Google Calendar events for a specific date
  List<GoogleCalendarEvent> getGoogleCalendarEventsForDate(DateTime date) {
    if (!_showGoogleCalendarEvents || !_googleCalendarSyncEnabled) {
      return [];
    }

    final dateKey = _formatDateKey(date);
    final events = _googleCalendarEventsByDate[dateKey] ?? [];

    // Filter out all-day events as they would cover the entire day in the calendar view
    // Users can still see them in a separate section if needed
    return events.where((event) => !event.isAllDay).toList();
  }

  // Clear Google Calendar events cache
  void clearGoogleCalendarEventsCache() {
    _googleCalendarEvents.clear();
    _googleCalendarEventsByDate.clear();
    notifyListeners();
  }

  // Add a new appointment
  Future<Appointment?> addAppointment(Appointment appointment) async {
    try {
      final newAppointment = await _calendarService.addAppointment(appointment);

      // Only proceed if we got a valid appointment back
      if (newAppointment != null) {
        // Clear the cache for the appointment date to force refresh
        final dateKey = _formatDateKey(newAppointment.startTime);
        _appointmentsByDate.remove(dateKey);

        // Add to current appointments list if it's for the same date
        final currentDate = _appointments.isNotEmpty ? _appointments.first.startTime : DateTime.now();
        final currentDateKey = _formatDateKey(currentDate);

        if (dateKey == currentDateKey) {
          _appointments.add(newAppointment);
          _appointments.sort((a, b) => a.startTime.compareTo(b.startTime));

          // Update the cache with the new list
          _appointmentsByDate[dateKey] = List.from(_appointments);
          notifyListeners();
        } else {
          // If it's for a different date, just clear that date's cache
          // The appointments will be loaded when that date is viewed
          DebugLogger.logVerbose('✅ Appointment created for ${dateKey}, cache cleared');
        }
      }

      return newAppointment;
    } catch (e) {
      DebugLogger.logVerbose('❌ Error adding appointment: $e');
      return null;
    }
  }

  // Add a new appointment from form data using ScheduleAppointmentRequest DTO
  Future<AppointmentCreationResult> addAppointmentFromFormData(Map<String, dynamic> scheduleRequest) async {
    Appointment? optimistic;
    try {
      // Build a lightweight appointment for immediate UI feedback
      try {
        final start = DateTime.parse('${scheduleRequest['appointmentDate']}T${scheduleRequest['startTime']}');
        final end = DateTime.parse('${scheduleRequest['appointmentDate']}T${scheduleRequest['endTime']}');
        optimistic = Appointment(
          id: 'temp-${DateTime.now().millisecondsSinceEpoch}',
          clientId: scheduleRequest['clientId'] ?? '',
          clientName: scheduleRequest['clientName'] ?? '',
          clientPhone: scheduleRequest['clientPhone'] ?? '',
          petId: scheduleRequest['petId'] ?? '',
          petName: scheduleRequest['petName'] ?? '',
          petSpecies: scheduleRequest['petSpecies'] ?? '',
          service: '',
          services: List<String>.from(scheduleRequest['serviceIds'] ?? []),
          startTime: start,
          endTime: end,
          status: 'PENDING',
          isPaid: false,
          notes: scheduleRequest['notes'] ?? '',
          assignedGroomer: '',
          groomerId: scheduleRequest['staffId'],
          totalPrice: 0.0,
          totalDuration: end.difference(start).inMinutes,
          repetitionFrequency: scheduleRequest['repetitionFrequency'] ?? 'none',
        );

        final dateKey = _formatDateKey(start);
        _appointments.add(optimistic);
        _appointments.sort((a, b) => a.startTime.compareTo(b.startTime));
        _appointmentsByDate[dateKey] = List.from(_appointments);
        notifyListeners();
      } catch (_) {}

      final result = await _calendarService.addAppointmentFromFormData(scheduleRequest);

      // Only proceed if we got a successful result
      if (result.success && result.appointment != null) {
        final newAppointment = result.appointment!;

        if (optimistic != null) {
          _appointments.removeWhere((a) => a.id == optimistic!.id);
        }

        // Clear the cache for the appointment date to force refresh
        final dateKey = _formatDateKey(newAppointment.startTime);
        _appointmentsByDate.remove(dateKey);

        // Add to current appointments list if it's for the same date
        final currentDate = _appointments.isNotEmpty ? _appointments.first.startTime : DateTime.now();
        final currentDateKey = _formatDateKey(currentDate);

        if (dateKey == currentDateKey) {
          _appointments.add(newAppointment);
          _appointments.sort((a, b) => a.startTime.compareTo(b.startTime));

          // Update the cache with the new list
          _appointmentsByDate[dateKey] = List.from(_appointments);
          notifyListeners();
        } else {
          // If it's for a different date, just clear that date's cache
          // The appointments will be loaded when that date is viewed
          DebugLogger.logVerbose('✅ Appointment created for ${dateKey}, cache cleared');
        }

        // Sync SMS count after successful appointment creation
        // This handles the case where backend automatically sends confirmation SMS
        await SmsQuotaService.syncAfterAppointmentCreation();

        // Sync to Google Calendar if enabled
        if (_googleCalendarSyncEnabled) {
          await _syncAppointmentToGoogleCalendar(newAppointment);
        }
      } else {
        if (optimistic != null) {
          _appointments.removeWhere((a) => a.id == optimistic!.id);
          _appointmentsByDate[_formatDateKey(optimistic!.startTime)]?.removeWhere((a) => a.id == optimistic!.id);
          notifyListeners();
        }
      }

      return result;
    } catch (e) {
      DebugLogger.logError('❌ Error adding appointment from form data: $e');
      return AppointmentCreationResult.failure('Error creating appointment: $e');
    }
  }

  // Block time in the calendar
  Future<BlockTimeResult> blockTime(DateTime start, DateTime end, String reason, {String? staffId}) async {
    try {
      DebugLogger.logVerbose('🔄 CalendarProvider.blockTime: $start to $end for reason: $reason${staffId != null ? ' (staff: $staffId)' : ''}');

      final result = await _calendarService.blockTime(start, end, reason, staffId: staffId);

      if (result.success) {
        DebugLogger.logVerbose('✅ CalendarProvider: Block time successful, refreshing calendar data');

        // Refresh appointments for the affected date
        await fetchAppointmentsForDate(start);

        // Refresh blocked times for the affected date (CRITICAL: this was missing!)
        await fetchBlockedTimesForDate(start, forceRefresh: true);

        // Also refresh staff data to ensure calendar shows updated availability
        await refreshStaffData();

        notifyListeners();
        DebugLogger.logVerbose('🎨 CalendarProvider: Block time created and calendar refreshed - UI should show new block');
      } else {
        DebugLogger.logVerbose('❌ CalendarProvider: Block time failed - ${result.errorMessage}');
      }

      return result;
    } catch (e) {
      DebugLogger.logVerbose('❌ CalendarProvider.blockTime error: $e');
      return BlockTimeResult.failure('Eroare neașteptată: $e');
    }
  }

  // Check availability for blocking time
  Future<Map<String, dynamic>?> checkBlockTimeAvailability({
    required DateTime startTime,
    required DateTime endTime,
    required List<String> staffIds,
  }) async {
    try {
      DebugLogger.logVerbose('🔄 CalendarProvider.checkBlockTimeAvailability: Checking availability');

      final availability = await _calendarService.checkBlockTimeAvailability(
        startTime: startTime,
        endTime: endTime,
        staffIds: staffIds,
      );

      if (availability != null) {
        DebugLogger.logVerbose('✅ CalendarProvider: Availability check completed');
        return availability;
      } else {
        DebugLogger.logVerbose('❌ CalendarProvider: Availability check failed');
        return null;
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ CalendarProvider.checkBlockTimeAvailability error: $e');
      return null;
    }
  }

  // Delete (cancel) a blocked time slot with optimistic UI updates
  Future<bool> deleteBlockTime(String blockId, {String? reason, bool refreshWeek = false}) async {
    try {
      DebugLogger.logVerbose('🔄 CalendarProvider.deleteBlockTime: Deleting block $blockId (refreshWeek: $refreshWeek)');

      // Optimistic update: Remove from cache immediately for instant UI feedback
      final removedBlock = removeBlockTimeFromCache(blockId);
      if (removedBlock == null) {
        DebugLogger.logVerbose('❌ Block $blockId not found in cache, cannot perform optimistic delete');
        return false;
      }

      // Make the API call
      final success = await _calendarService.deleteBlockTime(blockId, reason: reason);

      if (success) {
        DebugLogger.logVerbose('✅ CalendarProvider: Block time deleted successfully on server');
        // Block is already removed from cache, no need to refresh unless we want to sync other changes
        // Just refresh staff data to ensure availability is updated
        await refreshStaffData();
      } else {
        DebugLogger.logVerbose('❌ CalendarProvider: Failed to delete block time on server, restoring to cache');
        // Restore the block to cache since the API call failed
        restoreBlockTimeToCache(removedBlock);
      }

      return success;
    } catch (e) {
      DebugLogger.logVerbose('❌ CalendarProvider.deleteBlockTime error: $e');
      // If there was an error and we have the removed block, restore it
      return false;
    }
  }

  // Get available grooming services with their durations
  Future<Map<String, int>> getServiceDurations() async {
    return await _calendarService.getServiceDurations();
  }

  // Clear cache for specific date(s) and force refresh
  void clearCacheForDate(DateTime date) {
    final dateKey = _formatDateKey(date);
    _appointmentsByDate.remove(dateKey);
    _blockedTimesByDate.remove(dateKey);
    DebugLogger.logVerbose('🗑️ Cache cleared for date: $dateKey');
  }

  // Clear cache for multiple dates and force refresh
  void clearCacheForDates(List<DateTime> dates) {
    for (final date in dates) {
      clearCacheForDate(date);
    }
  }

  // Remove a specific block time from cache (optimistic update)
  Map<String, dynamic>? removeBlockTimeFromCache(String blockId) {
    Map<String, dynamic>? removedBlock;

    // Search through all dates to find and remove the block
    for (final entry in _blockedTimesByDate.entries) {
      final dateKey = entry.key;
      final blocks = entry.value;

      for (int i = 0; i < blocks.length; i++) {
        if (blocks[i]['blockId'] == blockId) {
          removedBlock = blocks[i];
          blocks.removeAt(i);
          DebugLogger.logVerbose('🗑️ Optimistically removed block $blockId from cache for date $dateKey');
          notifyListeners(); // Immediately update UI
          return removedBlock;
        }
      }
    }

    DebugLogger.logVerbose('⚠️ Block $blockId not found in cache for removal');
    return null;
  }

  // Restore a block time to cache (if API call fails)
  void restoreBlockTimeToCache(Map<String, dynamic> block) {
    try {
      final startTime = DateTime.parse(block['startTime']).toLocal();
      final dateKey = _formatDateKey(startTime);

      if (_blockedTimesByDate.containsKey(dateKey)) {
        _blockedTimesByDate[dateKey]!.add(block);
      } else {
        _blockedTimesByDate[dateKey] = [block];
      }

      DebugLogger.logVerbose('🔄 Restored block ${block['blockId']} to cache for date $dateKey');
      notifyListeners();
    } catch (e) {
      DebugLogger.logVerbose('❌ Error restoring block to cache: $e');
    }
  }

  // Force refresh appointments for current date
  Future<void> forceRefreshCurrentDate() async {
    if (_appointments.isNotEmpty) {
      final currentDate = _appointments.first.startTime;
      await fetchAppointmentsForDate(currentDate, forceRefresh: true);
    }
  }

  /// Save current staff selection for a specific salon
  void _saveStaffSelectionForSalon(String salonId) {
    if (_selectedStaff.isNotEmpty) {
      _staffSelectionPerSalon[salonId] = List.from(_selectedStaff);
      DebugLogger.logVerbose('💾 CalendarProvider: Saved staff selection for salon $salonId: $_selectedStaff');
    }
  }

  /// Restore staff selection for the new salon after switching
  void _restoreStaffSelectionForSalon(String salonId) {
    if (_staffSelectionPerSalon.containsKey(salonId)) {
      final savedSelection = _staffSelectionPerSalon[salonId]!;
      // Only restore selection for staff that actually exist in the new salon
      final validSelection = savedSelection.where((staffId) =>
        _availableStaff.any((staff) => staff.id == staffId)
      ).toList();

      if (validSelection.isNotEmpty) {
        _selectedStaff = validSelection;
        DebugLogger.logVerbose('🔄 CalendarProvider: Restored staff selection for salon $salonId: $validSelection');
      } else {
        DebugLogger.logVerbose('⚠️ CalendarProvider: No valid saved staff selection for salon $salonId, using default');
      }
    } else {
      DebugLogger.logVerbose('📭 CalendarProvider: No saved staff selection for salon $salonId, will use default');
    }
  }

  /// Clear saved staff selection for a deleted salon
  void clearStaffSelectionForSalon(String salonId) {
    if (_staffSelectionPerSalon.containsKey(salonId)) {
      _staffSelectionPerSalon.remove(salonId);
      DebugLogger.logVerbose('🗑️ CalendarProvider: Cleared saved staff selection for deleted salon $salonId');
    }
  }

  /// Clear all salon-specific caches for salon switching
  Future<void> clearAllCachesForSalonSwitch(String newSalonId) async {
    DebugLogger.logVerbose('🔄 CalendarProvider: Salon switch initiated');
    DebugLogger.logVerbose('📍 CalendarProvider: New salon ID: $newSalonId');
    DebugLogger.logVerbose('🗑️ CalendarProvider: Clearing all salon-specific caches...');

    // Save current staff selection before clearing
    try {
      final currentSalonId = await AuthService.getCurrentSalonId();
      if (currentSalonId != null && currentSalonId != newSalonId) {
        _saveStaffSelectionForSalon(currentSalonId);
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ CalendarProvider: Error saving current staff selection: $e');
    }

    // Count current cache sizes for logging
    final clientsCacheCount = clientsCache.length;
    final petsCacheCount = petsCache.length;
    final clientPetsCacheCount = clientPetsCache.length;
    final appointmentsCacheCount = _appointmentsByDate.length;
    final blockedTimesCacheCount = _blockedTimesByDate.length;
    final currentAppointmentsCount = _appointments.length;
    final staffCount = _availableStaff.length;
    final servicesCount = _services.length;

    // Clear all client-related caches
    clientsCache.clear();
    petsCache.clear();
    clientPetsCache.clear();

    // Clear all appointment-related caches
    _appointments.clear();
    _appointmentsByDate.clear();
    _blockedTimesByDate.clear();
    _appointmentsError = null;

    // Clear staff data (will be reloaded for new salon)
    _availableStaff.clear();
    _selectedStaff.clear(); // This will be restored after loading new salon staff
    _staffError = null;

    // Clear services data (will be reloaded for new salon)
    _services.clear();
    _servicesCache.clear();
    _servicesError = null;

    // Clear staff working hours cache (salon-specific data)
    final staffWorkingHoursCacheCount = _staffWorkingHoursCache.length;
    clearStaffWorkingHoursCache();

    // Reset filters and search
    _searchQuery = '';
    _selectedServiceFilter = '';
    _selectedStatusFilter = '';

    // Notify listeners that all data has been cleared
    notifyListeners();

    DebugLogger.logVerbose('✅ CalendarProvider: Cleared $clientsCacheCount clients, $petsCacheCount pets, $clientPetsCacheCount client-pet associations');
    DebugLogger.logVerbose('✅ CalendarProvider: Cleared $currentAppointmentsCount current appointments, $appointmentsCacheCount cached appointment dates');
    DebugLogger.logVerbose('✅ CalendarProvider: Cleared $blockedTimesCacheCount cached block time dates');
    DebugLogger.logVerbose('✅ CalendarProvider: Cleared $staffCount staff members, $servicesCount services');
    DebugLogger.logVerbose('✅ CalendarProvider: Cleared $staffWorkingHoursCacheCount staff working hours cache entries');
    DebugLogger.logVerbose('🔔 CalendarProvider: UI notified of cache clearing');
    DebugLogger.logVerbose('✅ CalendarProvider: All cache clearing completed for salon switch');
  }

  /// Reload all data for the new salon after switching
  Future<void> reloadDataForNewSalon(String newSalonId) async {
    DebugLogger.logVerbose('🔄 CalendarProvider: Reloading data for new salon: $newSalonId');

    try {
      // Clear staff working hours cache for new salon
      if (ApiConfig.enableVerboseLogging) {
        DebugLogger.logVerbose('🔄 CalendarProvider: Clearing staff working hours cache for new salon...');
      }
      clearStaffWorkingHoursCache();

      // Load staff first (required for other operations)
      if (ApiConfig.enableVerboseLogging) {
        DebugLogger.logVerbose('🔄 CalendarProvider: Loading staff for new salon...');
      }
      await loadStaff();

      // Load services
      if (ApiConfig.enableVerboseLogging) {
        DebugLogger.logVerbose('🔄 CalendarProvider: Loading services for new salon...');
      }
      await loadServices();

      // Load working hours
      if (ApiConfig.enableVerboseLogging) {
        DebugLogger.logVerbose('🔄 CalendarProvider: Loading working hours for new salon...');
      }
      await loadWorkingHours();

      // Defer loading appointments and blocked times to reduce startup noise
      // These will be loaded when the calendar view is actually accessed
      if (ApiConfig.enableVerboseLogging) {
        DebugLogger.logVerbose('✅ CalendarProvider: Essential data reloaded for new salon (appointments deferred)');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        DebugLogger.logVerbose('❌ CalendarProvider: Error reloading data for new salon: $e');
      }
    }
  }

  // Get business hours - dynamically adjusts based on working hours configuration
  Map<String, dynamic> getBusinessHours() {
    // Trigger loading of working hours data if dynamic range is enabled (async, don't wait)
    if (_useDynamicTimeRange) {
      ensureWorkingHoursDataForDynamicRange().catchError((e) {
        DebugLogger.logVerbose('⚠️ Failed to load working hours data for dynamic range: $e');
      });
    }

    // Calculate dynamic time range based on staff working hours if enabled
    if (_useDynamicTimeRange && _staffWorkingHoursCache.isNotEmpty) {
      return _calculateDynamicBusinessHours();
    }

    // Fallback to salon working hours if available
    if (_useDynamicTimeRange && _workingHoursSettings != null) {
      return _calculateSalonBusinessHours();
    }

    // Default wide range for maximum flexibility (legacy behavior)
    return {
      'openTime': 6,   // 6 AM wide range for full-screen calendar
      'closeTime': 22, // 10 PM wide range for full-screen calendar
      'workDays': [1, 2, 3, 4, 5, 6, 7], // All days for staff-based scheduling
      'lunchBreak': {
        'start': 25, // Disabled for staff-based scheduling
        'end': 25,   // Disabled for staff-based scheduling
      },
    };
  }

  /// Calculate business hours based on staff working schedules
  Map<String, dynamic> _calculateDynamicBusinessHours() {
    int earliestStart = 24; // Initialize to end of day
    int latestEnd = 0;     // Initialize to start of day
    Set<int> workingDays = {};

    // Analyze all staff schedules to find actual business hours
    for (final staffSettings in _staffWorkingHoursCache.values) {
      for (final entry in staffSettings.weeklySchedule.entries) {
        final daySchedule = entry.value;
        if (daySchedule.isWorkingDay && daySchedule.startTime != null && daySchedule.endTime != null) {
          // Add this day to working days
          final dayNumber = _getDayNumber(entry.key);
          if (dayNumber != null) {
            workingDays.add(dayNumber);
          }

          // Check for earliest start time
          final startHour = int.tryParse(daySchedule.startTime!.split(':')[0]) ?? 9;
          if (startHour < earliestStart) {
            earliestStart = startHour;
          }

          // Check for latest end time
          final endHour = int.tryParse(daySchedule.endTime!.split(':')[0]) ?? 18;
          if (endHour > latestEnd) {
            latestEnd = endHour;
          }
        }
      }
    }

    // Apply calculated hours with padding and minimum range
    return _applyTimeRangeConstraints(earliestStart, latestEnd, workingDays);
  }

  /// Calculate business hours based on salon working hours settings
  Map<String, dynamic> _calculateSalonBusinessHours() {
    if (_workingHoursSettings == null) {
      return _getDefaultBusinessHours();
    }

    int earliestStart = 24;
    int latestEnd = 0;
    Set<int> workingDays = {};

    // Analyze salon weekly schedule
    for (final entry in _workingHoursSettings!.weeklySchedule.entries) {
      final daySchedule = entry.value;
      if (daySchedule.isWorkingDay && daySchedule.startTime != null && daySchedule.endTime != null) {
        // Add this day to working days
        final dayNumber = _getDayNumber(entry.key);
        if (dayNumber != null) {
          workingDays.add(dayNumber);
        }

        // Check for earliest start time - parse from string format "HH:mm"
        final startHour = int.tryParse(daySchedule.startTime!.split(':')[0]) ?? 9;
        if (startHour < earliestStart) {
          earliestStart = startHour;
        }

        // Check for latest end time - parse from string format "HH:mm"
        final endHour = int.tryParse(daySchedule.endTime!.split(':')[0]) ?? 18;
        if (endHour > latestEnd) {
          latestEnd = endHour;
        }
      }
    }

    return _applyTimeRangeConstraints(earliestStart, latestEnd, workingDays);
  }

  /// Apply time range constraints (padding, minimum range) and return business hours
  Map<String, dynamic> _applyTimeRangeConstraints(int earliestStart, int latestEnd, Set<int> workingDays) {
    // Ensure we have reasonable defaults if no valid hours were found
    if (earliestStart == 24 || latestEnd == 0) {
      DebugLogger.logVerbose('📅 CalendarProvider: No valid working hours found, using defaults');
      return _getDefaultBusinessHours();
    }

    // Apply padding
    int openTime = (earliestStart - _timeRangePaddingHours).clamp(0, 23);
    int closeTime = (latestEnd + _timeRangePaddingHours).clamp(1, 24);

    // Ensure minimum time range
    final currentRange = closeTime - openTime;
    if (currentRange < _minimumTimeRangeHours) {
      final additionalHours = _minimumTimeRangeHours - currentRange;
      final hoursToAddBefore = additionalHours ~/ 2;
      final hoursToAddAfter = additionalHours - hoursToAddBefore;

      openTime = (openTime - hoursToAddBefore).clamp(0, 23);
      closeTime = (closeTime + hoursToAddAfter).clamp(1, 24);
    }

    // Ensure we have at least some working days
    final List<int> workDays = workingDays.isEmpty
        ? [1, 2, 3, 4, 5, 6] // Default to Monday-Saturday
        : workingDays.toList()..sort();

    return {
      'openTime': openTime,
      'closeTime': closeTime,
      'workDays': workDays,
      'lunchBreak': {
        'start': 25, // Disabled for staff-based scheduling
        'end': 25,   // Disabled for staff-based scheduling
      },
    };
  }

  /// Get default business hours when no working hours are available
  Map<String, dynamic> _getDefaultBusinessHours() {
    return {
      'openTime': 6,   // 6 AM default
      'closeTime': 22, // 10 PM default
      'workDays': [1, 2, 3, 4, 5, 6, 7], // All days
      'lunchBreak': {
        'start': 25, // Disabled
        'end': 25,   // Disabled
      },
    };
  }

  // Helper method to convert day name to weekday number
  int? _getDayNumber(String dayName) {
    switch (dayName.toLowerCase()) {
      case 'monday': return 1;
      case 'tuesday': return 2;
      case 'wednesday': return 3;
      case 'thursday': return 4;
      case 'friday': return 5;
      case 'saturday': return 6;
      case 'sunday': return 7;
      default: return null;
    }
  }

  // Check if a time slot is available
  Future<bool> isTimeSlotAvailable(DateTime start, DateTime end, [String? excludeAppointmentId]) async {
    return await _calendarService.isTimeSlotAvailable(start, end, excludeAppointmentId);
  }



  // Get a client by ID (with caching)
  Future<Client?> getClientById(String clientId) async {
    // Check if client is already in cache
    if (clientsCache.containsKey(clientId)) {
      return clientsCache[clientId];
    }

    _isLoadingClientData = true;
    _clientDataError = null;
    notifyListeners();

    try {
      final client = await _calendarService.getClientById(clientId);
      if (client != null) {
        clientsCache[clientId] = client;
      }
      _isLoadingClientData = false;
      notifyListeners();
      return client;
    } catch (e) {
      _isLoadingClientData = false;
      _clientDataError = 'Failed to load client data: ${e.toString()}';
      notifyListeners();
      return null;
    }
  }

  // Get a pet by ID (with caching)
  Future<Pet?> getPetById(String petId) async {
    // Check if pet is already in cache
    if (petsCache.containsKey(petId)) {
      return petsCache[petId];
    }

    _isLoadingClientData = true;
    _clientDataError = null;
    notifyListeners();

    try {
      final pet = await _calendarService.getPetById(petId);
      if (pet != null) {
        petsCache[petId] = pet;
      }
      _isLoadingClientData = false;
      notifyListeners();
      return pet;
    } catch (e) {
      _isLoadingClientData = false;
      _clientDataError = 'Failed to load pet data: ${e.toString()}';
      notifyListeners();
      return null;
    }
  }

  // Get all pets for a client (with caching)
  Future<List<Pet>> getPetsForClient(String clientId) async {
    DebugLogger.logVerbose('🔄 CalendarProvider: getPetsForClient called for client: $clientId');

    // Check if client's pets are already in cache
    if (clientPetsCache.containsKey(clientId)) {
      final cachedPets = clientPetsCache[clientId]!;
      DebugLogger.logVerbose('📦 CalendarProvider: Found ${cachedPets.length} cached pets for client $clientId');
      return cachedPets;
    }

    DebugLogger.logVerbose('🔄 CalendarProvider: No cached pets found, fetching from service...');
    _isLoadingClientData = true;
    _clientDataError = null;
    notifyListeners();

    try {
      final pets = await _calendarService.getPetsForClient(clientId);
      DebugLogger.logVerbose('📡 CalendarProvider: Service returned ${pets.length} pets for client $clientId');

      clientPetsCache[clientId] = pets;

      // Also cache individual pets
      for (final pet in pets) {
        petsCache[pet.id] = pet;
        DebugLogger.logVerbose('📦 CalendarProvider: Cached pet ${pet.name} (${pet.id})');
      }

      _isLoadingClientData = false;
      notifyListeners();
      return pets;
    } catch (e) {
      DebugLogger.logVerbose('❌ CalendarProvider: Error loading pets for client $clientId: $e');
      _isLoadingClientData = false;
      _clientDataError = 'Failed to load client pets: ${e.toString()}';
      notifyListeners();
      return [];
    }
  }

  // Force refresh pets for a client (bypass cache)
  Future<List<Pet>> refreshPetsForClient(String clientId) async {
    DebugLogger.logVerbose('🔄 CalendarProvider: Force refreshing pets for client: $clientId');

    // Clear cache for this client
    clientPetsCache.remove(clientId);

    // Fetch fresh data
    return await getPetsForClient(clientId);
  }

  // Get breeds for a specific species
  Future<List<String>> getBreedsForSpecies(String species) async {
    try {
      return await BreedService.getCachedBreedsBySpecies(species);
    } catch (e) {
      DebugLogger.logError('Error fetching breeds for species $species: $e');
      // Return fallback breeds
      return ['Metis', 'Necunoscut'];
    }
  }

  // Search clients by phone number
  Future<List<Client>> searchClientsByPhone(String phoneNumber) async {
    try {
      if (phoneNumber.isEmpty) return [];

      // Use the ClientService to search by phone
      final response = await ClientService.searchClients(phoneNumber);

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        DebugLogger.logError('Error searching clients by phone: ${response.error}');
        return [];
      }
    } catch (e) {
      DebugLogger.logError('Error searching clients by phone: $e');
      return [];
    }
  }

  // Staff management methods
  Future<void> loadStaff() async {
    _isLoadingStaff = true;
    _staffError = null;
    notifyListeners();

    try {
      final response = await StaffService.getCurrentSalonStaff(activeOnly: true);
      DebugLogger.logVerbose('🔍 Staff service response: success=${response.success}, data=${response.data}');

      if (response.success && response.data != null) {
        _availableStaff = response.data!.activeStaff;

        // Ensure proper slot insertion order by sorting staff
        _sortStaffByCreationOrder();

        DebugLogger.logVerbose('🔍 Loaded ${_availableStaff.length} staff members');

        // Debug log each staff member with nickname info
        for (final staff in _availableStaff) {
          DebugLogger.logVerbose('🔍 Staff: ${staff.name} (${staff.id}) - Display: ${staff.displayName} - Role: ${staff.groomerRole}');
        }

        // Try to restore saved staff selection for this salon first
        final currentSalonId = await AuthService.getCurrentSalonId();
        if (currentSalonId != null) {
          _restoreStaffSelectionForSalon(currentSalonId);
        }

        // If no staff are selected after restoration, select the current user by default
        if (_selectedStaff.isEmpty) {
          final currentUserId = await AuthService.getCurrentUserId();
          if (currentUserId != null &&
              _availableStaff.any((s) => s.id == currentUserId)) {
            _selectedStaff = [currentUserId];
            DebugLogger.logVerbose('🔍 Auto-selected current user: $_selectedStaff');
          } else {
            _selectedStaff = _availableStaff.map((s) => s.id).toList();
            DebugLogger.logVerbose('🔍 Auto-selected all staff (fallback): $_selectedStaff');
          }
        }
        _staffError = null;
      } else {
        DebugLogger.logError('❌ Failed to load staff: ${response.error}');
        _staffError = response.error ?? 'Failed to load staff';
        _availableStaff = [];
        _selectedStaff = [];
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ Error loading staff: $e');
      _staffError = 'Error loading staff: $e';
      _availableStaff = [];
      _selectedStaff = [];
    } finally {
      _isLoadingStaff = false;
      notifyListeners();
    }
  }

  /// Force refresh staff data (useful when staff info like nicknames are updated)
  Future<void> refreshStaffData() async {
    DebugLogger.logVerbose('🔄 CalendarProvider.refreshStaffData() called - forcing staff reload');
    await loadStaff();
  }

  /// Ensure proper slot insertion order by sorting staff
  void _sortStaffByCreationOrder() {
    _availableStaff.sort((a, b) {
      // Identify slots vs regular staff
      final aIsSlot = a.notes?.contains('SLOT_FOR:') ?? false;
      final bIsSlot = b.notes?.contains('SLOT_FOR:') ?? false;

      // If one is a slot and the other isn't, regular staff comes first
      if (aIsSlot && !bIsSlot) return 1;
      if (!aIsSlot && bIsSlot) return -1;

      // For slots, extract and compare slot numbers to ensure proper order
      if (aIsSlot && bIsSlot) {
        final aSlotNumber = _extractSlotNumber(a.notes);
        final bSlotNumber = _extractSlotNumber(b.notes);

        // If both have slot numbers, sort by slot number
        if (aSlotNumber != null && bSlotNumber != null) {
          return aSlotNumber.compareTo(bSlotNumber);
        }
      }

      // For regular staff or when slot numbers aren't available, sort by join date
      return a.joinedAt.compareTo(b.joinedAt);
    });

    // Debug log the final order
    DebugLogger.logVerbose('📋 Staff order after sorting:');
    for (int i = 0; i < _availableStaff.length; i++) {
      final staff = _availableStaff[i];
      final isSlot = staff.notes?.contains('SLOT_FOR:') ?? false;
      final slotNumber = isSlot ? _extractSlotNumber(staff.notes) : null;
      DebugLogger.logVerbose('   ${i + 1}. ${staff.displayName} ${isSlot ? "(Slot $slotNumber)" : "(Regular)"}');
    }
  }

  /// Extract slot number from notes string
  int? _extractSlotNumber(String? notes) {
    if (notes == null) return null;
    final match = RegExp(r'SLOT_NUMBER:(\d+)').firstMatch(notes);
    return match != null ? int.tryParse(match.group(1)!) : null;
  }

  void selectStaff(String staffId) {
    if (!_selectedStaff.contains(staffId)) {
      _selectedStaff.add(staffId);
      notifyListeners();
    }
  }

  /// Check if selecting a staff member would exceed day view limit
  bool canSelectStaffForDayView(String staffId) {
    if (_selectedStaff.contains(staffId)) {
      return true; // Already selected
    }
    return _selectedStaff.length < 3; // Max 3 for day view
  }

  /// Select staff with day view limit enforcement
  void selectStaffForDayView(String staffId) {
    if (!_selectedStaff.contains(staffId) && _selectedStaff.length < 3) {
      _selectedStaff.add(staffId);
      notifyListeners();
    }
  }

  /// Get the maximum number of staff that can be selected for day view
  int get maxStaffForDayView => 3;

  /// Check if current selection exceeds day view limit
  bool get exceedsDayViewLimit => _selectedStaff.length > 3;

  void deselectStaff(String staffId) {
    _selectedStaff.remove(staffId);
    notifyListeners();
  }

  void selectAllStaff() {
    _selectedStaff = _availableStaff.map((s) => s.id).toList();
    notifyListeners();
  }

  /// Select all staff with day view limit consideration
  void selectAllStaffForDayView() {
    _selectedStaff = _availableStaff.take(3).map((s) => s.id).toList();
    notifyListeners();
  }

  Future<void> selectCurrentUserOnly() async {
    final currentUserId = await AuthService.getCurrentUserId();
    if (currentUserId != null &&
        _availableStaff.any((s) => s.id == currentUserId)) {
      _selectedStaff = [currentUserId];
      notifyListeners();
    }
  }

  void clearStaffSelection() {
    _selectedStaff.clear();
    notifyListeners();
  }

  StaffResponse? getStaffById(String staffId) {
    try {
      return _availableStaff.firstWhere((s) => s.id == staffId);
    } catch (e) {
      return null;
    }
  }

  Color getStaffColor(String staffId) {
    return AppTheme.getStaffColor(staffId);
  }

  // Working hours management
  Future<void> loadWorkingHours() async {
    _isLoadingWorkingHours = true;
    _workingHoursError = null;
    notifyListeners();

    try {
      final response = await WorkingHoursService.getWorkingHours();
      if (response.success && response.data != null) {
        _workingHoursSettings = response.data!;
        _workingHoursError = null;

        for (final closure in _workingHoursSettings!.customClosures) {
          DebugLogger.logVerbose('   🚫 Closure: ${closure.startDate.toIso8601String().split('T')[0]}-${closure.endDate.toIso8601String().split('T')[0]} - ${closure.reason}');
        }

        // Notify listeners so calendar views refresh with new business hours
        notifyListeners();
      } else {
        _workingHoursError = response.error ?? 'Failed to load working hours';
        _workingHoursSettings = null;
        DebugLogger.logVerbose('❌ Failed to load working hours: $_workingHoursError');
      }
    } catch (e) {
      _workingHoursError = 'Error loading working hours: $e';
      _workingHoursSettings = null;
      DebugLogger.logVerbose('❌ Error loading working hours: $e');
    } finally {
      _isLoadingWorkingHours = false;
      notifyListeners();
    }
  }

  // Method to refresh calendar when working hours change
  Future<void> refreshCalendarAfterWorkingHoursChange() async {
    await loadWorkingHours();
    // Clear staff working hours cache to ensure fresh data
    clearStaffWorkingHoursCache();
    // The notifyListeners() in loadWorkingHours will trigger calendar rebuild
    notifyListeners();
  }

  /// Ensure working hours data is loaded for dynamic time range calculation
  Future<void> ensureWorkingHoursDataForDynamicRange() async {
    if (!_useDynamicTimeRange) {
      return; // Dynamic range is disabled, no need to load data
    }

    // Load salon working hours if not available
    if (_workingHoursSettings == null && !_isLoadingWorkingHours) {
      await loadWorkingHours();
    }

    // Load staff working hours for selected staff if cache is empty
    if (_staffWorkingHoursCache.isEmpty && _selectedStaff.isNotEmpty) {
      await loadStaffWorkingHoursOnDemand(_selectedStaff, reason: 'Dynamic time range calculation');
    }
  }



  bool isWorkingDay(DateTime date) {
    // Only check for holidays and custom closures, not salon working days
    // Staff schedules will determine actual working days
    return !RomanianHolidays.isHoliday(date) && !hasCustomClosure(date);
  }

  /// Check if the salon is closed on a specific date
  bool isClosedOnDate(DateTime date) {
    // Only check for holidays and custom closures, not salon working days
    // Staff schedules will determine actual working days
    return RomanianHolidays.isHoliday(date) || hasCustomClosure(date);
  }

  /// Check if a date is a Romanian holiday (immediate, no async)
  bool isRomanianHoliday(DateTime date) {
    final isHoliday = RomanianHolidays.isHoliday(date);
    if (isHoliday) {
    }
    return isHoliday;
  }

  /// Check if a date has a custom closure (immediate, no async)
  bool hasCustomClosure(DateTime date) {
    if (_workingHoursSettings == null) {
      // Only log warning if we're not currently loading working hours
      if (!_isLoadingWorkingHours) {
      }
      return false;
    }

    final hasCustom = _workingHoursSettings!.customClosures.any((c) =>
      !date.isBefore(c.startDate) && !date.isAfter(c.endDate)
    );
    return hasCustom;
  }

  /// Ensure working hours are loaded before checking closures
  Future<void> ensureWorkingHoursLoaded() async {
    if (_workingHoursSettings == null && !_isLoadingWorkingHours) {
      DebugLogger.logVerbose('🔄 Working hours not loaded, loading now...');
      await loadWorkingHours();
    }
  }

  /// Get comprehensive closure information for a date (immediate, no async)
  ({
    bool isRomanianHoliday,
    bool hasCustomClosure,
    bool isSalonClosed,
    String? closureReason,
    String? holidayName
  }) getDateClosureInfo(DateTime date) {

    // Trigger working hours loading if not available (async, don't wait)
    if (_workingHoursSettings == null && !_isLoadingWorkingHours) {
      loadWorkingHours().catchError((e) {
      });
    }

    final isHoliday = isRomanianHoliday(date);
    final hasCustom = hasCustomClosure(date);
    final isClosed = isClosedOnDate(date);

    String? reason;
    String? holidayName;

    if (isHoliday) {
      final holiday = RomanianHolidays.getHolidayForDate(date);
      holidayName = holiday?.name;
      reason = 'Sărbătoare legală: ${holidayName ?? 'Sărbătoare'}';
    } else if (hasCustom && _workingHoursSettings != null) {
      final closure = _workingHoursSettings!.customClosures.firstWhere(
        (c) => c.date.year == date.year &&
               c.date.month == date.month &&
               c.date.day == date.day,
        orElse: () => CustomClosure(reason: 'Închis', startDate: date, endDate: date),
      );
      reason = closure.reason;
    }

    final result = (
      isRomanianHoliday: isHoliday,
      hasCustomClosure: hasCustom,
      isSalonClosed: isClosed,
      closureReason: reason,
      holidayName: holidayName,
    );

    return result;
  }

  /// Check if staff member is working on date (synchronous with cache)
  bool isStaffWorkingOnDateSync(String staffId, DateTime date) {

    final settings = _staffWorkingHoursCache[staffId];
    if (settings == null) {
      // DO NOT trigger async load automatically to prevent API spam
      // Staff working hours should be loaded proactively when needed

      // Return false when staff settings aren't available - no salon schedule fallback
      return false;
    }

    final staffWorking = settings.isAvailableOnDate(date);

    // Additional logging for staff-specific constraints
    if (!staffWorking) {
      final dayOfWeek = _getDayOfWeekString(date);
      final daySchedule = settings.getScheduleForDay(dayOfWeek);
      if (daySchedule == null || !daySchedule.isWorkingDay) {
      } else {
        // Check for custom closures or holidays
        settings.customClosures.any((closure) =>
            !date.isBefore(closure.startDate) && !date.isAfter(closure.endDate));
      }
    }

    return staffWorking;
  }

  /// Helper method to get day of week string
  String _getDayOfWeekString(DateTime date) {
    const dayNames = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    return dayNames[date.weekday - 1];
  }

  /// Get visual styling information for a time slot
  ({
    bool isAvailable,
    bool isGreyedOut,
    bool isInteractive,
    String? disabledReason
  }) getTimeSlotStyling(DateTime dateTime, String? staffId) {
    final date = DateTime(dateTime.year, dateTime.month, dateTime.day);

    // Check if time slot is in the past
    final isPastSlot = dateTime.isBefore(DateTime.now());
    if (isPastSlot) {
      return (
        isAvailable: false,
        isGreyedOut: true,
        isInteractive: false,
        disabledReason: 'Slot din trecut',
      );
    }

    final closureInfo = getDateClosureInfo(date);

    // Check if salon is closed
    if (closureInfo.isSalonClosed) {
      return (
        isAvailable: false,
        isGreyedOut: true,
        isInteractive: false,
        disabledReason: closureInfo.closureReason,
      );
    }

    // Check staff availability if staffId provided
    if (staffId != null) {
      // If we don't yet have staff working-hours cached for this staff member,
      // avoid returning a pessimistic "unavailable" result that causes a UI
      // flash (slots briefly rendered with the unavailable color). Instead,
      // return a neutral visual state: show the normal surface color but keep
      // the slot non-interactive until real data arrives. This prevents the
      // bad visual flash while still avoiding accidental user actions.
      final staffSettings = _staffWorkingHoursCache[staffId];
      if (staffSettings == null) {
        return (
          isAvailable: true, // show neutral/available surface to avoid flash
          isGreyedOut: false,
          isInteractive: false, // disable interactions until data loads
          disabledReason: 'Se încarcă...',
        );
      }

      // We have cached staff settings -> evaluate actual availability
      final staffWorking = isStaffWorkingOnDateSync(staffId, date);
      if (!staffWorking) {
        return (
          isAvailable: false,
          isGreyedOut: true,
          isInteractive: false,
          disabledReason: 'Personal indisponibil',
        );
      }

      // Check staff-specific working hours for this time slot
      final isStaffAvailableAtTime = staffSettings.isAvailableAtTime(dateTime);

      if (!isStaffAvailableAtTime) {
        return (
          isAvailable: false,
          isGreyedOut: true,
          isInteractive: false,
          disabledReason: 'Personal indisponibil la această oră',
        );
      }

      // Staff is available at this time - no need to check salon hours
      return (
        isAvailable: true,
        isGreyedOut: false,
        isInteractive: true,
        disabledReason: null,
      );
    }

    // Only apply salon business hours when no staff is specified
    final businessHours = getBusinessHours();
    final openTime = businessHours['openTime'] as int;
    final closeTime = businessHours['closeTime'] as int;
    final lunchStart = businessHours['lunchBreak']['start'] as int;
    final lunchEnd = businessHours['lunchBreak']['end'] as int;

    final hour = dateTime.hour;
    final isBusinessHour = hour >= openTime && hour < closeTime;
    final isLunchBreak = hour >= lunchStart && hour < lunchEnd;


    if (!isBusinessHour) {
      return (
        isAvailable: false,
        isGreyedOut: true,
        isInteractive: false,
        disabledReason: 'În afara programului salonului',
      );
    }

    if (isLunchBreak) {
      return (
        isAvailable: false,
        isGreyedOut: true,
        isInteractive: false,
        disabledReason: 'Pauza de prânz',
      );
    }

    return (
      isAvailable: true,
      isGreyedOut: false,
      isInteractive: true,
      disabledReason: null,
    );
  }

  // Service management methods
  Future<void> loadServices() async {
    _isLoadingServices = true;
    _servicesError = null;
    notifyListeners();

    try {
      final response = await ServiceManagementService.getServices(isActive: true);
      if (response.success && response.data != null) {
        _services = response.data!;
        // Update cache
        _servicesCache.clear();
        for (final service in _services) {
          _servicesCache[service.id] = service;
        }
        _servicesError = null;
      } else {
        _servicesError = response.error ?? 'Failed to load services';
        _services = [];
        _servicesCache.clear();
      }
    } catch (e) {
      _servicesError = 'Error loading services: $e';
      _services = [];
      _servicesCache.clear();
    } finally {
      _isLoadingServices = false;
      notifyListeners();
    }
  }

  Service? getServiceById(String serviceId) {
    return _servicesCache[serviceId];
  }

  Service? getServiceByName(String serviceName) {
    try {
      return _services.firstWhere((s) => s.name == serviceName);
    } catch (e) {
      return null;
    }
  }

  // Get service details in the format expected by appointment widgets
  Map<String, Map<String, dynamic>> getServiceDetails() {
    final Map<String, Map<String, dynamic>> details = {};

    for (final service in _services) {
      details[service.name] = {
        'id': service.id,
        'duration': service.duration,
        'price': service.price,
        if (service.sizePrices != null) 'sizePrices': service.sizePrices,
        'description': service.description,
        'category': service.category,
        if (service.color != null) 'color': service.color,
      };
    }

    return details;
  }

  // Get available service names for appointment form
  List<String> getAvailableServiceNames() {
    return _services.where((s) => s.isActive).map((s) => s.name).toList();
  }

  // Get service IDs from service names (for backend compatibility)
  List<String> getServiceIds(List<String> serviceNames) {
    final List<String> serviceIds = [];
    for (final serviceName in serviceNames) {
      final service = getServiceByName(serviceName);
      if (service != null) {
        serviceIds.add(service.id);
      }
    }
    return serviceIds;
  }

  // Get service name to ID mapping (for appointment form)
  Map<String, String> getServiceNameToIdMap() {
    final Map<String, String> mapping = {};
    for (final service in _services) {
      mapping[service.name] = service.id;
    }
    return mapping;
  }

  final Map<String, StaffWorkingHoursSettings> _staffWorkingHoursCache = {};
  final Map<String, DateTime> _staffWorkingHoursCacheTimestamp = {};
  static const Duration _cacheValidityDuration = Duration(minutes: 30); // Increased cache duration

  // Track ongoing API requests to prevent duplicates
  final Set<String> _ongoingStaffRequests = {};
  bool _isBatchRequestInProgress = false;

  /// Load staff working hours in batch (OPTIMIZED - single API call for multiple staff)
  Future<void> _loadStaffWorkingHoursBatch(List<String> staffIds) async {
    final now = DateTime.now();
    DebugLogger.logVerbose('🚀 Loading staff working hours in batch for ${staffIds.length} staff members');

    try {
      final response = await StaffWorkingHoursService.getBatchStaffWorkingHours(staffIds);

      if (response.success && response.data != null) {
        final batchResponse = response.data!;
        final staffWorkingHours = batchResponse.staffWorkingHours;

        DebugLogger.logVerbose('✅ Batch API response received for ${staffWorkingHours.length} staff members');

        // Cache all the received data
        for (final staffSettings in staffWorkingHours) {
          _staffWorkingHoursCache[staffSettings.staffId] = staffSettings;
          _staffWorkingHoursCacheTimestamp[staffSettings.staffId] = now;
        }

        DebugLogger.logVerbose('🚀 Batch loading completed successfully - ${staffWorkingHours.length}/${staffIds.length} staff loaded');
      } else {
        throw Exception(response.error ?? 'Failed to parse response');
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ Batch loading failed with exception: $e');
      rethrow;
    }
  }

  /// Get staff working hours with caching to prevent API spam
  Future<StaffWorkingHoursSettings?> _getStaffWorkingHoursWithCache(String staffId) async {
    final now = DateTime.now();
    DebugLogger.logVerbose('👤 Getting staff working hours for: $staffId');

    // Check if we have cached data that's still valid
    if (_staffWorkingHoursCache.containsKey(staffId) &&
        _staffWorkingHoursCacheTimestamp.containsKey(staffId)) {
      final cacheTime = _staffWorkingHoursCacheTimestamp[staffId]!;
      final cacheAge = now.difference(cacheTime);
      if (cacheAge < _cacheValidityDuration) {
        DebugLogger.logVerbose('📦 Using cached staff working hours (age: ${cacheAge.inMinutes}min)');
        return _staffWorkingHoursCache[staffId];
      } else {
        DebugLogger.logVerbose('⏰ Cache expired for staff $staffId (age: ${cacheAge.inMinutes}min)');
      }
    } else {
      DebugLogger.logVerbose('📭 No cached data for staff $staffId');
    }

    try {
      DebugLogger.logVerbose('🌐 Fetching fresh staff working hours from API...');
      final response = await StaffWorkingHoursService.getStaffWorkingHours(staffId);
      if (response.success && response.data != null) {
        _staffWorkingHoursCache[staffId] = response.data!;
        _staffWorkingHoursCacheTimestamp[staffId] = now;

        final settings = response.data!;
        DebugLogger.logVerbose('✅ Staff working hours cached successfully');
        DebugLogger.logVerbose('📅 Staff custom closures count: ${settings.customClosures.length}');
        return response.data!;
      } else {
        DebugLogger.logError('❌ Failed to get staff working hours: ${response.error}');
      }
    } catch (e) {
      DebugLogger.logError('❌ Error getting staff working hours for $staffId: $e');
    }

    return null;
  }

  /// Check if staff member is working on a specific date
  Future<bool> isStaffWorkingOnDate(String staffId, DateTime date) async {
    final settings = await _getStaffWorkingHoursWithCache(staffId);
    if (settings == null) {
      return false; // Default to not working if can't get settings
    }

    return settings.isAvailableOnDate(date);
  }

  /// Check if staff member is available at a specific time
  Future<bool> isStaffAvailableAtTime(String staffId, DateTime dateTime) async {
    final settings = await _getStaffWorkingHoursWithCache(staffId);
    if (settings == null) {
      return false; // Default to not available if can't get settings
    }

    return settings.isAvailableAtTime(dateTime);
  }

  /// Clear staff working hours cache (useful when staff schedules are updated)
  void clearStaffWorkingHoursCache([String? specificStaffId]) {
    if (specificStaffId != null) {
      _staffWorkingHoursCache.remove(specificStaffId);
      _staffWorkingHoursCacheTimestamp.remove(specificStaffId);
      _ongoingStaffRequests.remove(specificStaffId);
      DebugLogger.logVerbose('🗑️ Cleared working hours cache for staff: $specificStaffId');
    } else {
      _staffWorkingHoursCache.clear();
      _staffWorkingHoursCacheTimestamp.clear();
      _ongoingStaffRequests.clear();
      _isBatchRequestInProgress = false;
      DebugLogger.logVerbose('🗑️ Cleared all staff working hours cache and ongoing requests');
    }
  }

  /// Handle real-time updates when salon or staff schedules change
  Future<void> handleScheduleUpdate({
    bool salonScheduleChanged = false,
    String? staffId,
    bool customClosuresChanged = false,
  }) async {
    final timestamp = DateTime.now().toIso8601String();
    DebugLogger.logVerbose('🔄 [$timestamp] Handling schedule update:');
    DebugLogger.logVerbose('   📊 Salon schedule changed: $salonScheduleChanged');
    DebugLogger.logVerbose('   👤 Staff ID: $staffId');
    DebugLogger.logVerbose('   🚫 Custom closures changed: $customClosuresChanged');

    if (salonScheduleChanged || customClosuresChanged) {
      // Reload salon working hours
      DebugLogger.logVerbose('🏢 [$timestamp] Reloading salon working hours...');
      await loadWorkingHours();
      DebugLogger.logVerbose('✅ [$timestamp] Salon working hours reloaded');
    }

    if (staffId != null) {
      // Clear specific staff cache and reload only that staff member
      DebugLogger.logVerbose('👤 [$timestamp] Refreshing staff cache for: $staffId');
      clearStaffWorkingHoursCache(staffId);
      // Load fresh staff data using optimized method
      await loadStaffWorkingHoursOnDemand([staffId], reason: 'Staff schedule update');
      DebugLogger.logVerbose('✅ [$timestamp] Staff cache refreshed for: $staffId');
    } else if (salonScheduleChanged) {
      // Clear all staff cache if salon schedule changed
      DebugLogger.logVerbose('👥 [$timestamp] Clearing all staff cache due to salon schedule change');
      clearStaffWorkingHoursCache();
      // Reload for currently selected staff only
      if (_selectedStaff.isNotEmpty) {
        await loadStaffWorkingHoursOnDemand(_selectedStaff, reason: 'Salon schedule change');
      }
      DebugLogger.logVerbose('✅ [$timestamp] All staff cache cleared and reloaded for selected staff');
    }

    // Force immediate UI update
    notifyListeners();
    DebugLogger.logVerbose('🎨 [$timestamp] UI refresh triggered - calendar views should update immediately');
    DebugLogger.logVerbose('✅ [$timestamp] Schedule update completed successfully');
  }

  /// Force refresh of staff working hours (useful for debugging)
  Future<void> forceRefreshStaffWorkingHours(String staffId) async {
    DebugLogger.logVerbose('🔄 Force refreshing staff working hours for: $staffId');
    clearStaffWorkingHoursCache(staffId);
    await _getStaffWorkingHoursWithCache(staffId);
    notifyListeners();
    DebugLogger.logVerbose('✅ Staff working hours force refreshed');
  }

  // Testing helper methods
  void setStaffCacheForTesting(String staffId, dynamic data) {
    _staffWorkingHoursCache[staffId] = data;
  }

  bool hasStaffCacheForTesting(String staffId) {
    return _staffWorkingHoursCache.containsKey(staffId);
  }

  /// Force complete calendar refresh (useful for debugging real-time updates)
  Future<void> forceCalendarRefresh({String? reason}) async {
    final timestamp = DateTime.now().toIso8601String();
    DebugLogger.logVerbose('🔄 [$timestamp] Force calendar refresh triggered${reason != null ? ' - Reason: $reason' : ''}');

    // Reload all working hours data
    await loadWorkingHours();

    // Clear all staff caches
    clearStaffWorkingHoursCache();

    // Reload staff working hours for currently selected staff
    final selectedStaffIds = _selectedStaff;
    if (selectedStaffIds.isNotEmpty) {
      await loadStaffWorkingHoursOnDemand(selectedStaffIds, reason: 'Force calendar refresh');
    }

    // Force UI update
    notifyListeners();

    DebugLogger.logVerbose('✅ [$timestamp] Force calendar refresh completed');
  }

  /// Refresh staff working hours when user explicitly requests it
  Future<void> refreshStaffWorkingHours({String? reason}) async {
    final timestamp = DateTime.now().toIso8601String();
    DebugLogger.logVerbose('🔄 [$timestamp] Refreshing staff working hours${reason != null ? ' - Reason: $reason' : ''}');

    // Clear all staff caches to force fresh data
    clearStaffWorkingHoursCache();

    // Reload for currently selected staff
    final selectedStaffIds = _selectedStaff;
    if (selectedStaffIds.isNotEmpty) {
      DebugLogger.logVerbose('🚀 [$timestamp] Using batch API for refresh of ${selectedStaffIds.length} staff members');
      await loadStaffWorkingHoursOnDemand(selectedStaffIds, reason: reason ?? 'User refresh');
    }

    // Force UI update
    notifyListeners();

    DebugLogger.logVerbose('✅ [$timestamp] Staff working hours refresh completed');
  }

  /// Test the new batch endpoint explicitly
  Future<void> testBatchStaffWorkingHours() async {
    final timestamp = DateTime.now().toIso8601String();
    DebugLogger.logVerbose('🧪 [$timestamp] Testing batch staff working hours endpoint');

    final selectedStaffIds = _selectedStaff;
    if (selectedStaffIds.isEmpty) {
      DebugLogger.logVerbose('⚠️ [$timestamp] No staff selected for batch test');
      return;
    }

    try {
      DebugLogger.logVerbose('🌐 [$timestamp] Testing batch endpoint with ${selectedStaffIds.length} staff: ${selectedStaffIds.join(', ')}');
      await _loadStaffWorkingHoursBatch(selectedStaffIds);
      DebugLogger.logVerbose('✅ [$timestamp] Batch endpoint test SUCCESSFUL!');

      // Verify cache was populated
      int cachedCount = 0;
      for (final staffId in selectedStaffIds) {
        if (_staffWorkingHoursCache.containsKey(staffId)) {
          cachedCount++;
        }
      }
      DebugLogger.logVerbose('📦 [$timestamp] Cache populated for $cachedCount/${selectedStaffIds.length} staff members');

    } catch (e) {
      DebugLogger.logVerbose('❌ [$timestamp] Batch endpoint test FAILED: $e');
      DebugLogger.logVerbose('🔄 [$timestamp] This indicates the backend batch endpoint may not be working correctly');
    }
  }

  /// Preload staff working hours for all active staff members
  Future<void> preloadStaffWorkingHours(List<String> staffIds) async {
    final timestamp = DateTime.now().toIso8601String();
    DebugLogger.logVerbose('🔄 [$timestamp] Preloading staff working hours for ${staffIds.length} staff members');

    final futures = staffIds.map((staffId) async {
      try {
        await _getStaffWorkingHoursWithCache(staffId);
        DebugLogger.logVerbose('✅ [$timestamp] Preloaded working hours for staff: $staffId');
      } catch (e) {
        DebugLogger.logVerbose('❌ [$timestamp] Failed to preload working hours for staff $staffId: $e');
      }
    });

    await Future.wait(futures);
    DebugLogger.logVerbose('✅ [$timestamp] Staff working hours preloading completed');
  }

  /// Load staff working hours only when explicitly needed (optimized approach)
  Future<void> loadStaffWorkingHoursOnDemand(List<String> staffIds, {String? reason}) async {
    DebugLogger.logVerbose('🚀 Loading staff working hours on demand for ${staffIds.length} staff members${reason != null ? ' - Reason: $reason' : ''}');

    // Check if a batch request is already in progress
    if (_isBatchRequestInProgress) {
      DebugLogger.logVerbose('⏳ Batch request already in progress - skipping duplicate call');
      return;
    }

    // Only load for staff that don't have valid cache and aren't already being requested
    final staffToLoad = <String>[];
    final now = DateTime.now();

    for (final staffId in staffIds) {
      // Skip if already being requested
      if (_ongoingStaffRequests.contains(staffId)) {
        DebugLogger.logVerbose('⏳ Staff $staffId already being requested - skipping');
        continue;
      }

      if (!_staffWorkingHoursCache.containsKey(staffId) ||
          !_staffWorkingHoursCacheTimestamp.containsKey(staffId)) {
        staffToLoad.add(staffId);
        DebugLogger.logVerbose('📭 Staff $staffId has no cache - will load');
      } else {
        final cacheTime = _staffWorkingHoursCacheTimestamp[staffId]!;
        final cacheAge = now.difference(cacheTime);
        if (cacheAge >= _cacheValidityDuration) {
          staffToLoad.add(staffId);
          DebugLogger.logVerbose('⏰ Staff $staffId cache expired (age: ${cacheAge.inMinutes}min) - will reload');
        } else {
          DebugLogger.logVerbose('📦 Staff $staffId has valid cache (age: ${cacheAge.inMinutes}min) - skipping');
        }
      }
    }

    if (staffToLoad.isEmpty) {
      DebugLogger.logVerbose('✅ All staff have valid cache or are being loaded - no API requests needed');
      return;
    }

    DebugLogger.logVerbose('🌐 Making API requests for ${staffToLoad.length} staff members: ${staffToLoad.join(', ')}');

    // Mark staff as being requested and set batch flag
    for (final staffId in staffToLoad) {
      _ongoingStaffRequests.add(staffId);
    }
    _isBatchRequestInProgress = true;

    try {
      // Try batch loading first
      if (staffToLoad.isNotEmpty) {
        try {
          DebugLogger.logVerbose('🚀 Attempting batch loading for ${staffToLoad.length} staff members');
          await _loadStaffWorkingHoursBatch(staffToLoad);
          DebugLogger.logVerbose('✅ Batch staff working hours loading completed successfully');
          return;
        } catch (e) {
          DebugLogger.logError('⚠️ Batch loading failed, falling back to individual requests: $e');
        }
      }

      // Fallback to individual requests
      final futures = staffToLoad.map((staffId) async {
        try {
          await _getStaffWorkingHoursWithCache(staffId);
          DebugLogger.logVerbose('✅ Loaded working hours for staff: $staffId');
        } catch (e) {
          DebugLogger.logVerbose('❌ Failed to load working hours for staff $staffId: $e');
        }
      });

      await Future.wait(futures);
      DebugLogger.logVerbose('✅ Staff working hours on-demand loading completed');
    } finally {
      // Clear ongoing requests and batch flag
      for (final staffId in staffToLoad) {
        _ongoingStaffRequests.remove(staffId);
      }
      _isBatchRequestInProgress = false;
    }
  }

  /// Get all cached staff IDs (useful for debugging)
  List<String> getCachedStaffIds() {
    return _staffWorkingHoursCache.keys.toList();
  }

  /// Get cached staff working hours (for fast validation)
  StaffWorkingHoursSettings? getStaffWorkingHours(String staffId) {
    return _staffWorkingHoursCache[staffId];
  }

  /// Get cache status for debugging
  Map<String, dynamic> getStaffCacheStatus() {
    final now = DateTime.now();
    final status = <String, dynamic>{};

    for (final staffId in _staffWorkingHoursCache.keys) {
      final cacheTime = _staffWorkingHoursCacheTimestamp[staffId];
      final age = cacheTime != null ? now.difference(cacheTime) : null;
      final isValid = age != null && age < _cacheValidityDuration;

      status[staffId] = {
        'cached': true,
        'cacheAge': age?.inMinutes,
        'isValid': isValid,
        'hasSettings': _staffWorkingHoursCache[staffId] != null,
      };
    }

    return status;
  }

  /// Handle staff schedule changes with real-time calendar updates
  Future<void> onStaffScheduleChanged(String staffId, {String? reason}) async {
    final timestamp = DateTime.now().toIso8601String();
    DebugLogger.logVerbose('📅 [$timestamp] Staff schedule changed for $staffId${reason != null ? ' - Reason: $reason' : ''}');

    // Clear cache for the specific staff member
    clearStaffWorkingHoursCache(staffId);
    DebugLogger.logVerbose('🗑️ [$timestamp] Cleared cache for staff: $staffId');

    // Reload the staff's working hours using optimized method
    try {
      await loadStaffWorkingHoursOnDemand([staffId], reason: reason ?? 'Staff schedule change');
      DebugLogger.logVerbose('✅ [$timestamp] Reloaded working hours for staff: $staffId');
    } catch (e) {
      DebugLogger.logVerbose('❌ [$timestamp] Failed to reload working hours for staff $staffId: $e');
    }

    // Trigger UI update
    notifyListeners();
    DebugLogger.logVerbose('🔄 [$timestamp] Calendar UI updated for staff schedule change');
  }

  /// Handle salon schedule changes with real-time calendar updates
  Future<void> onSalonScheduleChanged({String? reason}) async {
    final timestamp = DateTime.now().toIso8601String();
    DebugLogger.logVerbose('🏢 [$timestamp] Salon schedule changed${reason != null ? ' - Reason: $reason' : ''}');

    // Reload salon working hours
    await loadWorkingHours();
    DebugLogger.logVerbose('✅ [$timestamp] Reloaded salon working hours');

    // Clear all staff caches since salon changes might affect staff availability
    clearStaffWorkingHoursCache();
    DebugLogger.logVerbose('🗑️ [$timestamp] Cleared all staff working hours cache');

    // Reload for currently selected staff only
    if (_selectedStaff.isNotEmpty) {
      await loadStaffWorkingHoursOnDemand(_selectedStaff, reason: reason ?? 'Salon schedule change');
      DebugLogger.logVerbose('✅ [$timestamp] Reloaded working hours for selected staff');
    }

    // Trigger UI update
    notifyListeners();
    DebugLogger.logVerbose('🔄 [$timestamp] Calendar UI updated for salon schedule change');
  }

  /// Handle holiday/closure changes with real-time calendar updates
  Future<void> onHolidayClosureChanged({String? reason}) async {
    final timestamp = DateTime.now().toIso8601String();
    DebugLogger.logVerbose('🎄 [$timestamp] Holiday/closure changed${reason != null ? ' - Reason: $reason' : ''}');

    // Clear holiday cache (if we implement one in the future)
    // For now, holidays are calculated dynamically

    // Clear all staff caches since holidays affect all staff
    clearStaffWorkingHoursCache();
    DebugLogger.logVerbose('🗑️ [$timestamp] Cleared all staff working hours cache due to holiday change');

    // Reload for currently selected staff only
    if (_selectedStaff.isNotEmpty) {
      await loadStaffWorkingHoursOnDemand(_selectedStaff, reason: reason ?? 'Holiday/closure change');
      DebugLogger.logVerbose('✅ [$timestamp] Reloaded working hours for selected staff');
    }

    // Trigger UI update
    notifyListeners();
    DebugLogger.logVerbose('🔄 [$timestamp] Calendar UI updated for holiday/closure change');
  }

  /// Testing method to set working hours directly
  void setWorkingHoursForTesting(WorkingHoursSettings workingHours) {
    _workingHoursSettings = workingHours;
  }

  // Public getter for staff working hours settings
  StaffWorkingHoursSettings? getStaffWorkingHoursSettings(String staffId) {
    return _staffWorkingHoursCache[staffId];
  }

  // Public getter for staff working hours cache (for testing)
  Map<String, StaffWorkingHoursSettings> get staffWorkingHoursCache => Map.unmodifiable(_staffWorkingHoursCache);

  // Feature 4: Real-time color update methods
  void startColorUpdateTimer() {
    // Update colors every minute to reflect time-based status changes
    _colorUpdateTimer?.cancel();
    _colorUpdateTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      // Trigger a rebuild to update appointment colors
      notifyListeners();
    });
  }

  void stopColorUpdateTimer() {
    _colorUpdateTimer?.cancel();
    _colorUpdateTimer = null;
  }

  // Highlighted appointment methods for visual feedback after creation
  void highlightAppointment(String appointmentId, {Duration duration = const Duration(seconds: 2)}) {
    _highlightedAppointmentId = appointmentId;
    notifyListeners();

    // Clear highlight after specified duration
    _highlightTimer?.cancel();
    _highlightTimer = Timer(duration, () {
      clearHighlightedAppointment();
    });
  }

  void clearHighlightedAppointment() {
    _highlightedAppointmentId = null;
    _highlightTimer?.cancel();
    _highlightTimer = null;
    notifyListeners();
  }

  @override
  void dispose() {
    stopColorUpdateTimer();
    _highlightTimer?.cancel();
    _stopGoogleCalendarAutoRefresh();
    super.dispose();
  }
}

