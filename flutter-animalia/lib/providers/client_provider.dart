import 'dart:async';

import '../models/api_response.dart';
import '../models/client.dart';
import '../models/inactive_client.dart';
import '../services/auth/auth_service.dart';
import '../services/client/client_service.dart';
import '../utils/debug_logger.dart';
import 'base_provider.dart';

/// Provider for managing client data with salon-specific isolation
class Client<PERSON>rovider extends BaseProvider {
  List<Client> _clients = [];
  List<Client> _filteredClients = [];
  List<InactiveClient> _inactiveClients = [];
  List<InactiveClient> _filteredInactiveClients = [];
  String _searchQuery = '';
  String? _currentSalonId;
  bool _isLoadingInactiveClients = false;

  // Pagination state
  int _currentOffset = 0;
  final int _pageSize = 100;
  bool _hasMoreClients = true;
  bool _isLoadingMore = false;
  Timer? _searchDebounce;
  bool _isSearching = false;
  int _totalClientCount = 0;

  // Getters
  List<Client> get clients => _clients;
  List<Client> get filteredClients => _filteredClients;
  List<InactiveClient> get inactiveClients => _inactiveClients;
  List<InactiveClient> get filteredInactiveClients => _filteredInactiveClients;
  String get searchQuery => _searchQuery;
  String? get currentSalonId => _currentSalonId;
  bool get hasClients => _clients.isNotEmpty;
  bool get hasInactiveClients => _inactiveClients.isNotEmpty;
  bool get isLoadingInactiveClients => _isLoadingInactiveClients;
  bool get hasMoreClients => _hasMoreClients;
  bool get isLoadingMore => _isLoadingMore;
  bool get isSearching => _isSearching;
  int get totalClientCount => _totalClientCount;

  @override
  Future<void> initialize() async {
    await executeVoidAsync(
      () async {
        await _loadCurrentSalonId();
        await _loadClients();
        setInitialized(true);
      },
      errorMessage: 'Failed to initialize client provider',
    );
  }

  @override
  Future<void> refresh() async {
    await executeVoidAsync(
      () async {
        await _loadCurrentSalonId();
        await _loadClients();
        DebugLogger.logVerbose('✅ ClientProvider: Refresh completed');
      },
      errorMessage: 'Failed to refresh client data',
    );
  }

  @override
  void clear() {
    DebugLogger.logVerbose('🧹 ClientProvider: Clearing all client data...');
    _clients.clear();
    _filteredClients.clear();
    _inactiveClients.clear();
    _filteredInactiveClients.clear();
    _searchQuery = '';
    _currentSalonId = null;
    _isLoadingInactiveClients = false;
    _currentOffset = 0;
    _hasMoreClients = true;
    _isLoadingMore = false;
    _searchDebounce?.cancel();
    _searchDebounce = null;
    _isSearching = false;
    _totalClientCount = 0;
    super.clear();
    DebugLogger.logVerbose('✅ ClientProvider: All client data cleared');
  }

  /// Clear client data for salon switching (with comprehensive logging)
  Future<void> clearForSalonSwitch(String newSalonId) async {

    // Clear all cached data
    _clients.clear();
    _filteredClients.clear();
    _searchQuery = '';
    _currentSalonId = newSalonId;
    _currentOffset = 0;
    _hasMoreClients = true;
    _isLoadingMore = false;
    _searchDebounce?.cancel();
    _searchDebounce = null;
    _isSearching = false;
    _totalClientCount = 0;

    // Notify listeners immediately after clearing data
    notifyListeners();


    // Load fresh data for new salon
    await _loadClients();

  }

  /// Load current salon ID
  Future<void> _loadCurrentSalonId() async {
    _currentSalonId = await AuthService.getCurrentSalonId();
  }

  /// Load clients for current salon
  Future<void> _loadClients() async {
    if (_currentSalonId == null) {
      return;
    }

    _currentOffset = 0;
    _hasMoreClients = true;

    // Fetch total count first (e.g., 4653)
    final countResponse = await ClientService.getClientsCount();
    if (countResponse.success && countResponse.data != null) {
      _totalClientCount = countResponse.data!;
      DebugLogger.logVerbose('📊 ClientProvider: Total clients in salon: $_totalClientCount');
    } else {
      _totalClientCount = 0;
    }

    // Load only first 100 clients for display
    final response = await ClientService.getClients(
      limit: _pageSize,
      offset: 0,
    );

    if (response.success && response.data != null) {
      _clients = response.data!;
      _filteredClients = response.data!;

      // Check if there might be more clients
      _hasMoreClients = response.data!.length >= _pageSize;
      _currentOffset = response.data!.length;

      DebugLogger.logVerbose('✅ ClientProvider: Loaded ${response.data!.length} clients. Total: $_totalClientCount');
    } else {
      _clients = [];
      _filteredClients = [];
      _totalClientCount = 0;
      final errorMsg = response.error ?? 'Unknown error loading clients';
      setError('Failed to load clients: $errorMsg');
    }

    // Notify listeners that the data has changed
    notifyListeners();
  }

  /// Search clients by query
  void searchClients(String query) {
    _searchQuery = query;
    
    // Cancel previous debounce timer
    _searchDebounce?.cancel();

    if (query.isEmpty) {
      // If search is cleared, show cached clients and restore total count
      _filteredClients = List.from(_clients);
      _isSearching = false;

      // Restore the total count from database
      _restoreTotalCount();

      notifyListeners();
      return;
    }

    // First, do local filtering for immediate feedback
    _filteredClients = _clients.where((client) {
      final searchLower = query.toLowerCase();
      final phoneNormalized = client.phone.replaceAll(RegExp(r'[\s\-+]'), ''); // Elimină spații, - și +
      final queryNormalized = query.replaceAll(RegExp(r'[\s\-+]'), ''); // Elimină spații, - și +

      // Normalize secondary phone for searching
      final secondaryPhoneNormalized = client.secondaryPhone?.replaceAll(RegExp(r'[\s\-+]'), '') ?? '';

      final petNameMatch = client.petNames.any((petName) => petName.toLowerCase().contains(searchLower));
      final backendPetMatch =
          client.matchingPetNames.any((petName) => petName.toLowerCase().contains(searchLower));

      return client.name.toLowerCase().contains(searchLower) ||
             client.phone.toLowerCase().contains(searchLower) ||
             phoneNormalized.contains(queryNormalized) || // Caută în numărul normalizat
             (client.secondaryPhone != null && client.secondaryPhone!.toLowerCase().contains(searchLower)) ||
             secondaryPhoneNormalized.contains(queryNormalized) || // Caută și în numărul secundar
             client.email.toLowerCase().contains(searchLower) ||
             petNameMatch ||
             backendPetMatch;
    }).toList();

    notifyListeners();

    // Debounce server-side search
    _searchDebounce = Timer(const Duration(milliseconds: 500), () {
      _performServerSearch(query);
    });
  }

  /// Restore total count from database when search is cleared
  Future<void> _restoreTotalCount() async {
    final countResponse = await ClientService.getClientsCount();
    if (countResponse.success && countResponse.data != null) {
      _totalClientCount = countResponse.data!;
      DebugLogger.logVerbose('🔄 ClientProvider: Restored total count: $_totalClientCount');
      notifyListeners();
    }
  }

  /// Perform server-side search after debounce
  Future<void> _performServerSearch(String query) async {
    if (query.isEmpty) return;

    _isSearching = true;
    notifyListeners();

    try {
      // Fetch count for search results (e.g., 5 matching "Maria")
      final countResponse = await ClientService.getClientsCount(search: query);
      if (countResponse.success && countResponse.data != null) {
        _totalClientCount = countResponse.data!;
        DebugLogger.logVerbose('🔍 ClientProvider: Search "$query" found $_totalClientCount clients');
      }

      // Fetch search results
      final response = await ClientService.getClients(
        search: query,
        limit: 1000, // Return more results for search
      );

      if (response.success && response.data != null) {
        _filteredClients = response.data!;
      }
    } catch (e) {
      DebugLogger.logError('❌ ClientProvider: Server search error: $e');
    } finally {
      _isSearching = false;
      notifyListeners();
    }
  }

  /// Load more clients (pagination)
  Future<void> loadMoreClients() async {
    if (_isLoadingMore || !_hasMoreClients || _searchQuery.isNotEmpty) {
      return;
    }
    
    _isLoadingMore = true;
    notifyListeners();

    try {
      final response = await ClientService.getClients(
        limit: _pageSize,
        offset: _currentOffset,
      );

      if (response.success && response.data != null) {
        final newClients = response.data!;

        if (newClients.isEmpty || newClients.length < _pageSize) {
          _hasMoreClients = false;
        }

        _clients.addAll(newClients);
        _filteredClients = List.from(_clients);
        _currentOffset += newClients.length;

        // totalClientCount stays the same (e.g., 4653)
        // We're just loading more clients into memory for scrolling
        DebugLogger.logVerbose('✅ ClientProvider: Loaded ${newClients.length} more clients. Loaded: ${_clients.length}/$_totalClientCount');
      } else {
        DebugLogger.logError('❌ ClientProvider: Failed to load more clients: ${response.error}');
      }
    } catch (e) {
      DebugLogger.logError('❌ ClientProvider: Error loading more clients: $e');
    } finally {
      _isLoadingMore = false;
      notifyListeners();
    }
  }

  /// Add a new client
  Future<bool> addClient(Client client) async {

    final response = await ClientService.createClient(client);
    
    if (response.success && response.data != null) {
      _clients.add(response.data!);
      searchClients(_searchQuery); // Refresh filtered list
      return true;
    } else {
      final errorMsg = response.error ?? 'Failed to add client';
      setError(errorMsg);
      return false;
    }
  }

  /// Update an existing client
  Future<bool> updateClient(Client client) async {

    final response = await ClientService.updateClient(client.id, client);
    
    if (response.success && response.data != null) {
      final index = _clients.indexWhere((c) => c.id == client.id);
      if (index != -1) {
        _clients[index] = response.data!;
        searchClients(_searchQuery); // Refresh filtered list
        return true;
      }
    }
    
    final errorMsg = response.error ?? 'Failed to update client';
    setError(errorMsg);
    return false;
  }

  /// Delete a client
  Future<bool> deleteClient(String clientId) async {

    final response = await ClientService.deleteClient(clientId);

    if (response.success) {
      _clients.removeWhere((c) => c.id == clientId);
      searchClients(_searchQuery); // Refresh filtered list
      return true;
    } else {
      final errorMsg = response.error ?? 'Failed to delete client';
      setError(errorMsg);
      return false;
    }
  }

  /// Delete multiple clients
  Future<Map<String, dynamic>> deleteMultipleClients(List<String> clientIds) async {
    final response = await ClientService.deleteMultipleClients(clientIds);

    if (response.success && response.data != null) {
      final result = response.data!;
      final deletedCount = result['deletedCount'] as int? ?? 0;

      // Remove successfully deleted clients from local list
      if (deletedCount > 0) {
        _clients.removeWhere((c) => clientIds.contains(c.id));
        searchClients(_searchQuery); // Refresh filtered list
      }

      return result;
    } else {
      final errorMsg = response.error ?? 'Failed to delete clients';
      setError(errorMsg);
      return {
        'deletedCount': 0,
        'failedCount': clientIds.length,
        'errors': [errorMsg],
      };
    }
  }

  /// Add multiple clients in batch
  Future<ApiResponse<List<Client>>> addMultipleClients(List<Client> clients) async {
    final response = await ClientService.createMultipleClients(clients);
    
    if (response.success && response.data != null) {
      _clients.addAll(response.data!);
      searchClients(_searchQuery); // Refresh filtered list
    }
    
    return response;
  }

  /// Get client by ID
  Client? getClientById(String clientId) {
    try {
      return _clients.firstWhere((client) => client.id == clientId);
    } catch (e) {
      return null;
    }
  }

  /// Load inactive clients (clients who haven't had appointments in 30+ days)
  Future<void> loadInactiveClients({int daysSinceLastAppointment = 30}) async {
    _isLoadingInactiveClients = true;
    notifyListeners();

    try {
      DebugLogger.logVerbose('🔄 ClientProvider: Loading inactive clients (${daysSinceLastAppointment}+ days)...');

      // Use the new efficient backend endpoint that retrieves all data in one query
      final response = await ClientService.getInactiveClients(daysSinceLastAppointment: daysSinceLastAppointment);

      if (response.success && response.data != null) {
        _inactiveClients = response.data!;
        _filteredInactiveClients = List.from(_inactiveClients);
        DebugLogger.logVerbose('✅ ClientProvider: Loaded ${_inactiveClients.length} inactive clients from backend');
      } else {
        DebugLogger.logError('❌ ClientProvider: Failed to load inactive clients: ${response.error}');
        setError('Failed to load inactive clients: ${response.error}');
      }
    } catch (e) {
      DebugLogger.logError('❌ ClientProvider: Error loading inactive clients: $e');
      setError('Failed to load inactive clients: $e');
    } finally {
      _isLoadingInactiveClients = false;
      notifyListeners();
    }
  }

  /// Search inactive clients
  void searchInactiveClients(String query) {
    _searchQuery = query;

    if (query.isEmpty) {
      _filteredInactiveClients = List.from(_inactiveClients);
    } else {
      final lowerQuery = query.toLowerCase();
      _filteredInactiveClients = _inactiveClients.where((inactiveClient) {
        final client = inactiveClient.client;
        return client.name.toLowerCase().contains(lowerQuery) ||
               client.phone.toLowerCase().contains(lowerQuery) ||
               client.email.toLowerCase().contains(lowerQuery) ||
               client.petNames.any((petName) => petName.toLowerCase().contains(lowerQuery));
      }).toList();
    }

    notifyListeners();
  }

  /// Get inactive clients count by priority level
  Map<int, int> getInactiveClientsByPriority() {
    final priorityCounts = <int, int>{};

    for (final inactiveClient in _inactiveClients) {
      final priority = inactiveClient.priorityLevel;
      priorityCounts[priority] = (priorityCounts[priority] ?? 0) + 1;
    }

    return priorityCounts;
  }

  /// Clear inactive clients data
  void clearInactiveClients() {
    _inactiveClients.clear();
    _filteredInactiveClients.clear();
    _isLoadingInactiveClients = false;
    notifyListeners();
  }

  @override
  void dispose() {
    _searchDebounce?.cancel();
    super.dispose();
  }
}
